package com.stepup.springrobot;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.Transactional;
import net.devh.boot.grpc.server.security.authentication.GrpcAuthenticationReader;
import net.devh.boot.grpc.server.security.authentication.CompositeGrpcAuthenticationReader;
import java.util.Collections;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.TimeZone;

@Slf4j
@SpringBootApplication(
		exclude = { RedisRepositoriesAutoConfiguration.class }
)
@Configuration
@EnableScheduling
public class SpringRobotApplication {
	public static void main(String[] args) {
		SpringApplication.run(SpringRobotApplication.class, args);
	}

	@PostConstruct
	@Transactional(rollbackFor = Exception.class)
	public void init() {
		log.info("=========================Date before in UTC: =========================" + new Date());
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Ho_Chi_Minh"));
		log.info("=========================Date after in UTC: =========================" + new Date());
	}

	@Bean
	public GrpcAuthenticationReader grpcAuthenticationReader() {
		return new CompositeGrpcAuthenticationReader(Collections.emptyList());
	}
}
