package com.stepup.springrobot.util;

import io.github.jaredm<PERSON>bson.OpusDecoder;
import io.github.jaredmdobson.OpusException;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * Opus Audio Decoder utility using Concentus pure Java library
 * 
 * This class provides functionality to decode Opus-encoded audio data
 * using the Concentus library, which is a pure Java implementation of Opus codec.
 * No native library installation required!
 */
@Slf4j
public class ConcentusOpusDecoder {
    
    // Audio parameters matching ESP32 configuration
    // From ESP32: MIC_SAMPLE_RATE = 16000, MIC_STREAMING_OPUS_FRAME_MS = 40
    private static final int DEFAULT_SAMPLE_RATE = 16000; // Match ESP32 sample rate
    private static final int DEFAULT_CHANNELS = 1;
    private static final int MAX_FRAME_SIZE = 5760; // Maximum frame size for any sample rate
    private static final int FRAME_SIZE_40MS_16K = 640; // 40ms frame at 16kHz (16000 * 40 / 1000)
    private static final int BUFFER_SIZE_MULTIPLIER = 8; // Larger safety multiplier for buffer size
    
    private OpusDecoder decoder;
    private final int sampleRate;
    private final int channels;
    private boolean initialized = false;
    
    /**
     * Default constructor with standard audio parameters
     */
    public ConcentusOpusDecoder() {
        this(DEFAULT_SAMPLE_RATE, DEFAULT_CHANNELS);
    }
    
    /**
     * Constructor with custom audio parameters
     * 
     * @param sampleRate Sample rate in Hz (8000, 12000, 16000, 24000, 48000)
     * @param channels Number of channels (1 or 2)
     */
    public ConcentusOpusDecoder(int sampleRate, int channels) {
        this.sampleRate = sampleRate;
        this.channels = channels;
        initialize();
    }
    
    /**
     * Initialize the Concentus Opus decoder
     */
    private void initialize() {
        try {
            log.info("Initializing Concentus Opus decoder with sample rate: {} Hz, channels: {}", sampleRate, channels);
            
            decoder = new OpusDecoder(sampleRate, channels);
            initialized = true;
            
            log.info("Concentus Opus decoder initialized successfully (Pure Java, no native library required)");
            
        } catch (OpusException e) {
            log.error("Failed to initialize Concentus Opus decoder", e);
            throw new RuntimeException("Failed to initialize Concentus Opus decoder", e);
        } catch (Exception e) {
            log.error("Unexpected error during Concentus Opus decoder initialization", e);
            throw new RuntimeException("Unexpected error during Concentus Opus decoder initialization", e);
        }
    }
    
    /**
     * Decode Opus audio data to PCM
     * 
     * @param opusData The Opus-encoded audio data
     * @return Decoded PCM audio data as byte array (16-bit little-endian)
     */
    public byte[] decode(byte[] opusData) {
        if (!initialized) {
            throw new IllegalStateException("Concentus Opus decoder is not initialized");
        }
        
        if (opusData == null || opusData.length == 0) {
            log.warn("Empty or null Opus data provided");
            return new byte[0];
        }
        
        try {
            // Calculate appropriate frame size based on ESP32 configuration
            // ESP32 uses 40ms frames at 16kHz, but we need to handle variable sizes
            int expectedFrameSize = calculateExpectedFrameSize();
            
            // Prepare very large output buffer for PCM data (short array) with large safety margin
            // Use much larger buffer to handle any possible Opus frame size
            int bufferSize = MAX_FRAME_SIZE * channels * BUFFER_SIZE_MULTIPLIER;
            short[] pcmBuffer = new short[bufferSize];
            
            log.debug("Decoding Opus data: {} bytes, expected frame size: {}, buffer size: {} shorts", 
                     opusData.length, expectedFrameSize, bufferSize);
            
            // Decode the Opus data using Concentus with maximum possible frame size
            int decodedSamples = decoder.decode(
                opusData,           // input Opus data
                0,                  // input offset
                opusData.length,    // input length
                pcmBuffer,          // output PCM buffer
                0,                  // output offset
                MAX_FRAME_SIZE,     // use maximum possible frame size
                false               // decode FEC (Forward Error Correction)
            );
            
            if (decodedSamples <= 0) {
                log.warn("No samples decoded from Opus data. Decoded samples: {}", decodedSamples);
                return new byte[0];
            }
            
            log.debug("Decoded {} samples from {} bytes of Opus data using Concentus", 
                     decodedSamples, opusData.length);
            
            // Convert short array to byte array (16-bit little-endian)
            return convertShortArrayToByteArray(pcmBuffer, decodedSamples * channels);
            
        } catch (OpusException e) {
            if (e.getMessage() != null && e.getMessage().contains("buffer too small")) {
                log.warn("Opus decode buffer too small, trying with auto buffer adjustment. Input size: {} bytes", opusData.length);
                return decodeWithAutoBuffer(opusData);
            } else {
                log.error("Concentus Opus decoding failed: {}", e.getMessage(), e);
                return new byte[0];
            }
        } catch (Exception e) {
            log.error("Unexpected error during Concentus Opus decoding", e);
            return new byte[0];
        }
    }
    
    /**
     * Retry decoding with a much larger buffer when initial decode fails
     * 
     * @param opusData The Opus-encoded audio data
     * @return Decoded PCM audio data as byte array
     */
    private byte[] retryDecodeWithLargerBuffer(byte[] opusData) {
        try {
            // Use maximum possible buffer size for retry
            int largeBufferSize = MAX_FRAME_SIZE * channels * BUFFER_SIZE_MULTIPLIER * 2; // Double the safety margin
            short[] largeBuffer = new short[largeBufferSize];
            
            log.debug("Retrying Opus decode with larger buffer: {} shorts", largeBufferSize);
            
            // Try decoding with maximum possible frame size
            int decodedSamples = decoder.decode(
                opusData,
                0,
                opusData.length,
                largeBuffer,
                0,
                MAX_FRAME_SIZE,
                false
            );
            
            if (decodedSamples <= 0) {
                log.warn("Retry decode failed: No samples decoded");
                return new byte[0];
            }
            
            log.debug("Retry decode successful: {} samples", decodedSamples);
            return convertShortArrayToByteArray(largeBuffer, decodedSamples * channels);
            
        } catch (Exception e) {
            log.error("Retry decode with larger buffer failed", e);
            return new byte[0];
        }
    }
    
    /**
     * Calculate expected frame size based on ESP32 configuration
     * ESP32 uses 40ms frames at 16kHz sample rate
     * 
     * @return Expected frame size in samples
     */
    private int calculateExpectedFrameSize() {
        if (sampleRate == 16000) {
            // ESP32 configuration: 40ms frame at 16kHz
            return FRAME_SIZE_40MS_16K; // 640 samples
        } else {
            // Fallback for other sample rates: use 20ms frame
            int frameSize20ms = sampleRate * 20 / 1000;
            return Math.min(frameSize20ms, MAX_FRAME_SIZE);
        }
    }
    
    /**
     * Calculate appropriate frame size based on sample rate
     * Opus supports frame sizes from 2.5ms to 60ms
     * 
     * @return Frame size in samples
     */
    private int calculateFrameSize() {
        return calculateExpectedFrameSize();
    }
    
    /**
     * Convert short array to byte array (16-bit little-endian)
     * 
     * @param shortArray Input short array
     * @param length Number of shorts to convert
     * @return Byte array representation
     */
    private byte[] convertShortArrayToByteArray(short[] shortArray, int length) {
        ByteBuffer byteBuffer = ByteBuffer.allocate(length * 2);
        byteBuffer.order(ByteOrder.LITTLE_ENDIAN);
        
        for (int i = 0; i < length; i++) {
            byteBuffer.putShort(shortArray[i]);
        }
        
        return byteBuffer.array();
    }
    
    /**
     * Get audio format information
     * 
     * @return String describing the audio format
     */
    public String getAudioFormatInfo() {
        return String.format("Sample Rate: %d Hz, Channels: %d, Format: 16-bit PCM (Concentus Pure Java)", 
                           sampleRate, channels);
    }
    
    /**
     * Check if decoder is initialized and ready to use
     * 
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * Get decoder information
     * 
     * @return Information about the decoder
     */
    public String getDecoderInfo() {
        return String.format("Concentus Opus Decoder - Pure Java Implementation (Sample Rate: %d Hz, Channels: %d, Expected Frame: %d samples)", 
                           sampleRate, channels, calculateExpectedFrameSize());
    }
    
    /**
     * Decode with automatic buffer size adjustment for ESP32 compatibility
     * This method tries different buffer sizes if the initial decode fails
     * 
     * @param opusData The Opus-encoded audio data
     * @return Decoded PCM audio data as byte array
     */
    public byte[] decodeWithAutoBuffer(byte[] opusData) {
        if (!initialized) {
            throw new IllegalStateException("Concentus Opus decoder is not initialized");
        }
        
        if (opusData == null || opusData.length == 0) {
            log.warn("Empty or null Opus data provided for auto buffer decode");
            return new byte[0];
        }
        
        // Try with progressively larger buffers
        int[] bufferSizes = {
            MAX_FRAME_SIZE * channels * 2,      // Small buffer first
            MAX_FRAME_SIZE * channels * 4,      // Medium buffer
            MAX_FRAME_SIZE * channels * 8,      // Large buffer
            MAX_FRAME_SIZE * channels * 16      // Very large buffer
        };
        
        for (int bufferSize : bufferSizes) {
            try {
                short[] pcmBuffer = new short[bufferSize];
                
                log.debug("Trying auto decode with buffer size: {} shorts", bufferSize);
                
                int decodedSamples = decoder.decode(
                    opusData,
                    0,
                    opusData.length,
                    pcmBuffer,
                    0,
                    MAX_FRAME_SIZE,
                    false
                );
                
                if (decodedSamples > 0) {
                    log.debug("Auto decode successful with buffer size: {}, decoded samples: {}", 
                             bufferSize, decodedSamples);
                    return convertShortArrayToByteArray(pcmBuffer, decodedSamples * channels);
                }
                
            } catch (Exception e) {
                log.debug("Auto decode failed with buffer size: {}, trying next size", bufferSize);
                continue;
            }
        }
        
        log.error("Auto decode failed with all buffer sizes for {} bytes of Opus data", opusData.length);
        return new byte[0];
    }
    
    /**
     * Clean up resources (Concentus doesn't require explicit cleanup, but good practice)
     */
    public void destroy() {
        if (initialized) {
            try {
                decoder = null;
                initialized = false;
                log.info("Concentus Opus decoder destroyed successfully");
            } catch (Exception e) {
                log.error("Error destroying Concentus Opus decoder", e);
            }
        }
    }
}
