package com.stepup.springrobot.security;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.common.RandomString;
import com.stepup.springrobot.exception.business.user.InvalidTokenException;
import com.stepup.springrobot.exception.business.user.TokenExpiredException;
import com.stepup.springrobot.model.auth.UserIssueToken;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.repository.auth.UserIssueTokenRepository;
import com.stepup.springrobot.repository.auth.UserRepository;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@Slf4j
@Component
public class JwtService {
    public static final String SECRET = "04K1ctpz3cTca+pNNQYQXZX6bQLJEbckmZeU1NtbEPc=";

    @Value("${access_token_duration_in_hours}")
    private Long accessTokenDurationInHour;

    @Value("${max_sign_in_device}")
    private Long maxSignInDevice;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserIssueTokenRepository userIssueTokenRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedissonClient redissonClient;

    public String extractPhone(String token) throws JsonProcessingException {
        final Claims claims = extractAllClaims(token);
        Map<String, Object> claimsMap = objectMapper.readValue(objectMapper.writeValueAsString(claims), Map.class);

        return claimsMap.get("phone").toString();
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        log.info("Claims: {}", claims);
        return claimsResolver.apply(claims);
    }

    public Claims extractAllClaims(String token) {
        try {
            return Jwts
                    .parserBuilder()
                    .setSigningKey(getSignKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {
            throw new TokenExpiredException();
        } catch (MalformedJwtException e) {
            throw new InvalidTokenException();
        }
    }

    private boolean checkTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    public void validateToken(String token) throws JsonProcessingException {
        checkTokenExpired(token);
        boolean isValidJti = checkValidJti(token);
        if (!isValidJti) {
            throw new TokenExpiredException();
        }
    }

    private boolean checkValidJti(String token) throws JsonProcessingException {
        final Claims claims = extractAllClaims(token);
        Map<String, Object> claimsMap = objectMapper.readValue(objectMapper.writeValueAsString(claims), new TypeReference<>() {
        });
        String jti = claimsMap.get("jti").toString();
        if (StringUtils.isEmpty(jti)) {
            log.info("jti is empty");
            return false;
        }

        RMapCache<String, String> users = redissonClient.getMapCache(CodeDefine.REDIS_KEY_USERS_ISSUE_TOKEN);
        if (!users.containsKey(jti)) {
            UserIssueToken userIssueToken = userIssueTokenRepository.findByToken(jti);
            if (userIssueToken == null) {
                return false;
            }

            User user = userRepository.findById(userIssueToken.getUserId()).orElse(null);
            if (user == null) {
                return false;
            }

            log.info("=====load jti token from db====={}", user);

            // cần được update lại redis
            users.put(jti, objectMapper.writeValueAsString(user), 1, TimeUnit.DAYS);
            return true;
        } else {
            try {
                User userDataDTO = objectMapper.readValue(users.get(jti), new TypeReference<>() {
                });
                log.info("=====load jti token from redisson======{}", userDataDTO);
            } catch (Exception e) {
                log.error("Error when load jti token from redisson", e);
            }

            return true;
        }
    }

    public String generateToken(User user, String deviceId) {
        return generateToken(user, deviceId, null);
    }

    public String generateToken(User user, String deviceId, String deviceToken) {
        String jti = genJtiToken(user.getId());
        Map<String, Object> claims = new HashMap<>();
        claims.put("jti", jti);
        claims.put("user_id", user.getId());
        claims.put("phone", user.getPhone());
        claims.put("authorities", user.getRoles());

        saveUserIssueTokenAndCheckMaxDeviceUse(user, jti, deviceId, deviceToken);
        return createToken(claims, user.getPhone(), jti);
    }

    private String createToken(Map<String, Object> claims, String phone, String jti) {
        return Jwts.builder()
                .setSubject(phone)
                .setClaims(claims)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + 1000 * 60 * 60 * accessTokenDurationInHour))
                .signWith(getSignKey(), SignatureAlgorithm.HS256)
                .setId(jti)
                .compact();
    }

    private void saveUserIssueTokenAndCheckMaxDeviceUse(User user, String jti, String deviceId) {
        saveUserIssueTokenAndCheckMaxDeviceUse(user, jti, deviceId, null);
    }

    private void saveUserIssueTokenAndCheckMaxDeviceUse(User user, String jti, String deviceId, String deviceToken) {
        // Validate deviceId
        if (StringUtils.isEmpty(deviceId)) {
            deviceId = "unknown_device_" + System.currentTimeMillis();
        }

        List<UserIssueToken> userIssueTokens = userIssueTokenRepository.findByUserIdOrderByIdAsc(user.getId());
        // Sort by updatedAt descending (newest first) to remove oldest when needed
        userIssueTokens.sort(Comparator.comparing(UserIssueToken::getUpdatedAt).reversed());

        RMapCache<String, String> userTokenMapCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_USERS_ISSUE_TOKEN);

        // Check if device already exists
        String finalDeviceId = deviceId;
        UserIssueToken existingToken = userIssueTokens.stream()
                .filter(x -> Objects.equals(x.getDeviceId(), finalDeviceId))
                .findFirst()
                .orElse(null);

        if (existingToken != null) {
            // Update existing device token
            userTokenMapCache.remove(existingToken.getToken());
            existingToken.setToken(jti);
            if (!StringUtils.isEmpty(deviceToken)) {
                existingToken.setDeviceToken(deviceToken);
            }
            userIssueTokenRepository.save(existingToken);
        } else {
            // Check device limit for new device
            if (userIssueTokens.size() >= maxSignInDevice) {
                // Remove oldest device (last in sorted list)
                UserIssueToken oldestToken = userIssueTokens.get(userIssueTokens.size() - 1);
                userIssueTokenRepository.deleteById(oldestToken.getId());
                userTokenMapCache.remove(oldestToken.getToken());
            }

            // Create new device token
            UserIssueToken newToken = UserIssueToken.builder()
                    .token(jti)
                    .userId(user.getId())
                    .deviceId(deviceId)
                    .deviceToken(deviceToken)
                    .build();
            userIssueTokenRepository.save(newToken);
        }

        // Cache user data
        try {
            userTokenMapCache.put(jti, objectMapper.writeValueAsString(user), 1, TimeUnit.DAYS);
        } catch (JsonProcessingException e) {
            log.error("Error when save jti token to redisson", e);
        }
    }

    private Key getSignKey() {
        byte[] keyBytes = SECRET.getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * Sinh ra token cho user, cần check xem token sinh ra có duy nhất ko? Nếu trùng thì cần gen lại
     */
    private String genJtiToken(String userId) {
        int lenStringRandom = 21;
        String jti = new RandomString(lenStringRandom).nextString() + "_" + userId;
        while (userIssueTokenRepository.findByToken(jti) != null) {
            jti = new RandomString(lenStringRandom).nextString() + "_" + userId;
        }

        return jti;
    }
}
