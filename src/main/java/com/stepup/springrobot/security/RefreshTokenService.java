package com.stepup.springrobot.security;

import com.stepup.springrobot.dto.auth.LoginResDTO;
import com.stepup.springrobot.exception.business.user.RefreshTokenExpiredException;
import com.stepup.springrobot.model.auth.RefreshToken;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.repository.auth.RefreshTokenRepository;
import com.stepup.springrobot.repository.auth.UserRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class RefreshTokenService {
    @Value("${refresh_token_duration_in_days}")
    private Long refreshTokenDurationInDays;

    private final RefreshTokenRepository refreshTokenRepository;

    private final UserRepository userRepository;

    private final JwtService jwtService;

    public RefreshTokenService(RefreshTokenRepository refreshTokenRepository, UserRepository userRepository, JwtService jwtService) {
        this.refreshTokenRepository = refreshTokenRepository;
        this.userRepository = userRepository;
        this.jwtService = jwtService;
    }

    public RefreshToken handleCreateRefreshToken(String userId, String deviceId) {
        return createRefreshToken(userId, deviceId);
    }

    public RefreshToken createRefreshToken(String userId, String deviceId) {
        // Validate deviceId
        if (StringUtils.isEmpty(deviceId)) {
            deviceId = "unknown_device_" + System.currentTimeMillis();
        }

        // Find existing refresh token for this user and device
        RefreshToken refreshToken = refreshTokenRepository.findByUserIdAndDeviceId(userId, deviceId);

        // expire after configured days
        Date expiryDate = new Date(System.currentTimeMillis() + refreshTokenDurationInDays * 24 * 60 * 60 * 1000);
        String newRefreshToken = UUID.randomUUID() + "-" + userId + "-" + deviceId;

        if (refreshToken != null) {
            // Update existing refresh token for this device
            refreshToken.setToken(newRefreshToken);
            refreshToken.setExpiryDate(expiryDate);
        } else {
            // Check device limit - similar to access token logic
            List<RefreshToken> userRefreshTokens = refreshTokenRepository.findByUserId(userId);
            if (userRefreshTokens.size() >= 3) { // maxSignInDevice = 3
                // Remove oldest refresh token (by updatedAt)
                RefreshToken oldestToken = userRefreshTokens.stream()
                        .min((t1, t2) -> t1.getUpdatedAt().compareTo(t2.getUpdatedAt()))
                        .orElse(null);
                if (oldestToken != null) {
                    refreshTokenRepository.delete(oldestToken);
                }
            }

            // Create new refresh token
            refreshToken = RefreshToken.builder()
                    .userId(userId)
                    .deviceId(deviceId)
                    .token(newRefreshToken)
                    .expiryDate(expiryDate)
                    .build();
        }

        return refreshTokenRepository.save(refreshToken);
    }

    public Optional<RefreshToken> findRefreshTokenByToken(String token) {
        return refreshTokenRepository.findByToken(token);
    }

    public void verifyExpiration(RefreshToken token) {
        if (token.getExpiryDate().before(new Date())) {
            refreshTokenRepository.delete(token);
            throw new RefreshTokenExpiredException();
        }
    }

    public LoginResDTO handleRefreshToken(String token, String deviceId) {
        if (StringUtils.isEmpty(token)) {
            throw new RefreshTokenExpiredException();
        }

        Optional<RefreshToken> oRefreshToken = findRefreshTokenByToken(token);
        if (oRefreshToken.isEmpty()) {
            throw new RefreshTokenExpiredException();
        }

        RefreshToken refreshToken = oRefreshToken.get();
        verifyExpiration(refreshToken);

        User user = userRepository.findById(refreshToken.getUserId()).orElseThrow(RefreshTokenExpiredException::new);
        return LoginResDTO.builder()
                .accessToken(jwtService.generateToken(user, deviceId))
                .refreshToken(createRefreshToken(user.getId(), deviceId).getToken())
                .build();
    }
}
