package com.stepup.springrobot.grpc;

import com.stepup.springrobot.dto.grpc.TranscriptionResponseDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import reactor.core.scheduler.Schedulers;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class AudioStreamWebSocketHandler extends AbstractWebSocketHandler {

    private static final Logger logger = LoggerFactory.getLogger(AudioStreamWebSocketHandler.class);

    private final ObjectMapper objectMapper;

    @Value("${asr_grpc_uri_vi}")
    private String grpcUri;

    private final Map<WebSocketSession, Sinks.Many<byte[]>> sessionAudioSinks = new ConcurrentHashMap<>();
    private final Map<WebSocketSession, GrpcAsrClientService> sessionGrpcClients = new ConcurrentHashMap<>();

    public AudioStreamWebSocketHandler(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        logger.info("WebSocket connection established: Session ID - {}", session.getId());

        Sinks.Many<byte[]> audioSink = Sinks.many().unicast().onBackpressureBuffer();
        sessionAudioSinks.put(session, audioSink);

        GrpcAsrClientService grpcClient = new GrpcAsrClientService(grpcUri);
        sessionGrpcClients.put(session, grpcClient);

        Flux<byte[]> audioFluxFromClient = audioSink.asFlux();

        grpcClient.streamAudio(audioFluxFromClient)
                .publishOn(Schedulers.boundedElastic())
                .doOnSubscribe(
                        subscription -> logger.debug("Subscribed to gRPC stream for session {}", session.getId()))
                .subscribe(
                        transcriptionResponse -> sendTranscription(session, transcriptionResponse),
                        error -> {
                            logger.error("Error in gRPC stream for session {}: {}", session.getId(), error.getMessage(),
                                    error);
                            closeSessionWithError(session, "gRPC stream error: " + error.getMessage());
                            cleanupSessionResources(session);
                        },
                        () -> {
                            logger.info("gRPC stream completed for session {}. Closing WebSocket.", session.getId());
                            closeSession(session, CloseStatus.NORMAL);
                            cleanupSessionResources(session);
                        });
    }

    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) throws Exception {
       // logger.info("Received audio chunk of size: {} bytes for session {}", message.getPayloadLength(), session.getId());
        Sinks.Many<byte[]> audioSink = sessionAudioSinks.get(session);
        if (audioSink == null) {
            logger.warn(
                    "Received binary message but audioSink is null for session {}. Connection might be closing or closed.",
                    session.getId());
            return;
        }
        ByteBuffer payload = message.getPayload();
        byte[] audioChunk = new byte[payload.remaining()];
        payload.get(audioChunk);

        Sinks.EmitResult result = audioSink.tryEmitNext(audioChunk);
        if (result.isFailure()) {
            logger.warn("Failed to emit audio chunk to sink for session {}: {}", session.getId(), result);
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        logger.info("Received text message: {} for session {}", payload, session.getId());
        Sinks.Many<byte[]> audioSink = sessionAudioSinks.get(session);
        if ("EOS".equalsIgnoreCase(payload)) {
            if (audioSink != null) {
                logger.info("EOS received, completing audio sink for session {}.", session.getId());
                audioSink.tryEmitComplete();
            } else {
                logger.warn("EOS received but audioSink is null for session {}.", session.getId());
            }
        } else {
            logger.warn("Received unexpected text message: {} for session {}", payload, session.getId());
        }
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        logger.info("WebSocket connection closed: Session ID - {}, Status - {}", session.getId(), status);
        cleanupSessionResources(session);
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        logger.error("WebSocket transport error for session {}: {}", session.getId(), exception.getMessage(),
                exception);
        cleanupSessionResources(session);
    }

    private void cleanupSessionResources(WebSocketSession session) {
        Sinks.Many<byte[]> audioSink = sessionAudioSinks.remove(session);
        if (audioSink != null) {
            audioSink.tryEmitComplete();
            logger.debug("Cleaned up audioSink for session {}", session.getId());
        }

        GrpcAsrClientService grpcClient = sessionGrpcClients.remove(session);
        if (grpcClient != null) {
            grpcClient.close();
            logger.debug("Cleaned up GrpcAsrClientService for session {}", session.getId());
        }
    }

    private void sendTranscription(WebSocketSession session, TranscriptionResponseDTO transcription) {
        logger.debug("Receive transcription response for session {}: {}", session.getId(), transcription);
        if (session.isOpen()) {
            try {
                String jsonResponse = objectMapper.writeValueAsString(transcription);
                logger.debug("Sending transcription to session {}: {}", session.getId(), jsonResponse);
                session.sendMessage(new TextMessage(jsonResponse));
            } catch (JsonProcessingException e) {
                logger.error("Error serializing transcription response for session {}: {}", session.getId(),
                        e.getMessage(), e);
            } catch (IOException e) {
                logger.error("IOException while sending message to session {}: {}", session.getId(), e.getMessage(), e);
            }
        } else {
            logger.warn("Attempted to send transcription to closed session {}.", session.getId());
        }
    }

    private void closeSessionWithError(WebSocketSession session, String reason) {
        try {
            if (session.isOpen()) {
                session.close(CloseStatus.SERVER_ERROR.withReason(reason));
            }
        } catch (IOException e) {
            logger.error("Error closing session {} with error: {}", session.getId(), e.getMessage(), e);
        }
    }

    private void closeSession(WebSocketSession session, CloseStatus status) {
        try {
            if (session.isOpen()) {
                session.close(status);
            }
        } catch (IOException e) {
            logger.error("Error closing session {}: {}", session.getId(), e.getMessage(), e);
        }
    }
}