package com.stepup.springrobot.grpc;

import com.stepup.springrobot.dto.grpc.TranscriptionResponseDTO;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.Metadata;
import io.grpc.stub.MetadataUtils;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import streaming_voice.StreamVoiceGrpc;
import streaming_voice.StreamingVoice;

import java.util.concurrent.TimeUnit;

@Slf4j
public class GrpcAsrClientService {
    private final String grpcUri;
    private ManagedChannel channel;
    private StreamVoiceGrpc.StreamVoiceStub asyncStub;

    // Metadata for gRPC
    private static final Metadata.Key<String> TOKEN_KEY = Metadata.Key.of("token", Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> SPEECH_TIMEOUT_KEY = Metadata.Key.of("speech_timeout",
            Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> SPEECH_MAX_KEY = Metadata.Key.of("speech_max",
            Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> SILENCE_TIMEOUT_KEY = Metadata.Key.of("silence_timeout",
            Metadata.ASCII_STRING_MARSHALLER);

    private static final String TOKEN_VALUE = "web_test_token";
    private static final String SPEECH_TIMEOUT_VALUE = "1";
    private static final String SPEECH_MAX_VALUE = "60";
    private static final String SILENCE_TIMEOUT_VALUE = "30";

    public GrpcAsrClientService(String grpcUri) {
        this.grpcUri = grpcUri;
        try {
            log.info("Initializing gRPC channel to ASR service at: {}", this.grpcUri);
            this.channel = ManagedChannelBuilder.forTarget(this.grpcUri)
                    .usePlaintext()
                    .build();

            Metadata headers = new Metadata();
            headers.put(TOKEN_KEY, TOKEN_VALUE);
            headers.put(SPEECH_TIMEOUT_KEY, SPEECH_TIMEOUT_VALUE);
            headers.put(SPEECH_MAX_KEY, SPEECH_MAX_VALUE);
            headers.put(SILENCE_TIMEOUT_KEY, SILENCE_TIMEOUT_VALUE);

            this.asyncStub = StreamVoiceGrpc.newStub(channel)
                    .withInterceptors(MetadataUtils.newAttachHeadersInterceptor(headers));

            log.info("gRPC channel and stub initialized successfully with headers.");
        } catch (Exception e) {
            log.error("Failed to initialize gRPC channel: {}", e.getMessage(), e);
            // Handle initialization failure, maybe prevent app startup or fallback
            throw new RuntimeException("Failed to initialize gRPC client for ASR service", e);
        }
    }

    public Flux<TranscriptionResponseDTO> streamAudio(Flux<byte[]> audioFlux) {
        Sinks.Many<TranscriptionResponseDTO> sink = Sinks.many().multicast().onBackpressureBuffer();

        StreamObserver<StreamingVoice.VoiceRequest> requestObserver = asyncStub
                .sendVoice(new StreamObserver<StreamingVoice.TextReply>() {
                    @Override
                    public void onNext(StreamingVoice.TextReply reply) {
                        if (reply.hasResult() && !reply.getResult().getHypothesesList().isEmpty()) {
                            String transcript = reply.getResult().getHypotheses(0).getTranscriptNormed();
                            boolean isFinal = reply.getResult().getFinal();
                            log.info("gRPC ASR Response - Final: {}, Transcript: {}", isFinal, transcript);
                            sink.tryEmitNext(new TranscriptionResponseDTO(transcript, isFinal));
                        } else {
                            log.info("Received TextReply without valid transcription result.");
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        log.error("gRPC stream error from ASR service: {}", t.getMessage(), t);
                        sink.tryEmitError(t);
                    }

                    @Override
                    public void onCompleted() {
                        log.info("gRPC stream completed by ASR service.");
                        sink.tryEmitComplete();
                    }
                });

        audioFlux
                .map(chunk -> StreamingVoice.VoiceRequest.newBuilder()
                        .setByteBuff(com.google.protobuf.ByteString.copyFrom(chunk))
                        .build())
                .doOnNext(request -> {
                   // log.info("Sending audio chunk to gRPC ASR service. Chunk size: {} bytes",request.getByteBuff().size());
                    requestObserver.onNext(request);
                })
                .doOnError(requestObserver::onError)
                .doOnComplete(requestObserver::onCompleted)
                .doOnCancel(() -> {
                    log.info("Audio flux cancelled, closing gRPC request stream.");
                    requestObserver.onCompleted(); // Signal server that client is done sending
                })
                .subscribe();

        return sink.asFlux();
    }

    public void close() {
        log.info("Shutting down gRPC channel.");
        if (channel != null && !channel.isShutdown()) {
            try {
                channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.error("Error while shutting down gRPC channel: {}", e.getMessage(), e);
                Thread.currentThread().interrupt();
            }
        }
        log.info("gRPC channel shut down.");
    }
}