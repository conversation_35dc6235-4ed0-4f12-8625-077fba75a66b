package com.stepup.springrobot.grpc;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    private final AudioStreamWebSocketHandler audioStreamWebSocketHandler;

    public WebSocketConfig(AudioStreamWebSocketHandler audioStreamWebSocketHandler) {
        this.audioStreamWebSocketHandler = audioStreamWebSocketHandler;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(audioStreamWebSocketHandler, "/ws/stream-audio")
                .setAllowedOrigins("*"); // Configure allowed origins as needed
    }
}