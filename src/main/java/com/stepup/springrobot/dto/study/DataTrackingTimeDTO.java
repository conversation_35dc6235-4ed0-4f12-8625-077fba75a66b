package com.stepup.springrobot.dto.study;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataTrackingTimeDTO {
    private Long botId;
    private Integer totalUsers;
    private Integer completedUsers;
    private String formattedTotalDuration;

    // Constructor for native query results
    public DataTrackingTimeDTO(Long botId, Long totalUsers, Long completedUsers, String formattedTotalDuration) {
        this.botId = botId;
        this.totalUsers = totalUsers != null ? totalUsers.intValue() : 0;
        this.completedUsers = completedUsers != null ? completedUsers.intValue() : 0;
        this.formattedTotalDuration = formattedTotalDuration;
    }
} 