package com.stepup.springrobot.dto.study;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CompletedLessonsResDTO {
    @JsonProperty("lessons")
    private List<StudyLessonDTO> studyLessonDTOS;

    @JsonProperty("total_count")
    private Integer totalCount;
}