package com.stepup.springrobot.dto.study;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AssetImageDTO {
    @JsonProperty("url")
    private String url;

    //robot local asset's path
    @JsonProperty("local")
    private String local;

    @JsonProperty("etag")
    private String etag;
} 