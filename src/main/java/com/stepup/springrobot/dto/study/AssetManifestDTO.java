package com.stepup.springrobot.dto.study;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AssetManifestDTO {
    @JsonProperty("is_preload")
    private Boolean isPreload;

    @JsonProperty("images")
    private List<AssetImageDTO> images;
} 