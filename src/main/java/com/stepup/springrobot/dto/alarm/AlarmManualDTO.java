package com.stepup.springrobot.dto.alarm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmManualDTO {
    @JsonProperty("user_phone")
    private String userPhone;

    @JsonProperty("remind_sentence")
    private String remindSentence;

    @JsonProperty("sound")
    private String sound;

    @JsonProperty("remind_voice")
    private String remindVoice;

    @JsonProperty("response_voice")
    private String responseVoice;

}
