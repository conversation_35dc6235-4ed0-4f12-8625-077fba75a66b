package com.stepup.springrobot.dto.speech;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request payload for updating ASR/Intent QC fields of a message record.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AsrQcUpdateRequestDTO {
    @JsonProperty("message_id")
    private Long messageId;

    @JsonProperty("is_asr_correct")
    private Boolean isAsrCorrect;

    @JsonProperty("asr_corrected_content")
    private String asrCorrectedContent;

    @JsonProperty("is_intent_affected")
    private Boolean isIntentAffected;

    @JsonProperty("intent_corrected_content")
    private String intentCorrectedContent;
}


