package com.stepup.springrobot.dto.speech;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WakeWordDetectionResponseDTO {
    private boolean detected;

    @JsonProperty("audio_url")
    private String audioUrl;

    @JsonProperty("max_score")
    private Double maxScore;

    @JsonProperty("threshold")
    private Double threshold;
}
