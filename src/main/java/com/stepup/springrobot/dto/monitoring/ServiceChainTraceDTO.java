package com.stepup.springrobot.dto.monitoring;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class ServiceChainTraceDTO {
    @JsonProperty("trace_id")
    private String traceId;

    @JsonProperty("conversation_id")
    private String conversationId;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("robot_id")
    private String robotId;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("chain_start_time")
    private Instant chainStartTime;

    @JsonProperty("chain_end_time")
    private Instant chainEndTime;

    @JsonProperty("total_duration_ms")
    private Long totalDurationMs;

    @JsonProperty("service_calls")
    private List<ServiceCallTrace> serviceCalls;

    @JsonProperty("chain_status")
    private String chainStatus; // SUCCESS, PARTIAL_SUCCESS, FAILED

    @JsonProperty("error_message")
    private String errorMessage;

    @JsonProperty("metadata")
    private Map<String, Object> metadata;

    @Data
    @Builder
    public static class ServiceCallTrace {
        @JsonProperty("service_name")
        private String serviceName;

        @JsonProperty("method_name")
        private String methodName;

        @JsonProperty("span_id")
        private String spanId;

        @JsonProperty("parent_span_id")
        private String parentSpanId;

        @JsonProperty("start_time")
        private Instant startTime;

        @JsonProperty("end_time")
        private Instant endTime;

        @JsonProperty("duration_ms")
        private Long durationMs;

        @JsonProperty("status")
        private String status; // SUCCESS, ERROR, TIMEOUT

        @JsonProperty("input_data")
        private JsonNode inputData;

        @JsonProperty("output_data")
        private JsonNode outputData;

        @JsonProperty("error_message")
        private String errorMessage;

        @JsonProperty("service_endpoint")
        private String serviceEndpoint;

        @JsonProperty("http_status_code")
        private Integer httpStatusCode;

        @JsonProperty("retry_count")
        private Integer retryCount;

        @JsonProperty("queue_wait_time_ms")
        private Long queueWaitTimeMs;

        @JsonProperty("processing_time_ms")
        private Long processingTimeMs;

        @JsonProperty("dependency_calls")
        private List<ServiceCallTrace> dependencyCalls;
    }
}
