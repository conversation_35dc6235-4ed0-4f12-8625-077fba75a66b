package com.stepup.springrobot.dto.monitoring;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;

@Data
@Builder
public class TraceContextDTO {
    @JsonProperty("trace_id")
    private String traceId;

    @JsonProperty("span_id")
    private String spanId;

    @JsonProperty("parent_span_id")
    private String parentSpanId;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("robot_id")
    private String robotId;

    @JsonProperty("conversation_id")
    private String conversationId;

    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("timestamp")
    private Instant timestamp;

    public static TraceContextDTO createNew(String userId, String sessionId) {
        return TraceContextDTO.builder()
                .traceId(generateTraceId())
                .spanId(generateSpanId())
                .userId(userId)
                .sessionId(sessionId)
                .timestamp(Instant.now())
                .build();
    }

    public TraceContextDTO createChildSpan() {
        return TraceContextDTO.builder()
                .traceId(this.traceId)
                .spanId(generateSpanId())
                .parentSpanId(this.spanId)
                .userId(this.userId)
                .sessionId(this.sessionId)
                .robotId(this.robotId)
                .conversationId(this.conversationId)
                .requestId(this.requestId)
                .timestamp(Instant.now())
                .build();
    }

    private static String generateTraceId() {
        return "trace_" + System.currentTimeMillis() + "_" +
                Long.toHexString(Double.doubleToLongBits(Math.random()));
    }

    private static String generateSpanId() {
        return "span_" + System.currentTimeMillis() + "_" +
                Long.toHexString(Double.doubleToLongBits(Math.random())).substring(0, 8);
    }
}
