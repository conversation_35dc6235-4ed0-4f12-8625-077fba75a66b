package com.stepup.springrobot.dto.monitoring;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class ConversationFlowDTO {
    @JsonProperty("conversation_id")
    private String conversationId;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("robot_id")
    private String robotId;

    @JsonProperty("flow_stage")
    private ConversationFlowStage flowStage;

    @JsonProperty("stage_name")
    private String stageName;

    @JsonProperty("start_time")
    private Instant startTime;

    @JsonProperty("end_time")
    private Instant endTime;

    @JsonProperty("duration_ms")
    private Long durationMs;

    @JsonProperty("status")
    private String status; // SUCCESS, ERROR, TIMEOUT

    @JsonProperty("input_data")
    private JsonNode inputData;

    @JsonProperty("output_data")
    private JsonNode outputData;

    @JsonProperty("error_message")
    private String errorMessage;

    @JsonProperty("service_endpoint")
    private String serviceEndpoint;

    @JsonProperty("service_method")
    private String serviceMethod;

    @JsonProperty("retry_count")
    private Integer retryCount;

    @JsonProperty("metadata")
    private Map<String, Object> metadata;

    // Audio processing specific
    @JsonProperty("audio_size_bytes")
    private Integer audioSizeBytes;

    @JsonProperty("audio_duration_ms")
    private Long audioDurationMs;

    @JsonProperty("audio_format")
    private String audioFormat;

    // ASR specific
    @JsonProperty("asr_provider")
    private String asrProvider; // GOOGLE, INTERNAL, GRPC

    @JsonProperty("transcript_text")
    private String transcriptText;

    @JsonProperty("confidence_score")
    private Double confidenceScore;

    @JsonProperty("language_detected")
    private String languageDetected;

    // LLM specific
    @JsonProperty("llm_provider")
    private String llmProvider;

    @JsonProperty("llm_model")
    private String llmModel;

    @JsonProperty("prompt_tokens")
    private Integer promptTokens;

    @JsonProperty("completion_tokens")
    private Integer completionTokens;

    @JsonProperty("total_tokens")
    private Integer totalTokens;

    @JsonProperty("llm_call_type")
    private String llmCallType; // CONVERSATION, ACTION

    // TTS specific
    @JsonProperty("tts_provider")
    private String ttsProvider;

    @JsonProperty("tts_voice")
    private String ttsVoice;

    @JsonProperty("tts_speed")
    private Double ttsSpeed;

    @JsonProperty("tts_volume")
    private Integer ttsVolume;

    @JsonProperty("generated_audio_url")
    private String generatedAudioUrl;

    // Animation/Servo specific
    @JsonProperty("emotion_type")
    private String emotionType;

    @JsonProperty("servo_actions")
    private List<String> servoActions;

    @JsonProperty("animation_data")
    private JsonNode animationData;

    // Performance metrics
    @JsonProperty("cpu_usage_percent")
    private Double cpuUsagePercent;

    @JsonProperty("memory_usage_mb")
    private Long memoryUsageMb;

    @JsonProperty("queue_size")
    private Integer queueSize;

    @JsonProperty("concurrent_sessions")
    private Integer concurrentSessions;

    public enum ConversationFlowStage {
        // Main flow stages
        WEBSOCKET_CONNECTION("websocket_connection"),
        AUDIO_RECEIVED("audio_received"),
        AUDIO_PROCESSING("audio_processing"),
        ASR_PROCESSING("asr_processing"),
        LLM_PROCESSING("llm_processing"),
        TTS_PROCESSING("tts_processing"),
        ANIMATION_PROCESSING("animation_processing"),
        RESPONSE_SENT("response_sent"),
        CONVERSATION_END("conversation_end"),

        // Sub-stages for detailed tracking
        AUDIO_UPLOAD_S3("audio_upload_s3"),
        ASR_GOOGLE_CALL("asr_google_call"),
        ASR_INTERNAL_CALL("asr_internal_call"),
        ASR_GRPC_CALL("asr_grpc_call"),
        LLM_CONVERSATION_CALL("llm_conversation_call"),
        LLM_ACTION_CALL("llm_action_call"),
        TTS_CONVERSION("tts_conversion"),
        SERVO_ACTION_SELECTION("servo_action_selection"),
        ANIMATION_GENERATION("animation_generation"),

        // Error stages
        ASR_ERROR("asr_error"),
        LLM_ERROR("llm_error"),
        TTS_ERROR("tts_error"),
        ANIMATION_ERROR("animation_error"),
        TIMEOUT_ERROR("timeout_error"),
        UNKNOWN_ERROR("unknown_error");

        private final String stageName;

        ConversationFlowStage(String stageName) {
            this.stageName = stageName;
        }

        public String getStageName() {
            return stageName;
        }
    }
}
