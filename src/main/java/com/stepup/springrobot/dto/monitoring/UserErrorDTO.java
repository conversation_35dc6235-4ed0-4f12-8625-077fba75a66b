package com.stepup.springrobot.dto.monitoring;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class UserErrorDTO {

    @JsonProperty("error_id")
    private String errorId; // Unique error ID

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("conversation_id")
    private String conversationId;

    @JsonProperty("robot_id")
    private String robotId;

    @JsonProperty("journey_id")
    private String journeyId;

    @JsonProperty("error_type")
    private ErrorType errorType;

    @JsonProperty("error_category")
    private ErrorCategory errorCategory;

    @JsonProperty("error_severity")
    private ErrorSeverity errorSeverity;

    @JsonProperty("error_code")
    private String errorCode;

    @JsonProperty("error_message")
    private String errorMessage;

    @JsonProperty("error_description")
    private String errorDescription;

    @JsonProperty("stack_trace")
    private String stackTrace;

    @JsonProperty("service_name")
    private String serviceName;

    @JsonProperty("method_name")
    private String methodName;

    @JsonProperty("class_name")
    private String className;

    @JsonProperty("line_number")
    private Integer lineNumber;

    @JsonProperty("occurrence_time")
    private Instant occurrenceTime;

    @JsonProperty("resolution_time")
    private Instant resolutionTime;

    @JsonProperty("resolution_duration_ms")
    private Long resolutionDurationMs;

    @JsonProperty("is_resolved")
    private Boolean isResolved;

    @JsonProperty("resolution_method")
    private String resolutionMethod; // RETRY, FALLBACK, MANUAL, AUTO_RECOVERY

    @JsonProperty("user_impact")
    private UserImpact userImpact;

    @JsonProperty("business_impact")
    private BusinessImpact businessImpact;

    @JsonProperty("context_before_error")
    private Map<String, Object> contextBeforeError;

    @JsonProperty("context_after_error")
    private Map<String, Object> contextAfterError;

    @JsonProperty("user_actions_before_error")
    private List<String> userActionsBeforeError;

    @JsonProperty("system_state")
    private Map<String, Object> systemState;

    @JsonProperty("external_factors")
    private Map<String, Object> externalFactors;

    @JsonProperty("retry_attempts")
    private Integer retryAttempts;

    @JsonProperty("max_retry_attempts")
    private Integer maxRetryAttempts;

    @JsonProperty("fallback_triggered")
    private Boolean fallbackTriggered;

    @JsonProperty("fallback_success")
    private Boolean fallbackSuccess;

    @JsonProperty("user_notified")
    private Boolean userNotified;

    @JsonProperty("notification_method")
    private String notificationMethod;

    @JsonProperty("related_errors")
    private List<String> relatedErrorIds;

    @JsonProperty("tags")
    private List<String> tags;

    @JsonProperty("metadata")
    private Map<String, Object> metadata;

    public enum ErrorType {
        TECHNICAL("technical"),
        BUSINESS("business"),
        VALIDATION("validation"),
        AUTHENTICATION("authentication"),
        AUTHORIZATION("authorization"),
        NETWORK("network"),
        TIMEOUT("timeout"),
        RATE_LIMIT("rate_limit"),
        QUOTA_EXCEEDED("quota_exceeded"),
        EXTERNAL_SERVICE("external_service"),
        DATABASE("database"),
        FILE_SYSTEM("file_system"),
        MEMORY("memory"),
        CPU("cpu"),
        UNKNOWN("unknown");

        private final String typeName;

        ErrorType(String typeName) {
            this.typeName = typeName;
        }

        public String getTypeName() {
            return typeName;
        }
    }

    public enum ErrorCategory {
        CRITICAL("critical"),
        HIGH("high"),
        MEDIUM("medium"),
        LOW("low"),
        INFO("info");

        private final String categoryName;

        ErrorCategory(String categoryName) {
            this.categoryName = categoryName;
        }

        public String getCategoryName() {
            return categoryName;
        }
    }

    public enum ErrorSeverity {
        BLOCKER("blocker"),        // Blocks user completely
        CRITICAL("critical"),      // Major functionality broken
        MAJOR("major"),           // Important feature affected
        MINOR("minor"),           // Minor inconvenience
        TRIVIAL("trivial");       // Cosmetic issue

        private final String severityName;

        ErrorSeverity(String severityName) {
            this.severityName = severityName;
        }

        public String getSeverityName() {
            return severityName;
        }
    }

    @Data
    @Builder
    public static class UserImpact {
        @JsonProperty("blocked_completely")
        private Boolean blockedCompletely;

        @JsonProperty("feature_unavailable")
        private List<String> featureUnavailable;

        @JsonProperty("degraded_experience")
        private Boolean degradedExperience;

        @JsonProperty("data_loss")
        private Boolean dataLoss;

        @JsonProperty("time_wasted_minutes")
        private Integer timeWastedMinutes;

        @JsonProperty("user_frustration_level")
        private Integer userFrustrationLevel; // 1-10 scale

        @JsonProperty("likely_to_churn")
        private Boolean likelyToChurn;
    }

    @Data
    @Builder
    public static class BusinessImpact {
        @JsonProperty("revenue_loss_usd")
        private Double revenueLossUsd;

        @JsonProperty("cost_of_resolution_usd")
        private Double costOfResolutionUsd;

        @JsonProperty("sla_breach")
        private Boolean slaBreach;

        @JsonProperty("reputation_damage")
        private Boolean reputationDamage;

        @JsonProperty("compliance_violation")
        private Boolean complianceViolation;

        @JsonProperty("affected_users_count")
        private Integer affectedUsersCount;

        @JsonProperty("downtime_minutes")
        private Integer downtimeMinutes;
    }
}
