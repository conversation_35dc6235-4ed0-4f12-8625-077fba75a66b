package com.stepup.springrobot.dto.monitoring;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.DatadogLogType;
import com.stepup.springrobot.model.chat.STTHandlerType;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.Map;

@Data
@Builder
public class MonitoringLogDTO {
    @JsonProperty("log_type")
    private DatadogLogType logType;

    @JsonProperty("conversation_id")
    private Long conversationId;

    @JsonProperty("message")
    private String message;

    @JsonProperty("status_code")
    private Integer statusCode;

    @JsonProperty("level")
    private String level; // INFO, WARN, ERROR, DEBUG

    @JsonProperty("service_name")
    private String serviceName;

    @JsonProperty("method_name")
    private String methodName;

    @JsonProperty("class_name")
    private String className;

    @JsonProperty("trace_context")
    private TraceContextDTO traceContext;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("robot_id")
    private String robotId;

    @JsonProperty("socket_session_id")
    private String socketSessionId;

    @JsonProperty("status")
    private String status; // SUCCESS, ERROR, WARNING

    @JsonProperty("duration_ms")
    private Long durationMs;

    @JsonProperty("timestamp")
    private Instant timestamp;

    @JsonProperty("environment")
    private String environment;

    @JsonProperty("version")
    private String version;

    // WebSocket specific fields
    @JsonProperty("client_ip")
    private String clientIp;

    @JsonProperty("user_agent")
    private String userAgent;

    @JsonProperty("connection_type")
    private String connectionType;

    // Audio specific fields
    @JsonProperty("audio_size_bytes")
    private Long audioSizeBytes;

    @JsonProperty("audio_format")
    private String audioFormat;

    @JsonProperty("audio_duration_seconds")
    private Double audioDurationSeconds;

    // S3 specific fields
    @JsonProperty("s3_bucket")
    private String s3Bucket;

    @JsonProperty("s3_key")
    private String s3Key;

    @JsonProperty("upload_size_bytes")
    private Long uploadSizeBytes;

    // ASR specific fields
    @JsonProperty("asr_provider")
    private STTHandlerType asrProvider;

    @JsonProperty("confidence_score")
    private Double confidenceScore;

    @JsonProperty("transcribed_text")
    private String transcribedText;

    @JsonProperty("language")
    private String language;

    @JsonProperty("silence_threshold")
    private Double silenceThreshold;

    @JsonProperty("model_version")
    private String modelVersion;

    @JsonProperty("grpc_endpoint")
    private String grpcEndpoint;

    @JsonProperty("api_response_code")
    private Integer apiResponseCode;

    // LLM specific fields
    @JsonProperty("llm_call_type")
    private String llmCallType;

    @JsonProperty("llm_provider")
    private String llmProvider;

    @JsonProperty("model_name")
    private String modelName;

    @JsonProperty("prompt_tokens")
    private Integer promptTokens;

    @JsonProperty("completion_tokens")
    private Integer completionTokens;

    @JsonProperty("total_tokens")
    private Integer totalTokens;

    @JsonProperty("llm_response")
    private String llmResponse;

    @JsonProperty("action_type")
    private String actionType;

    @JsonProperty("action_parameters")
    private Map<String, Object> actionParameters;

    @JsonProperty("action_result")
    private String actionResult;

    // TTS specific fields
    @JsonProperty("tts_provider")
    private String ttsProvider;

    @JsonProperty("voice_name")
    private String voiceName;

    @JsonProperty("text_length")
    private Integer textLength;

    @JsonProperty("audio_output_size_bytes")
    private Long audioOutputSizeBytes;

    @JsonProperty("audio_output_duration_seconds")
    private Double audioOutputDurationSeconds;

    // Animation specific fields
    @JsonProperty("animation_type")
    private String animationType;

    @JsonProperty("animation_duration_seconds")
    private Double animationDurationSeconds;

    @JsonProperty("animation_file_size_bytes")
    private Long animationFileSizeBytes;

    // Response specific fields
    @JsonProperty("response_type")
    private String responseType;

    @JsonProperty("total_response_size_bytes")
    private Long totalResponseSizeBytes;

    // Conversation end specific fields
    @JsonProperty("total_duration_ms")
    private Long totalDurationMs;

    @JsonProperty("stages_completed")
    private Integer stagesCompleted;

    @JsonProperty("total_stages")
    private Integer totalStages;

    @JsonProperty("success_rate")
    private Double successRate;

    @JsonProperty("connection_duration_seconds")
    private Double connectionDurationSeconds;

    // Error specific fields
    @JsonProperty("error_code")
    private String errorCode;

    @JsonProperty("error_message")
    private String errorMessage;

    @JsonProperty("error_stack_trace")
    private String errorStackTrace;

    @JsonProperty("request_data")
    private JsonNode requestData;

    @JsonProperty("response_data")
    private JsonNode responseData;

    @JsonProperty("retry_count")
    private Integer retryCount;

    // HTTP specific fields
    @JsonProperty("http_method")
    private String httpMethod;

    @JsonProperty("http_url")
    private String httpUrl;

    @JsonProperty("http_headers")
    private Map<String, String> httpHeaders;

    @JsonProperty("http_query_params")
    private Map<String, String> httpQueryParams;

    // Additional metadata
    @JsonProperty("metadata")
    private Map<String, Object> metadata;
}
