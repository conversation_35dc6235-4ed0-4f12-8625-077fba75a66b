package com.stepup.springrobot.dto.monitoring;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.Map;

@Data
@Builder
public class WebSocketEventDTO {
    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("endpoint")
    private String endpoint;

    @JsonProperty("event_type")
    private String eventType; // CONNECT, DISCONNECT, MESSAGE_RECEIVED, MESSAGE_SENT, ERROR

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("robot_id")
    private String robotId;

    @JsonProperty("remote_address")
    private String remoteAddress;

    @JsonProperty("message_type")
    private String messageType; // TEXT, BINARY

    @JsonProperty("message_size")
    private Integer messageSize;

    @JsonProperty("close_status")
    private String closeStatus;

    @JsonProperty("error_message")
    private String errorMessage;

    @JsonProperty("connection_duration_ms")
    private Long connectionDurationMs;

    @JsonProperty("session_attributes")
    private Map<String, Object> sessionAttributes;

    @JsonProperty("timestamp")
    private Instant timestamp;
}
