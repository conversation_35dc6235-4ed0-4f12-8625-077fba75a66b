package com.stepup.springrobot.dto.monitoring;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class UserJourneyDTO {

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("journey_id")
    private String journeyId; // Unique ID for this user journey

    @JsonProperty("journey_type")
    private JourneyType journeyType;

    @JsonProperty("journey_stage")
    private JourneyStage journeyStage;

    @JsonProperty("stage_name")
    private String stageName;

    @JsonProperty("robot_id")
    private String robotId;

    @JsonProperty("conversation_id")
    private String conversationId;

    @JsonProperty("start_time")
    private Instant startTime;

    @JsonProperty("end_time")
    private Instant endTime;

    @JsonProperty("duration_ms")
    private Long durationMs;

    @JsonProperty("status")
    private String status; // SUCCESS, ERROR, TIMEOUT, IN_PROGRESS

    @JsonProperty("error_message")
    private String errorMessage;

    @JsonProperty("error_code")
    private String errorCode;

    @JsonProperty("error_category")
    private String errorCategory; // NETWORK, VALIDATION, BUSINESS_LOGIC, EXTERNAL_API, etc.

    @JsonProperty("previous_stage")
    private JourneyStage previousStage;

    @JsonProperty("next_stage")
    private JourneyStage nextStage;

    @JsonProperty("user_input")
    private Object userInput;

    @JsonProperty("system_response")
    private Object systemResponse;

    @JsonProperty("context_data")
    private Map<String, Object> contextData;

    @JsonProperty("device_info")
    private DeviceInfo deviceInfo;

    @JsonProperty("performance_metrics")
    private PerformanceMetrics performanceMetrics;

    @JsonProperty("business_metrics")
    private BusinessMetrics businessMetrics;

    @JsonProperty("retry_count")
    private Integer retryCount;

    @JsonProperty("is_fallback")
    private Boolean isFallback;

    @JsonProperty("fallback_reason")
    private String fallbackReason;

    @JsonProperty("tags")
    private List<String> tags;

    @JsonProperty("timestamp")
    private Instant timestamp;

    public enum JourneyType {
        CONVERSATION("conversation"),
        ONBOARDING("onboarding"),
        AUTHENTICATION("authentication"),
        CONFIGURATION("configuration"),
        ERROR_RECOVERY("error_recovery"),
        MAINTENANCE("maintenance");

        private final String typeName;

        JourneyType(String typeName) {
            this.typeName = typeName;
        }

        public String getTypeName() {
            return typeName;
        }
    }

    public enum JourneyStage {
        // Authentication stages
        AUTH_START("auth_start"),
        AUTH_TOKEN_VALIDATION("auth_token_validation"),
        AUTH_SUCCESS("auth_success"),
        AUTH_FAILED("auth_failed"),

        // Connection stages
        WEBSOCKET_CONNECT("websocket_connect"),
        WEBSOCKET_HANDSHAKE("websocket_handshake"),
        WEBSOCKET_ESTABLISHED("websocket_established"),
        WEBSOCKET_DISCONNECT("websocket_disconnect"),

        // Conversation stages
        CONVERSATION_START("conversation_start"),
        AUDIO_UPLOAD("audio_upload"),
        ASR_PROCESSING("asr_processing"),
        LLM_PROCESSING("llm_processing"),
        TTS_PROCESSING("tts_processing"),
        RESPONSE_DELIVERY("response_delivery"),
        CONVERSATION_END("conversation_end"),

        // Error stages
        ERROR_OCCURRED("error_occurred"),
        ERROR_RECOVERY_START("error_recovery_start"),
        ERROR_RECOVERY_SUCCESS("error_recovery_success"),
        ERROR_RECOVERY_FAILED("error_recovery_failed"),

        // System stages
        SYSTEM_HEALTH_CHECK("system_health_check"),
        RATE_LIMIT_CHECK("rate_limit_check"),
        QUOTA_CHECK("quota_check"),

        // Business stages
        FEATURE_USAGE("feature_usage"),
        SUBSCRIPTION_CHECK("subscription_check"),
        BILLING_EVENT("billing_event");

        private final String stageName;

        JourneyStage(String stageName) {
            this.stageName = stageName;
        }

        public String getStageName() {
            return stageName;
        }
    }

    @Data
    @Builder
    public static class DeviceInfo {
        @JsonProperty("device_type")
        private String deviceType; // ROBOT, MOBILE, WEB

        @JsonProperty("device_id")
        private String deviceId;

        @JsonProperty("ip_address")
        private String ipAddress;

        @JsonProperty("user_agent")
        private String userAgent;

        @JsonProperty("platform")
        private String platform;

        @JsonProperty("app_version")
        private String appVersion;
    }

    @Data
    @Builder
    public static class PerformanceMetrics {
        @JsonProperty("response_time_ms")
        private Long responseTimeMs;

        @JsonProperty("queue_wait_time_ms")
        private Long queueWaitTimeMs;

        @JsonProperty("processing_time_ms")
        private Long processingTimeMs;

        @JsonProperty("memory_usage_mb")
        private Long memoryUsageMb;

        @JsonProperty("cpu_usage_percent")
        private Double cpuUsagePercent;

        @JsonProperty("network_latency_ms")
        private Long networkLatencyMs;
    }

    @Data
    @Builder
    public static class BusinessMetrics {
        @JsonProperty("feature_used")
        private String featureUsed;

        @JsonProperty("success_rate")
        private Double successRate;

        @JsonProperty("user_satisfaction_score")
        private Integer userSatisfactionScore;

        @JsonProperty("conversation_length")
        private Integer conversationLength;

        @JsonProperty("cost_usd")
        private Double costUsd;

        @JsonProperty("revenue_impact")
        private Double revenueImpact;
    }
}
