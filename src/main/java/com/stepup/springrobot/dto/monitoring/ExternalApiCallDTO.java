package com.stepup.springrobot.dto.monitoring;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.Map;

@Data
@Builder
public class ExternalApiCallDTO {
    @JsonProperty("api_name")
    private String apiName;

    @JsonProperty("url")
    private String url;

    @JsonProperty("method")
    private String method;

    @JsonProperty("request_headers")
    private Map<String, String> requestHeaders;

    @JsonProperty("request_body")
    private JsonNode requestBody;

    @JsonProperty("response_status")
    private Integer responseStatus;

    @JsonProperty("response_headers")
    private Map<String, String> responseHeaders;

    @JsonProperty("response_body")
    private JsonNode responseBody;

    @JsonProperty("duration_ms")
    private Long durationMs;

    @JsonProperty("timeout_ms")
    private Long timeoutMs;

    @JsonProperty("retry_count")
    private Integer retryCount;

    @JsonProperty("error_message")
    private String errorMessage;

    @JsonProperty("timestamp")
    private Instant timestamp;

    @JsonProperty("success")
    private Boolean success;
}
