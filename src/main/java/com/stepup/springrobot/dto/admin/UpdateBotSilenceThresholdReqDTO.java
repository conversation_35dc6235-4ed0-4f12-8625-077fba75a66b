package com.stepup.springrobot.dto.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateBotSilenceThresholdReqDTO {

    @NotBlank(message = "Bot ID cannot be blank")
    @JsonProperty("bot_id")
    private String botId;

    @NotNull(message = "Silence threshold cannot be null")
    @Min(value = 0, message = "Silence threshold must be at least 0 milliseconds")
    @Max(value = 10000, message = "Silence threshold must not exceed 10000 milliseconds")
    @JsonProperty("silence_threshold")
    private Integer silenceThreshold;
}
