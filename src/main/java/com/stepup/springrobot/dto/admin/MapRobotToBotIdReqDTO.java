package com.stepup.springrobot.dto.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MapRobotToBotIdReqDTO {

    @NotBlank(message = "Robot ID cannot be blank")
    @JsonProperty("robot_id")
    private String robotId;

    @NotNull(message = "Bot ID cannot be null")
    @JsonProperty("value") // Matches the 'value' field sent from the frontend for bot_id
    private Long botId;
}