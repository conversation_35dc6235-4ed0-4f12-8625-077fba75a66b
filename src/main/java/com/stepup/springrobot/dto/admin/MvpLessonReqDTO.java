package com.stepup.springrobot.dto.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class MvpLessonReqDTO {
    private String image;

    @JsonProperty("lesson_id")
    private Long lessonId;

    private String title;

    @JsonProperty("campaign_name")
    private String campaignName;
}
