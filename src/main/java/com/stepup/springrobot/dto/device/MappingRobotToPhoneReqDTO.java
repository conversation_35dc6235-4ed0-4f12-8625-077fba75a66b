package com.stepup.springrobot.dto.device;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MappingRobotToPhoneReqDTO {
    @JsonProperty("robot_id")
    private String robotId;

    @JsonProperty("firmware_version")
    private String firmware_version;
}
