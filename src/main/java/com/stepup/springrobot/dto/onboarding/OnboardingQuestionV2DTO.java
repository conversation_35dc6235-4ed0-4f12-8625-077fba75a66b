package com.stepup.springrobot.dto.onboarding;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.onboarding.OnboardingAnswerType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class OnboardingQuestionV2DTO {
    @JsonProperty("background")
    private String background;

    @JsonProperty("character")
    private String character;

    @JsonProperty("text")
    private String text;

    @JsonProperty("type")
    private String type;

    @JsonProperty("answer_type")
    private OnboardingAnswerType answerType;

    @JsonProperty("data")
    private JsonNode data;
}
