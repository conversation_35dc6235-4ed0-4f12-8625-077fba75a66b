package com.stepup.springrobot.dto.communication;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.communication.CommunicationRequestStatusType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UpdateSDStatusReqDTO {
    @NotNull(message = "resource_request_id is required")
    @JsonProperty("resource_request_id")
    private Long resourceRequestId;

    @NotNull(message = "Status is required")
    private CommunicationRequestStatusType status;
}