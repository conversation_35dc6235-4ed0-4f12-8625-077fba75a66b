package com.stepup.springrobot.dto.communication;

import com.stepup.springrobot.model.communication.UpdateSDActionType;
import com.stepup.springrobot.model.communication.UpdateSDDataType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UpdateSDDetailDTO {
    private List<UpdateSDResourceDTO> resources;

    private UpdateSDActionType action;

    private UpdateSDDataType type;
}