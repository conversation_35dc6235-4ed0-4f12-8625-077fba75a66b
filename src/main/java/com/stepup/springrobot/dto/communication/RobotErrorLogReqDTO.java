package com.stepup.springrobot.dto.communication;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RobotErrorLogReqDTO {
    @NotBlank(message = "Robot ID cannot be blank")
    @JsonProperty("robot_id")
    private String robotId;

    @JsonProperty("info_type")
    private String infoType;

    @JsonProperty("screen")
    private String screen;

    @JsonProperty("firmware_version")
    private String firmwareVersion;

    @JsonProperty("msg_detail")
    private String msgDetail;
} 