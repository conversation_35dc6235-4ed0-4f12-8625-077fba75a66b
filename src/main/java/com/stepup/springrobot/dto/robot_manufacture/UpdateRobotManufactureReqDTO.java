package com.stepup.springrobot.dto.robot_manufacture;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.robot.RobotManufacture;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateRobotManufactureReqDTO {
    @Size(max = 50, message = "Batch number must not exceed 50 characters")
    @JsonProperty("batch_number")
    private String batchNumber;

    @JsonProperty("manufacturing_status")
    private RobotManufacture.ManufacturingStatus manufacturingStatus;

    @JsonProperty("hardware_version")
    private String hardwareVersion;

    @JsonProperty("firmware_version")
    private String firmwareVersion;

    @JsonProperty("quality_check_passed")
    private Boolean qualityCheckPassed;

    @JsonProperty("quality_check_notes")
    private String qualityCheckNotes;

    @JsonProperty("manufactured_date")
    private LocalDateTime manufacturedDate;

    @JsonProperty("delivery_date")
    private LocalDateTime deliveryDate;

    @JsonProperty("assigned_robot_id")
    private String assignedRobotId;

    @JsonProperty("manufacturing_notes")
    private String manufacturingNotes;
} 