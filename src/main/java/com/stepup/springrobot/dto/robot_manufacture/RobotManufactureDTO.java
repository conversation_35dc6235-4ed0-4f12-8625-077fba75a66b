package com.stepup.springrobot.dto.robot_manufacture;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.robot.RobotManufacture;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RobotManufactureDTO {
    @JsonProperty("id")
    private String id;

    @JsonProperty("device_serial_number")
    private String deviceSerialNumber;

    @JsonProperty("batch_number")
    private String batchNumber;

    @JsonProperty("manufacturing_status")
    private RobotManufacture.ManufacturingStatus manufacturingStatus;

    @JsonProperty("hardware_version")
    private String hardwareVersion;

    @JsonProperty("manufactured_date")
    private LocalDateTime manufacturedDate;

    @JsonProperty("delivery_date")
    private LocalDateTime deliveryDate;

    @JsonProperty("robot_id")
    private String robotId;

    @JsonProperty("manufacturing_notes")
    private String manufacturingNotes;
} 