package com.stepup.springrobot.dto.robot_manufacture;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.robot.RobotManufacture;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateRobotManufactureReqDTO {
    @NotBlank(message = "Manufacturing key is required")
    @JsonProperty("manufacturing_key")
    private String manufacturingKey;

    @NotBlank(message = "Device serial number cannot be blank")
    @JsonProperty("device_serial_number")
    private String deviceSerialNumber;

    @NotBlank(message = "Batch number cannot be blank")
    @JsonProperty("batch_number")
    private String batchNumber;

    @NotNull(message = "Manufacturing status cannot be null")
    @JsonProperty("manufacturing_status")
    private RobotManufacture.ManufacturingStatus manufacturingStatus;

    @JsonProperty("hardware_version")
    private String hardwareVersion;

    @JsonProperty("manufactured_date")
    private LocalDateTime manufacturedDate;

    @JsonProperty("manufacturing_notes")
    private String manufacturingNotes;
} 