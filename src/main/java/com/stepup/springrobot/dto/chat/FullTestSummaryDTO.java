package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FullTestSummaryDTO {
    @JsonProperty("total_flows_tested")
    private Integer totalFlowsTested;
    
    @JsonProperty("successful_flows")
    private Integer successfulFlows;
    
    @JsonProperty("failed_flows")
    private Integer failedFlows;
    
    @JsonProperty("success_rate_percentage")
    private Double successRatePercentage;
    
    @JsonProperty("total_interactions")
    private Integer totalInteractions;
    
    @JsonProperty("average_interactions_per_flow")
    private Double averageInteractionsPerFlow;
    
    @JsonProperty("total_execution_time_seconds")
    private Long totalExecutionTimeSeconds;
    
    @JsonProperty("average_response_time_ms")
    private Double averageResponseTimeMs;
    
    @JsonProperty("total_tokens_used")
    private Integer totalTokensUsed;
}
