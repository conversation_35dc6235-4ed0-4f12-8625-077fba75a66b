package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingleRandomTestResDTO {
    @JsonProperty("bot_id")
    private Long botId;
    
    @JsonProperty("conversation_id")
    private Long conversationId;
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("total_interactions")
    private Integer totalInteractions;
    
    @JsonProperty("completed_successfully")
    private Boolean completedSuccessfully;
    
    @JsonProperty("final_status")
    private String finalStatus;
    
    @JsonProperty("error_message")
    private String errorMessage;
    
    @JsonProperty("execution_time_seconds")
    private Long executionTimeSeconds;
    
    @JsonProperty("workflow_details")
    private List<WorkflowDetailDTO> workflowDetails;
    
    @JsonProperty("random_intents_selected")
    private List<String> randomIntentsSelected;
    
    @JsonProperty("random_flow_details")
    private List<RandomIntentDetailDTO> randomFlowDetails; // Chi tiết về random flow theo thứ tự thực tế
    
    @JsonProperty("interactions")
    private List<TestInteractionDTO> interactions;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RandomIntentDetailDTO {
        @JsonProperty("intent_name")
        private String intentName;
        
        @JsonProperty("loop_count")
        private Integer loopCount;
        
        @JsonProperty("workflow_id")
        private Long workflowId;
    }
}
