package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingleRandomTestReqDTO {
    @JsonProperty("bot_id")
    private Long botId;
    
    @JsonProperty("user_id")
    private String userId;
}
