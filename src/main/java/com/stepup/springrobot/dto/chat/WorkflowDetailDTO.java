package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowDetailDTO {
    @JsonProperty("workflow_id")
    private Long workflowId;
    
    @JsonProperty("workflow_order")
    private Integer workflowOrder; // 1, 2, 3 for first, second, third workflow
    
    @JsonProperty("tested_intents")
    private List<IntentWithLoopCount> testedIntents; // ordered list of intents with their loop counts
    
    @JsonProperty("execution_history")
    private List<IntentExecutionRecord> executionHistory; // execution history in order
    
    @JsonProperty("start_step")
    private Integer startStep;
    
    @JsonProperty("end_step") 
    private Integer endStep;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class IntentWithLoopCount {
        @JsonProperty("intent_name")
        private String intentName;
        
        @JsonProperty("loop_count")
        private Integer loopCount; // từ workflow data, không phải execution count
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class IntentExecutionRecord {
        @JsonProperty("intent_name")
        private String intentName;
        
        @JsonProperty("execution_count")
        private Integer executionCount; // số lần thực tế đã thực hiện intent này
        
        @JsonProperty("expected_loop_count")
        private Integer expectedLoopCount; // số lần cần thực hiện từ Excel data
    }
}
