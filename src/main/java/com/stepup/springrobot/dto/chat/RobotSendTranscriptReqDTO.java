package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RobotSendTranscriptReqDTO {
    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("robot_id")
    private String robotId;

    @JsonProperty("bot_id")
    private Long botId;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("source")
    private String source;

    @JsonProperty("is_web_mvp")
    private Boolean isWebMvp;

    @JsonProperty("transcript")
    private String transcript;

    @JsonProperty("stt_raw")
    private String sttRaw;
}
