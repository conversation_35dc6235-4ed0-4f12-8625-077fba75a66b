package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConversationFlowDTO {
    @JsonProperty("conversation_id")
    private Integer conversationId;
    
    @JsonProperty("steps")
    private List<ConversationStepDTO> steps;
    
    @JsonProperty("total_steps")
    private Integer totalSteps;
}
