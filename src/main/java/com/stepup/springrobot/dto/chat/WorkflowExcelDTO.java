package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkflowExcelDTO {
    @JsonProperty("QUESTION")
    private String question;

    @JsonProperty("INTENT_NAME")
    private String intentName;

    @JsonProperty("INTENT_DESCRIPTION")
    private String intentDescription;

    @JsonProperty("LOOP_COUNT")
    private String loopCount;

    @JsonProperty("MAX_LOOP")
    private String maxLoop;
}
