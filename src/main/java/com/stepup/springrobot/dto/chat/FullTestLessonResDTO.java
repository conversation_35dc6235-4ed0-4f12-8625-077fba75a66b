package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FullTestLessonResDTO {
    @JsonProperty("bot_id")
    private Long botId;
    
    @JsonProperty("total_test_flows")
    private Integer totalTestFlows;
    
    @JsonProperty("test_results")
    private List<TestFlowResultDTO> testResults;
    
    @JsonProperty("summary")
    private FullTestSummaryDTO summary;
}
