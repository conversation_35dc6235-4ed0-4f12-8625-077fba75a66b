package com.stepup.springrobot.dto.chat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Tracks the current execution state during test flow
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestExecutionStateDTO {
    // Current position tracking
    private Integer currentWorkflowIndex;
    private Integer currentQuestionIndex;
    private Integer currentStepIndex;
    
    // Intent execution tracking
    private Map<String, Integer> intentExecutionCounts; // intentName -> execution count
    private List<String> executionHistory; // ordered list of executed intents
    
    // Current flow step info
    private String currentIntentName;
    private Integer currentIntentLoopCount; // expected loop count from Excel
    private Integer currentIntentExecutedCount; // how many times this intent has been executed
    
    // Workflow and question info
    private Long currentWorkflowId;
    private String currentQuestionText;
    
    // Flow control
    private Boolean isWorkflowCompleted;
    private Boolean isQuestionCompleted;
    private Boolean shouldMoveToNextQuestion;
    private Boolean shouldMoveToNextWorkflow;
}
