package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConversationStepDTO {
    @JsonProperty("question_index")
    private Integer questionIndex;
    
    @JsonProperty("question_text")
    private String questionText;
    
    @JsonProperty("intent_name")
    private String intentName;
    
    @JsonProperty("intent_description")
    private String intentDescription;
    
    @JsonProperty("expected_response")
    private String expectedResponse;
}
