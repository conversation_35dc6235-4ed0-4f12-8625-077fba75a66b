package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BotInfoDTO {
    private Long id;
    private String name;
    private String description;
    
    @JsonProperty("is_loading")
    private Integer isLoading;
}
