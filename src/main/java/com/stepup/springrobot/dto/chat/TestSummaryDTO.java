package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestSummaryDTO {
    @JsonProperty("total_questions")
    private Integer totalQuestions;
    
    @JsonProperty("total_intents")
    private Integer totalIntents;
    
    @JsonProperty("max_intents_per_question")
    private Integer maxIntentsPerQuestion;
    
    @JsonProperty("estimated_duration_minutes")
    private Integer estimatedDurationMinutes;
}
