package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Represents execution history for reporting
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExecutionHistoryDTO {
    @JsonProperty("conversation_id")
    private Long conversationId;
    
    @JsonProperty("workflow_execution_history")
    private Map<Long, List<IntentExecutionRecord>> workflowExecutionHistory; // workflowId -> execution records
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class IntentExecutionRecord {
        @JsonProperty("intent_name")
        private String intentName;
        
        @JsonProperty("execution_count")
        private Integer executionCount;
        
        @JsonProperty("expected_loop_count")
        private Integer expectedLoopCount;
        
        @JsonProperty("question_index")
        private Integer questionIndex;
        
        @JsonProperty("step_number")
        private Integer stepNumber;
    }
}
