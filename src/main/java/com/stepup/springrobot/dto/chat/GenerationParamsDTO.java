package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GenerationParamsDTO {
    private String model;
    
    @JsonProperty("top_p")
    private Integer topP;
    
    private Boolean stream;
    
    @JsonProperty("max_tokens")
    private Integer maxTokens;
    
    private Integer temperature;
}
