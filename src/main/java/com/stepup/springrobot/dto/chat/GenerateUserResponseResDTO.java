package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GenerateUserResponseResDTO {
    @JsonProperty("generated_response")
    private String generatedResponse;
    
    @JsonProperty("intent_matched")
    private String intentMatched;
    
    @JsonProperty("language")
    private String language;
    
    @JsonProperty("tokens_used")
    private Integer tokensUsed;
    
    @JsonProperty("model_used")
    private String modelUsed;
}
