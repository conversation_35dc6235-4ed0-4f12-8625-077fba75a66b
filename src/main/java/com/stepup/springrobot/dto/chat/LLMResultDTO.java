package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.chat.AnswerModeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LLMResultDTO {
    private List<LLMResultTextDTO> text;

    private String status;

    @JsonProperty("listening_animation")
    private LlmListeningDetailDTO listeningAnimation;

    @JsonProperty("robot_type")
    private String robotType;

    private String language;

    private Double silenceThreshold;

    @JsonProperty("answer_mode")
    private AnswerModeType answerMode;

    @JsonProperty("listening_audio")
    private String listeningAudio;

    @JsonProperty("listening_image")
    private String listeningImage;
}
