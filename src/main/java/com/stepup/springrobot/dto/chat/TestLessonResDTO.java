package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestLessonResDTO {
    @JsonProperty("bot_id")
    private Long botId;
    
    @JsonProperty("total_conversations")
    private Integer totalConversations;
    
    @JsonProperty("conversations")
    private List<ConversationFlowDTO> conversations;
    
    @JsonProperty("summary")
    private TestSummaryDTO summary;
}
