package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConversationMessageDTO {
    @JsonProperty("role")
    private String role; // "user" or "assistant"
    
    @JsonProperty("content")
    private String content;
    
    @JsonProperty("timestamp")
    private Long timestamp;
}
