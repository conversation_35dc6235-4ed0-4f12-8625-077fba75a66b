package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestFlowResultDTO {
    @JsonProperty("flow_id")
    private Integer flowId;
    
    @JsonProperty("flow_name")
    private String flowName;
    
    @JsonProperty("conversation_id")
    private Long conversationId;
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("total_interactions")
    private Integer totalInteractions;
    
    @JsonProperty("completed_successfully")
    private Boolean completedSuccessfully;
    
    @JsonProperty("final_status")
    private String finalStatus;
    
    @JsonProperty("error_message")
    private String errorMessage;
    
    @JsonProperty("interactions")
    private List<TestInteractionDTO> interactions;
    
    @JsonProperty("conversation_history")
    private List<ConversationMessageDTO> conversationHistory;
    
    @JsonProperty("execution_time_seconds")
    private Long executionTimeSeconds;
    
    @JsonProperty("workflow_details")
    private List<WorkflowDetailDTO> workflowDetails;
}
