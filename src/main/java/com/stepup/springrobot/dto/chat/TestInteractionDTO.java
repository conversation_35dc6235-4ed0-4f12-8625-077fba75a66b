package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestInteractionDTO {
    @JsonProperty("step_number")
    private Integer stepNumber;
    
    @JsonProperty("user_input")
    private String userInput;
    
    @JsonProperty("target_intent")
    private String targetIntent;
    
    @JsonProperty("robot_response")
    private String robotResponse;
    
    @JsonProperty("robot_status")
    private String robotStatus;
    
    @JsonProperty("timestamp")
    private Long timestamp;
    
    @JsonProperty("tokens_used")
    private Integer tokensUsed;
    
    @JsonProperty("response_time_ms")
    private Long responseTimeMs;
}
