package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GenerateUserResponseReqDTO {
    @JsonProperty("conversation_history")
    private List<ConversationMessageDTO> conversationHistory;
    
    @JsonProperty("target_intent")
    private String targetIntent;
    
    @JsonProperty("intent_description")
    private String intentDescription;
    
    @JsonProperty("language")
    private String language;
}
