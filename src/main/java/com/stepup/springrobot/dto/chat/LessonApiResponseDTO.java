package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class LessonApiResponseDTO {
    private Integer status;
    private String msg;
    private LessonResultDTO result;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LessonResultDTO {
        private Long id;
        private String name;
        private String description;
        private List<LessonScenarioDTO> scenario;
        
        @JsonProperty("is_loading")
        private Integer isLoading;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LessonScenarioDTO {
        
        @JsonProperty("robot_type")
        private String robotType;
        
        @JsonProperty("set_variable")
        private String setVariable;
        
        @JsonProperty("robot_type_id")
        private Long robotTypeId;
        
        @JsonProperty("param_extractor")
        private String paramExtractor;
    }
}
