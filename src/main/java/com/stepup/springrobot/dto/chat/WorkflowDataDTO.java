package com.stepup.springrobot.dto.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowDataDTO {
    private Long id;
    private String name;
    private String description;
    
    @JsonProperty("generation_params")
    private GenerationParamsDTO generationParams;
    
    @JsonProperty("provider_name")
    private String providerName;
    
    @JsonProperty("system_prompt")
    private String systemPrompt;
    
    @JsonProperty("system_extraction_variables")
    private String systemExtractionVariables;
    
    @JsonProperty("system_prompt_generation")
    private String systemPromptGeneration;
    
    @JsonProperty("system_extraction_profile")
    private String systemExtractionProfile;
    
    @JsonProperty("data_excel")
    private List<WorkflowExcelDTO> dataExcel;
}
