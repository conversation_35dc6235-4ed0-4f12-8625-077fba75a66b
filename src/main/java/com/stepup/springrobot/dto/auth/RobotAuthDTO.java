package com.stepup.springrobot.dto.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RobotAuthDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "Robot ID cannot be blank")
    @JsonProperty("robot_id")
    private String robotId;

    @NotBlank(message = "Password cannot be blank")
    private String password;
} 