package com.stepup.springrobot.dto.notification;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PushNotificationRequest {
    private String title;

    private String message;

    private String topic;

    @JsonProperty("post_url")
    private String postUrl;

    @JsonProperty("banner_url")
    private String bannerUrl;

    @JsonProperty("version_app_update")
    private String versionAppUpdate;

    @JsonProperty("is_save")
    private Boolean isSave = true;

    @JsonIgnore
    private String token;

    private List<String> phones;

    /**
     * Additional data to be sent with the notification
     * Used for notification type and other metadata
     */
    private Map<String, String> data;
}
