package com.stepup.springrobot.dto.notification;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PushNotificationReqDTO {
    private String title;

    private String message;

    @JsonProperty("is_save")
    private Boolean isSave = true;

    @JsonProperty("post_url")
    private String postUrl;

    @JsonProperty("banner_url")
    private String bannerUrl;

    @JsonProperty("version_app_update")
    private String versionAppUpdate;

    @JsonProperty("user_id")
    private String userId;

    /**
     * Additional data to be sent with the notification
     * Used for notification type and other metadata
     */
    private Map<String, String> data;
}
