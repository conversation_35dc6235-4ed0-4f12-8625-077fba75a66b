package com.stepup.springrobot.dto.notification;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PronunciationIssueReqDTO {

    @NotBlank(message = "Số điện thoại không được để trống")
    private String targetPhone;

    private String lessonId;
} 