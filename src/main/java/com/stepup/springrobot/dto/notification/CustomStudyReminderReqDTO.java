package com.stepup.springrobot.dto.notification;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CustomStudyReminderReqDTO {
    @NotBlank(message = "Số điện thoại không được để trống")
    private String targetPhone;

    private String title;

    @NotBlank(message = "Custom message không được để trống")
    private String customMessage;

    private String lessonId;
} 