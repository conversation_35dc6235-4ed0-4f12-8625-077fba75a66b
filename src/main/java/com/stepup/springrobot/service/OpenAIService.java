package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.dto.chat.ConversationMessageDTO;
import com.stepup.springrobot.dto.chat.GenerateUserResponseReqDTO;
import com.stepup.springrobot.dto.chat.GenerateUserResponseResDTO;
import com.stepup.springrobot.model.ModelAIProvider;
import com.stepup.springrobot.model.chat.GPTCharacter;
import com.stepup.springrobot.repository.ExternalProviderTokensRepository;
import com.stepup.springrobot.security.JwtService;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.service.OpenAiService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

@Log4j2
@Service
public class OpenAIService extends CommonService {
    public final String[] onionGptRemoveWords = {"[part 1]", "[part 2]", "(pause)", "(listen)", "(listening)", "(part 1)", "(part 2)", "(Question 1)", "(Question 2)", "(post 1)", "(post 2)", "(post 3)", "(post 4)", "Part 1: ", "Part 2: ", "part 1:", "part 2:"};

    // Default model and token settings
    private static final int DEFAULT_MAX_TOKENS = 2048;

    @Value("${openai.model:gpt-4o-mini}")
    private String openaiModel;

    private final ExternalProviderTokensRepository externalProviderTokensRepository;

    @Autowired
    protected OpenAIService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService,
                            SlackWarningSystemService slackWarningSystemService,
                            ExternalProviderTokensRepository externalProviderTokensRepository) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
        this.externalProviderTokensRepository = externalProviderTokensRepository;
    }

    /**
     * Sends a request to OpenAI API and returns the chat completion result
     *
     * @param token        OpenAI API key
     * @param chatMessages List of chat messages to send to OpenAI
     * @return ChatCompletionResult containing the API response
     */
    protected ChatCompletionResult getOpenAIResponse(String token, String model, List<ChatMessage> chatMessages) {
        return callOpenAI(token, chatMessages, model, DEFAULT_MAX_TOKENS);
    }

    /**
     * Sends a request to OpenAI API with custom model and max tokens
     *
     * @param token        OpenAI API token
     * @param chatMessages List of chat messages to send to OpenAI
     * @return ChatCompletionResult containing the API response
     */
    protected ChatCompletionResult callOpenAI(String token, List<ChatMessage> chatMessages, String modelName, int maxTokens) {
        OpenAiService openAiService = new OpenAiService(token, Duration.ofSeconds(60));

        ChatCompletionRequest request = ChatCompletionRequest.builder()
                .model(modelName)
                .messages(chatMessages)
                .temperature(0.0)
                .maxTokens(maxTokens)
                .n(1)
                .frequencyPenalty(0.0)
                .topP(1.0)
                .presencePenalty(0.0)
                .build();

        ChatCompletionResult chatCompletionResult = openAiService.createChatCompletion(request);
        log.info("Gửi request tới OpenAI thành công, model {}, request: {}, response: {}", modelName, request, chatCompletionResult);
        return chatCompletionResult;
    }

    /**
     * Helper method to extract the response content from ChatCompletionResult
     *
     * @param result The ChatCompletionResult from OpenAI API
     * @return The text content of the response
     */
    protected String extractResponseContent(ChatCompletionResult result) {
        if (result != null && result.getChoices() != null && !result.getChoices().isEmpty()) {
            return result.getChoices().get(0).getMessage().getContent()
                    .replace("```json", "").replace("```", "");
        }
        return "";
    }

    /**
     * Removes unwanted words from the OpenAI response
     *
     * @param response The original response text
     * @return Cleaned response text
     */
    protected String cleanResponse(String response) {
        String cleanedResponse = response;
        for (String word : onionGptRemoveWords) {
            cleanedResponse = cleanedResponse.replace(word, "");
        }
        return cleanedResponse.trim();
    }

    /**
     * Compatible method for AdminService using old ChatMessage format
     * Note: The token parameter is accepted for backward compatibility, but if null/blank we will fetch from DB.
     */
    public String getOpenAIChatResponse(String token, String model, List<ChatMessage> messages) {
        try {
            String effectiveToken = (token == null || token.isBlank()) ? getOpenAIApiKey() : token;
            ChatCompletionResult result = getOpenAIResponse(effectiveToken, model, messages);
            return extractResponseContent(result);
        } catch (Exception e) {
            log.error("Error calling OpenAI API with ChatMessage format", e);
            throw new RuntimeException("Failed to get OpenAI chat response: " + e.getMessage(), e);
        }
    }

    /**
     * Generate user response using OpenAI based on conversation history and target intent.
     */
    public GenerateUserResponseResDTO generateUserResponse(GenerateUserResponseReqDTO request) {
        try {
            String token = getOpenAIApiKey();

            String systemPrompt = buildSystemPrompt(request);

            List<ChatMessage> chatMessages = buildChatMessages(systemPrompt, request.getConversationHistory());

            ChatMessage response = OpenAIUtil.getChatResponse(token, chatMessages);

            if (response == null || response.getContent() == null) {
                throw new RuntimeException("Invalid response from OpenAI API");
            }

            String generatedText = response.getContent();

            return GenerateUserResponseResDTO.builder()
                    .generatedResponse(generatedText.trim())
                    .intentMatched(request.getTargetIntent())
                    .language(request.getLanguage() != null ? request.getLanguage() : "en")
                    .tokensUsed(0)
                    .modelUsed(openaiModel)
                    .build();

        } catch (Exception e) {
            log.error("Error generating user response with OpenAI", e);
            throw new RuntimeException("Failed to generate user response: " + e.getMessage(), e);
        }
    }

    /**
     * Build system prompt for OpenAI.
     */
    private String buildSystemPrompt(GenerateUserResponseReqDTO request) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("Bạn là một đứa trẻ lớp 1, rất hồn nhiên, ngây thơ và tò mò. ");
        prompt.append("Bạn chỉ đóng vai là một đứa trẻ lớp 1 tham gia trò chuyện, không phải người ra đề, không phải người hướng dẫn, không phải người kể chuyện, không phải người tổng kết, không phải người đưa ra nhiệm vụ, không phải người cổ vũ, không phải người giải thích luật chơi, không phải người dẫn dắt câu chuyện.\n");
        prompt.append("Bạn chỉ trả lời đúng như một đứa trẻ lớp 1 đang trò chuyện với người lớn hoặc thầy cô, không được nói kiểu người lớn, không được đưa ra hướng dẫn, không được tổng kết, không được cảm ơn, không được cổ vũ, không được nhắc lại nhiệm vụ, không được nói về cuộc phiêu lưu, không được nói về các bạn thú, không được nói về trò chơi, không được nói về các bước tiếp theo, không được nói về việc giúp đỡ, không được nói về việc tham gia, không được nói về vai trò của mình.\n\n");

        prompt.append("Hãy đọc kỹ toàn bộ cuộc trò chuyện trước đó để hiểu mọi người đang nói về điều gì, và cảm nhận xem người lớn muốn gì ở bạn.\n");
        prompt.append("Nếu có từ mới hoặc điều gì chưa hiểu, bạn có thể hỏi lại hoặc trả lời theo cách đơn giản, dễ thương, đúng với lứa tuổi của mình.\n\n");

        prompt.append("YÊU CẦU (INTENT): ").append(request.getTargetIntent()).append("\n");
        if (request.getIntentDescription() != null && !request.getIntentDescription().trim().isEmpty()) {
            prompt.append("GIẢI THÍCH THÊM: ").append(request.getIntentDescription()).append("\n");
        }

        String language = "en".equals(request.getLanguage()) ? "English" : "Tiếng Việt";
        prompt.append("HÃY TRẢ LỜI BẰNG: ").append(language).append("\n\n");

        prompt.append("CÁCH TRẢ LỜI:\n");
        prompt.append("- Chỉ trả lời đúng như một đứa trẻ lớp 1, không được nói kiểu người lớn, không được đưa ra hướng dẫn, không được tổng kết, không được cảm ơn, không được cổ vũ, không được nhắc lại nhiệm vụ, không được nói về cuộc phiêu lưu, không được nói về các bạn thú, không được nói về trò chơi, không được nói về các bước tiếp theo, không được nói về việc giúp đỡ, không được nói về việc tham gia, không được nói về vai trò của mình.\n");
        prompt.append("- Hãy trả lời thật tự nhiên, đúng với cảm xúc và suy nghĩ của một đứa trẻ lớp 1.\n");
        prompt.append("- Đừng dùng từ ngữ quá phức tạp, hãy dùng câu ngắn gọn, dễ hiểu, có thể xen lẫn sự tò mò hoặc ngây ngô.\n");
        prompt.append("- Nếu thấy vui, ngạc nhiên, thích thú, hãy thể hiện cảm xúc đó trong câu trả lời.\n");
        prompt.append("- Đừng giải thích về ý định hay nhiệm vụ, chỉ trả lời như đang trò chuyện thật sự.\n");
        prompt.append("- Không dùng dấu ngoặc kép, không thêm ký tự đặc biệt, không nói kiểu người lớn.\n");
        prompt.append("- Nếu không biết, có thể nói \"Con chưa biết ạ\" hoặc hỏi lại một cách ngây thơ.\n\n");

        prompt.append("LƯU Ý:\n");
        prompt.append("- Tuyệt đối không được trả lời như người ra đề, người hướng dẫn, người kể chuyện, người tổng kết, người đưa ra nhiệm vụ, người cổ vũ, người giải thích luật chơi, người dẫn dắt câu chuyện.\n");
        prompt.append("- Đừng giải thích về cách trả lời, chỉ nói như một đứa trẻ đang phản hồi.\n");
        prompt.append("- Hãy giữ sự hồn nhiên, vui vẻ, và ngây thơ trong từng câu chữ.\n\n");

        prompt.append("CHỈ TRẢ LỜI BẰNG CÂU TRẢ LỜI CỦA MỘT ĐỨA TRẺ LỚP 1. KHÔNG ĐƯỢC ĐÓNG VAI NGƯỜI RA ĐỀ, NGƯỜI HƯỚNG DẪN, NGƯỜI KỂ CHUYỆN, NGƯỜI TỔNG KẾT, NGƯỜI ĐƯA RA NHIỆM VỤ, NGƯỜI CỔ VŨ, NGƯỜI GIẢI THÍCH LUẬT CHƠI, NGƯỜI DẪN DẮT CÂU CHUYỆN.");

        return prompt.toString();
    }

    /**
     * Build conversation messages for OpenAI using ChatMessage format (compatible with OpenAIUtil)
     */
    private List<ChatMessage> buildChatMessages(String systemPrompt, List<ConversationMessageDTO> history) {
        List<ChatMessage> messages = new ArrayList<>();

        messages.add(OpenAIUtil.buildChatMessage(GPTCharacter.system, systemPrompt));

        if (history != null) {
            for (ConversationMessageDTO msg : history) {
                GPTCharacter role = "user".equals(msg.getRole()) ? GPTCharacter.user : GPTCharacter.assistant;
                messages.add(OpenAIUtil.buildChatMessage(role, msg.getContent()));
            }
        }

        return messages;
    }

    /**
     * Get OpenAI API key from database
     */
    private String getOpenAIApiKey() {
        try {
            return externalProviderTokensRepository.findByProvider(ModelAIProvider.openai)
                    .stream()
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("OpenAI API token not found in database"))
                    .getToken();
        } catch (Exception e) {
            log.error("Error retrieving OpenAI API key from database", e);
            throw new RuntimeException("Failed to retrieve OpenAI API key: " + e.getMessage(), e);
        }
    }
}