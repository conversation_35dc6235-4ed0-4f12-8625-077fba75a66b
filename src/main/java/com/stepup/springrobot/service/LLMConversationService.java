package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.dto.llm.LlmInitConversationResDTO;
import com.stepup.springrobot.dto.llm_conversation.ChatReqDTO;
import com.stepup.springrobot.dto.llm_conversation.InitConversationReqDTO;
import com.stepup.springrobot.dto.llm_conversation.LLMChatResDTO;
import com.stepup.springrobot.dto.monitoring.MonitoringLogDTO;
import com.stepup.springrobot.model.DatadogLogType;
import com.stepup.springrobot.model.chat.ConversationLogType;
import com.stepup.springrobot.model.chat.RobotUserConversation;
import lombok.extern.log4j.Log4j2;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.TimeUnit;

@Log4j2
@Service
public class LLMConversationService {
    @Value("${llm_host_name}")
    private String llmHostName;

    @Value("${llm_init_uri}")
    private String llmInitUri;

    @Value("${llm_summary_uri}")
    private String llmSummaryUri;

    @Value("${llm_webhook_uri}")
    private String llmWebhookUri;

    @Value("${extract_facts_host}")
    private String extractFactHostName;

    @Value("${extract_facts_uri}")
    private String extractFactUri;

    private final ObjectMapper objectMapper;

    private final SlackWarningSystemService slackWarningSystemService;

    private final SharedService sharedService;

    private final DatadogService datadogService;

    @Autowired
    private ConversationFlowTracker conversationFlowTracker;

    @Autowired
    public LLMConversationService(ObjectMapper objectMapper, SlackWarningSystemService slackWarningSystemService, SharedService sharedService,
                                  DatadogService datadogService) {
        this.objectMapper = objectMapper;
        this.slackWarningSystemService = slackWarningSystemService;
        this.sharedService = sharedService;
        this.datadogService = datadogService;
    }

    public LlmInitConversationResDTO initConversation(String llmConversationId, String userId, String robotId, Long botId, Long robotConversationId) throws IOException {
        try {
            Instant start = Instant.now();
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, TimeUnit.SECONDS)
                    .writeTimeout(20, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build();

            InitConversationReqDTO reqDTO = InitConversationReqDTO.builder()
                    .botId(botId)
                    .userId(userId)
                    .conversationId(llmConversationId)
                    .build();

            RequestBody body = RequestBody.create(
                    okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsBytes(reqDTO));

            Request request = new Request.Builder()
                    .url(llmHostName + llmInitUri)
                    .post(body)
                    .build();

            Response response = client.newCall(request).execute();
            String responseString = response.body().string();
            long duration = Duration.between(start, Instant.now()).toMillis();
            log.info("=== Init LLM conversation: {}, url: {}", llmHostName + llmInitUri, responseString);

            ObjectNode data = JsonNodeFactory.instance.objectNode();
            data.set("request", objectMapper.valueToTree(reqDTO));
            data.set("response", objectMapper.readTree(responseString));
            sharedService.saveConversationLog(robotConversationId, ConversationLogType.LLM_RESPONSE, objectMapper.writeValueAsString(data), duration, false);

            // send to datadog
            MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                    .logType(DatadogLogType.CONVERSATION_LLM_RESPONSE)
                    .robotId(robotId)
                    .statusCode(CodeDefine.OK)
                    .conversationId(robotConversationId)
                    .clientIp(null)
                    .message("tts")
                    .level("INFO")
                    .durationMs(duration)
                    .timestamp(Instant.now())
                    .build();
            datadogService.handleMonitoringLog(monitoringLog);

            return objectMapper.readValue(responseString, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error when init LLM conversation", e);
            slackWarningSystemService.sendWarningSystemToSlack("Error when init LLM conversation: " + e.getMessage(), ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());

            // send to datadog
            MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                    .logType(DatadogLogType.CONVERSATION_LLM_RESPONSE)
                    .robotId(robotId)
                    .statusCode(CodeDefine.SERVER_ERROR)
                    .conversationId(robotConversationId)
                    .clientIp(null)
                    .message("Error when init LLM conversation: " + e.getMessage())
                    .level("ERROR")
                    .timestamp(Instant.now())
                    .build();
            datadogService.handleMonitoringLog(monitoringLog);

            throw e;
        }
    }

    public LLMChatResDTO getConversationResponse(Long conversationId, String llmConversationId, String userAnswer, String userAudio, String robotId) throws IOException {
        Instant start = Instant.now();

        try {
            // Track LLM call start
            conversationFlowTracker.trackLLMStart(conversationId.toString(), "CONVERSATION", userAnswer, userAudio);

            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, TimeUnit.SECONDS)
                    .writeTimeout(20, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build();

            ChatReqDTO reqDTO = ChatReqDTO.builder()
                    .message(userAnswer)
                    .conversationId(llmConversationId)
                    .audioUrl(StringUtils.isEmpty(userAudio) ? null : userAudio)
                    .build();

            RequestBody body = RequestBody.create(
                    okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsBytes(reqDTO));

            Request request = new Request.Builder()
                    .url(llmHostName + llmWebhookUri)
                    .post(body)
                    .build();

            Response response = client.newCall(request).execute();
            String responseString = response.body().string();
            long duration = Duration.between(start, Instant.now()).toMillis();
            log.info("=== Get response LLM conversation: {}", responseString);

            ObjectNode data = JsonNodeFactory.instance.objectNode();
            data.set("request", objectMapper.valueToTree(reqDTO));
            data.set("response", objectMapper.readTree(responseString));
            sharedService.saveConversationLog(conversationId, ConversationLogType.LLM_RESPONSE, objectMapper.writeValueAsString(data), duration, false);

            // send to datadog
            MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                    .logType(DatadogLogType.CONVERSATION_LLM_RESPONSE)
                    .robotId(robotId)
                    .statusCode(CodeDefine.OK)
                    .conversationId(conversationId)
                    .clientIp(null)
                    .durationMs(duration)
                    .message("OK")
                    .level("INFO")
                    .timestamp(Instant.now())
                    .build();
            datadogService.handleMonitoringLog(monitoringLog);

            LLMChatResDTO LLMChatResDTO = objectMapper.readValue(responseString, new TypeReference<>() {
            });

            if (LLMChatResDTO == null) {
                throw new RuntimeException("Response lỗi: " + responseString);
            }

            // Track LLM call success
            conversationFlowTracker.trackLLMEnd(conversationId.toString(), "CONVERSATION",
                    objectMapper.readTree(responseString), null, null, duration, true, null);

            return LLMChatResDTO;
        } catch (Exception e) {
            // Track LLM call error
            long duration = Duration.between(start, Instant.now()).toMillis();
            conversationFlowTracker.trackLLMEnd(conversationId.toString(), "CONVERSATION",
                    null, null, null, duration, false, e.getMessage());
            log.error("Error when get response LLM conversation", e);

            // send to datadog
            MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                    .logType(DatadogLogType.CONVERSATION_LLM_RESPONSE)
                    .robotId(robotId)
                    .statusCode(CodeDefine.SERVER_ERROR)
                    .conversationId(conversationId)
                    .clientIp(null)
                    .message("Error when init LLM conversation: " + e.getMessage())
                    .level("ERROR")
                    .timestamp(Instant.now())
                    .build();
            datadogService.handleMonitoringLog(monitoringLog);

            slackWarningSystemService.sendWarningSystemToSlack("Error when get response LLM conversation: " + e.getMessage(), ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            throw e;
        }
    }

    /**
     * Generate conversation summary by calling external API
     *
     * @param conversationData contain externalId and userId
     */
    public String generateConversationSummary(RobotUserConversation conversationData) {
        String conversationId = conversationData.getExternalConversationId();
        String userId = conversationData.getUserId();

        try {
            // Create HTTP client with timeouts
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, java.util.concurrent.TimeUnit.SECONDS)
                    .writeTimeout(20, java.util.concurrent.TimeUnit.SECONDS)
                    .readTimeout(20, java.util.concurrent.TimeUnit.SECONDS)
                    .build();

            String jsonBody = String.format(
                    "{\"conversation_id\":\"%s\",\"user_id\":\"%s\"}",
                    conversationId, userId
            );

            Request request = new Request.Builder()
                    .url(llmHostName + llmSummaryUri)
                    .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), jsonBody))
                    .addHeader("Content-Type", "application/json")
                    .build();

            // Execute request
            Response response = client.newCall(request).execute();

            if (response.isSuccessful()) {
                return response.body() != null ? response.body().string() : "";
            } else {
                String errorBody = response.body() != null ? response.body().string() : "No response body";
                logError(userId, new RuntimeException("HTTP " + response.code()),
                        "Failed to generate conversation summary. Response: " + errorBody);
                throw new RuntimeException("Failed to call external API to generate conversation summary. Response: " + errorBody);
            }

        } catch (Exception e) {
            logError(userId, e, "Error generating conversation summary for conversation: " + conversationId);
            throw new RuntimeException("Error generating conversation summary for conversation: " + conversationId + e.getMessage());
        }
    }

    public void extractFacts(String conversationId, String userId) {
        try {
            // Create HTTP client with timeouts
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, java.util.concurrent.TimeUnit.SECONDS)
                    .writeTimeout(20, java.util.concurrent.TimeUnit.SECONDS)
                    .readTimeout(20, java.util.concurrent.TimeUnit.SECONDS)
                    .build();
            ObjectNode requestBody = JsonNodeFactory.instance.objectNode();
            requestBody.put("conversation_id", conversationId);
            requestBody.put("user_id", userId);

            Request request = new Request.Builder()
                    .url(extractFactHostName + extractFactUri)
                    .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsString(requestBody)))
                    .addHeader("Content-Type", "application/json")
                    .build();

            // Execute request
            Response response = client.newCall(request).execute();
            log.info("=== Extract facts for conversation: {}, url: {}", extractFactHostName + extractFactUri, response.body().string());
        } catch (Exception e) {
            logError(userId, e, "Error extract facts for conversation: " + conversationId);
        }
    }

    protected void logError(String userId, Exception e, String message) {
        log.error("Client: {} - {}", userId, message, e);
    }
}
