package com.stepup.springrobot.service;
import com.stepup.springrobot.dto.chat.FullTestLessonReqDTO;
import com.stepup.springrobot.dto.chat.FullTestLessonResDTO;
import com.stepup.springrobot.dto.chat.SingleRandomTestReqDTO;
import com.stepup.springrobot.dto.chat.SingleRandomTestResDTO;
import com.stepup.springrobot.dto.chat.TestFlowResultDTO;
import com.stepup.springrobot.dto.chat.WorkflowDetailDTO;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;
import java.util.Map;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.ArrayList;

@Service
@RequiredArgsConstructor
@Log4j2
public class TestExecutionService {

    private final TestLessonService testLessonService;
    private final AlertWebhookService alertWebhookService;

    @Async("taskExecutor")
    public void executeTestAsync(Long botId) {
        FullTestLessonReqDTO requestDto = FullTestLessonReqDTO.builder()
                .botId(botId)
                .userId("TEST USER")
                .build();
        try {
            alertWebhookService.sendNotify(String.format("Bắt đầu chạy test cho bot's id %s", botId));

            // Execute long-running test
            FullTestLessonResDTO result = testLessonService.executeFullTestLesson(requestDto);

            String conversationIds = result.getTestResults() != null ? result.getTestResults().stream()
                    .map(TestFlowResultDTO::getConversationId)
                    .filter(id -> id != null)
                    .map(String::valueOf)
                    .reduce((a,b) -> a + ", " + b)
                    .orElse("(none)") : "(none)";

            // Generate conversation details
            String conversationDetails = generateConversationDetails(result);

            String completionMsg = String.format(
                    "Hoàn thành test cho bot's id %s\n" +
                    "- Tổng kịch bản: %d\n" +
                    "- Hoàn thành: %d\n" +
                    "- Thất bại: %d\n" +
                    "- Tỉ lệ thành công: %.2f%%\n" +
                    "- Tổng lượt tương tác: %d\n" +
                    "- Thời gian thực thi (s): %d\n" +
                    "- Thời gian phản hồi TB (ms): %.2f\n" +
                    "- Tổng tokens: %d\n" +
                    "- Conversation IDs: %s\n\n" +
                    "%s",
                    botId,
                    result.getTotalTestFlows(),
                    result.getSummary() != null ? result.getSummary().getSuccessfulFlows() : 0,
                    result.getSummary() != null ? result.getSummary().getFailedFlows() : 0,
                    result.getSummary() != null ? result.getSummary().getSuccessRatePercentage() : 0.0,
                    result.getSummary() != null ? result.getSummary().getTotalInteractions() : 0,
                    result.getSummary() != null ? result.getSummary().getTotalExecutionTimeSeconds() : 0,
                    result.getSummary() != null ? result.getSummary().getAverageResponseTimeMs() : 0.0,
                    result.getSummary() != null ? result.getSummary().getTotalTokensUsed() : 0,
                    conversationIds,
                    conversationDetails
            );
            alertWebhookService.sendNotify(completionMsg);
        } catch (Exception ex) {
            log.error("Lỗi khi chạy test cho bot's id {} {}", botId, ex);
            alertWebhookService.sendError(String.format("Lỗi test cho bot's id %s\nChi tiết: %s", botId, safeMessage(ex)));
        }
    }

    @Async
    public void executeSingleRandomTestAsync(Long botId) {
        SingleRandomTestReqDTO requestDto = SingleRandomTestReqDTO.builder()
                .botId(botId)
                .userId("TEST USER")
                .build();
        try {
            alertWebhookService.sendNotify(String.format("Bắt đầu chạy random test cho bot's id %s", botId));

            // Execute single random test
            SingleRandomTestResDTO result = testLessonService.executeSingleRandomTest(requestDto);

            String randomIntents = result.getRandomIntentsSelected() != null ? 
                    String.join(", ", result.getRandomIntentsSelected()) : "(none)";

            // Generate conversation details for single test
            String conversationDetails = generateSingleRandomConversationDetails(result);

            String completionMsg = String.format(
                    "Hoàn thành random test cho bot's id %s\n" +
                    "- Conversation ID: %s\n" +
                    "- Trạng thái: %s\n" +
                    "- Tổng lượt tương tác: %d\n" +
                    "- Thời gian thực thi (s): %d\n" +
                    "- Random intents đã test: %s\n\n" +
                    "%s",
                    botId,
                    result.getConversationId() != null ? result.getConversationId().toString() : "(none)",
                    Boolean.TRUE.equals(result.getCompletedSuccessfully()) ? "Thành công" : "Thất bại",
                    result.getTotalInteractions() != null ? result.getTotalInteractions() : 0,
                    result.getExecutionTimeSeconds() != null ? result.getExecutionTimeSeconds() : 0,
                    randomIntents,
                    conversationDetails
            );
            alertWebhookService.sendNotify(completionMsg);
        } catch (Exception ex) {
            log.error("Lỗi khi chạy random test cho bot's id {} {}", botId, ex);
            alertWebhookService.sendError(String.format("Lỗi random test cho bot's id %s\nChi tiết: %s", botId, safeMessage(ex)));
        }
    }

    private String generateSingleRandomConversationDetails(SingleRandomTestResDTO result) {
        if (result.getConversationId() == null) {
            return "Conversation Detail: (none)";
        }

        StringBuilder details = new StringBuilder("Conversation Detail:\n");
        details.append(result.getConversationId()).append(":\n");
        
        // Check if we have execution history from workflow details (preferred)
        if (result.getWorkflowDetails() != null && !result.getWorkflowDetails().isEmpty()) {
            for (WorkflowDetailDTO workflowDetail : result.getWorkflowDetails()) {
                details.append(workflowDetail.getWorkflowId()).append(": [");
                
                if (workflowDetail.getExecutionHistory() != null && !workflowDetail.getExecutionHistory().isEmpty()) {
                    // Show execution history in question order (do NOT group by intent name)
                    String intentsList = workflowDetail.getExecutionHistory().stream()
                            .map(record -> record.getIntentName() + " (" + record.getExecutionCount() + ")")
                            .collect(Collectors.joining(", "));
                    details.append(intentsList);
                }
                
                details.append("]\n");
            }
        } else if (result.getRandomFlowDetails() != null && !result.getRandomFlowDetails().isEmpty()) {
            // Fallback to old random flow details format
            List<SingleRandomTestResDTO.RandomIntentDetailDTO> flowDetails = result.getRandomFlowDetails();
            
            if (!flowDetails.isEmpty()) {
                Long currentWorkflowId = flowDetails.get(0).getWorkflowId();
                List<SingleRandomTestResDTO.RandomIntentDetailDTO> currentWorkflowIntents = new ArrayList<>();
                
                for (SingleRandomTestResDTO.RandomIntentDetailDTO intentDetail : flowDetails) {
                    if (!intentDetail.getWorkflowId().equals(currentWorkflowId)) {
                        // Workflow changed - output current workflow segment
                        if (!currentWorkflowIntents.isEmpty()) {
                            details.append(currentWorkflowId).append(": [");
                            // Group intents by name and sum their loop counts (expected, not actual)
                            Map<String, Integer> intentCounts = new LinkedHashMap<>();
                            for (SingleRandomTestResDTO.RandomIntentDetailDTO intent : currentWorkflowIntents) {
                                intentCounts.merge(intent.getIntentName(), intent.getLoopCount(), Integer::sum);
                            }
                            
                            String intentsList = intentCounts.entrySet().stream()
                                    .map(entry -> entry.getKey() + " (" + entry.getValue() + ")")
                                    .collect(Collectors.joining(", "));
                            details.append(intentsList);
                            details.append("]\n");
                        }
                        
                        // Start new workflow segment
                        currentWorkflowId = intentDetail.getWorkflowId();
                        currentWorkflowIntents = new ArrayList<>();
                    }
                    
                    currentWorkflowIntents.add(intentDetail);
                }
                
                // Output the last workflow segment
                if (!currentWorkflowIntents.isEmpty()) {
                    details.append(currentWorkflowId).append(": [");
                    // Group intents by name and sum their loop counts
                    Map<String, Integer> intentCounts = new LinkedHashMap<>();
                    for (SingleRandomTestResDTO.RandomIntentDetailDTO intent : currentWorkflowIntents) {
                        intentCounts.merge(intent.getIntentName(), intent.getLoopCount(), Integer::sum);
                    }
                    
                    String intentsList = intentCounts.entrySet().stream()
                            .map(entry -> entry.getKey() + " (" + entry.getValue() + ")")
                            .collect(Collectors.joining(", "));
                    details.append(intentsList);
                    details.append("]\n");
                }
            }
        }
        
        return details.toString().trim();
    }

    private String generateConversationDetails(FullTestLessonResDTO result) {
        if (result.getTestResults() == null || result.getTestResults().isEmpty()) {
            return "Conversation Detail: (none)";
        }

        StringBuilder details = new StringBuilder("Conversation Detail:\n");
        
        for (TestFlowResultDTO testResult : result.getTestResults()) {
            if (testResult.getConversationId() != null && testResult.getWorkflowDetails() != null && !testResult.getWorkflowDetails().isEmpty()) {
                details.append(testResult.getConversationId()).append(":\n");
                
                for (WorkflowDetailDTO workflowDetail : testResult.getWorkflowDetails()) {
                    details.append(workflowDetail.getWorkflowId()).append(": [");
                    
                    // Prefer execution history if available, fall back to tested intents
                    if (workflowDetail.getExecutionHistory() != null && !workflowDetail.getExecutionHistory().isEmpty()) {
                        // Show execution history in question order (do NOT group by intent name)
                        // Each question should show separately even if intent names are duplicate
                        String intentsList = workflowDetail.getExecutionHistory().stream()
                                .map(record -> record.getIntentName() + " (" + record.getExecutionCount() + ")")
                                .collect(Collectors.joining(", "));
                        details.append(intentsList);
                    } else if (workflowDetail.getTestedIntents() != null && !workflowDetail.getTestedIntents().isEmpty()) {
                        // Fallback to old format for backward compatibility
                        Map<String, Integer> intentCounts = new LinkedHashMap<>();
                        for (WorkflowDetailDTO.IntentWithLoopCount intent : workflowDetail.getTestedIntents()) {
                            intentCounts.merge(intent.getIntentName(), intent.getLoopCount(), Integer::sum);
                        }
                        
                        String intentsList = intentCounts.entrySet().stream()
                                .map(entry -> entry.getKey() + " (" + entry.getValue() + ")")
                                .collect(Collectors.joining(", "));
                        details.append(intentsList);
                    }
                    
                    details.append("]\n");
                }
                details.append("\n");
            }
        }
        
        return details.toString().trim();
    }

    private String safeMessage(Exception ex) {
        String message = ex.getMessage();
        if (message == null || message.isBlank()) {
            return ex.getClass().getName();
        }
        return message;
    }

    @Data
    private static class TextBody {
        public final String text;
        private TextBody(String text) { this.text = text; }
    }
}
