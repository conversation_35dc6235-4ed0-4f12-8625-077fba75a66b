package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.dto.monitoring.MonitoringLogDTO;
import com.stepup.springrobot.dto.monitoring.TraceContextDTO;
import com.stepup.springrobot.dto.monitoring.UserJourneyDTO;
import com.stepup.springrobot.model.DatadogLogType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class UserJourneyTracker {

    @Autowired
    private DatadogService datadogService;

    @Autowired
    private TraceContextService traceContextService;

    @Autowired
    private ObjectMapper objectMapper;

    // Store active user journeys
    private final Map<String, UserJourneyContext> activeJourneys = new ConcurrentHashMap<>();

    // Store user journey history for analysis
    private final Map<String, List<UserJourneyDTO>> userJourneyHistory = new ConcurrentHashMap<>();

    /**
     * Start a new user journey
     */
    public String startUserJourney(String userId, String sessionId, UserJourneyDTO.JourneyType journeyType,
                                   String robotId, Map<String, Object> initialContext) {
        try {
            String journeyId = generateJourneyId(userId, sessionId);

            UserJourneyContext context = UserJourneyContext.builder()
                    .journeyId(journeyId)
                    .userId(userId)
                    .sessionId(sessionId)
                    .robotId(robotId)
                    .journeyType(journeyType)
                    .startTime(Instant.now())
                    .currentStage(UserJourneyDTO.JourneyStage.WEBSOCKET_CONNECT)
                    .stageTimings(new HashMap<>())
                    .contextData(initialContext != null ? new HashMap<>(initialContext) : new HashMap<>())
                    .stageHistory(new ArrayList<>())
                    .traceContext(traceContextService.getTraceContext())
                    .build();

            activeJourneys.put(journeyId, context);

            // Log journey start
            trackJourneyStage(journeyId, UserJourneyDTO.JourneyStage.WEBSOCKET_CONNECT,
                    "SUCCESS", null, null, null);

            log.info("Started user journey: userId={}, journeyId={}, type={}",
                    userId, journeyId, journeyType);

            return journeyId;

        } catch (Exception e) {
            log.error("Error starting user journey for user: {}", userId, e);
            return null;
        }
    }

    /**
     * Track a specific journey stage
     */
    public void trackJourneyStage(String journeyId, UserJourneyDTO.JourneyStage stage, String status,
                                  Object userInput, Object systemResponse, String errorMessage) {
        try {
            UserJourneyContext context = activeJourneys.get(journeyId);
            if (context == null) {
                log.warn("Journey context not found: {}", journeyId);
                return;
            }

            Instant now = Instant.now();
            Instant stageStart = context.getStageTimings().get(stage);
            Long durationMs = null;

            if (stageStart == null) {
                // Start timing for this stage
                context.getStageTimings().put(stage, now);
                stageStart = now;
            } else {
                // Calculate duration
                durationMs = now.toEpochMilli() - stageStart.toEpochMilli();
            }

            UserJourneyDTO journeyDTO = UserJourneyDTO.builder()
                    .userId(context.getUserId())
                    .sessionId(context.getSessionId())
                    .journeyId(journeyId)
                    .journeyType(context.getJourneyType())
                    .journeyStage(stage)
                    .stageName(stage.getStageName())
                    .robotId(context.getRobotId())
                    .conversationId(context.getConversationId())
                    .startTime(stageStart)
                    .endTime(now)
                    .durationMs(durationMs)
                    .status(status)
                    .errorMessage(errorMessage)
                    .previousStage(context.getCurrentStage())
                    .userInput(userInput)
                    .systemResponse(systemResponse)
                    .contextData(new HashMap<>(context.getContextData()))
                    .timestamp(now)
                    .build();

            // Update context
            context.setCurrentStage(stage);
            context.getStageHistory().add(journeyDTO);

            // Add to user history
            userJourneyHistory.computeIfAbsent(context.getUserId(), k -> new ArrayList<>()).add(journeyDTO);

            // Send to Datadog
            sendJourneyLogToDatadog(journeyDTO, context);

        } catch (Exception e) {
            log.error("Error tracking journey stage: journeyId={}, stage={}", journeyId, stage, e);
        }
    }

    /**
     * Track authentication events
     */
    public void trackAuthentication(String userId, String sessionId, boolean success, String errorMessage) {
        String journeyId = findOrCreateJourney(userId, sessionId, UserJourneyDTO.JourneyType.AUTHENTICATION);
        UserJourneyDTO.JourneyStage stage = success ?
                UserJourneyDTO.JourneyStage.AUTH_SUCCESS : UserJourneyDTO.JourneyStage.AUTH_FAILED;

        trackJourneyStage(journeyId, stage, success ? "SUCCESS" : "ERROR",
                null, null, errorMessage);
    }

    /**
     * Track conversation events
     */
    public void trackConversationStart(String userId, String sessionId, String conversationId, String robotId) {
        String journeyId = findOrCreateJourney(userId, sessionId, UserJourneyDTO.JourneyType.CONVERSATION);

        UserJourneyContext context = activeJourneys.get(journeyId);
        if (context != null) {
            context.setConversationId(conversationId);
            context.setRobotId(robotId);
        }

        trackJourneyStage(journeyId, UserJourneyDTO.JourneyStage.CONVERSATION_START,
                "SUCCESS", null, null, null);
    }

    public void trackAudioUpload(String userId, String sessionId, int audioSize, String format) {
        String journeyId = findActiveJourney(userId, sessionId);
        if (journeyId != null) {
            Map<String, Object> audioData = new HashMap<>();
            audioData.put("size_bytes", audioSize);
            audioData.put("format", format);

            trackJourneyStage(journeyId, UserJourneyDTO.JourneyStage.AUDIO_UPLOAD,
                    "SUCCESS", audioData, null, null);
        }
    }

    public void trackASRProcessing(String userId, String sessionId, String provider, boolean success,
                                   String transcript, String errorMessage) {
        String journeyId = findActiveJourney(userId, sessionId);
        if (journeyId != null) {
            Map<String, Object> asrData = new HashMap<>();
            asrData.put("provider", provider);
            if (transcript != null) asrData.put("transcript", transcript);

            trackJourneyStage(journeyId, UserJourneyDTO.JourneyStage.ASR_PROCESSING,
                    success ? "SUCCESS" : "ERROR", null, asrData, errorMessage);
        }
    }

    public void trackLLMProcessing(String userId, String sessionId, String model, boolean success,
                                   String response, String errorMessage) {
        String journeyId = findActiveJourney(userId, sessionId);
        if (journeyId != null) {
            Map<String, Object> llmData = new HashMap<>();
            llmData.put("model", model);
            if (response != null) llmData.put("response", response);

            trackJourneyStage(journeyId, UserJourneyDTO.JourneyStage.LLM_PROCESSING,
                    success ? "SUCCESS" : "ERROR", null, llmData, errorMessage);
        }
    }

    /**
     * End user journey
     */
    public void endUserJourney(String journeyId, boolean success, String reason) {
        try {
            UserJourneyContext context = activeJourneys.get(journeyId);
            if (context == null) {
                log.warn("Journey context not found for ending: {}", journeyId);
                return;
            }

            context.setEndTime(Instant.now());
            context.setTotalDurationMs(context.getEndTime().toEpochMilli() - context.getStartTime().toEpochMilli());

            // Track final stage
            trackJourneyStage(journeyId, UserJourneyDTO.JourneyStage.CONVERSATION_END,
                    success ? "SUCCESS" : "ERROR", null, null, reason);

            // Remove from active journeys
            activeJourneys.remove(journeyId);

            log.info("Ended user journey: journeyId={}, success={}, duration={}ms",
                    journeyId, success, context.getTotalDurationMs());

        } catch (Exception e) {
            log.error("Error ending user journey: {}", journeyId, e);
        }
    }

    /**
     * Get user journey history
     */
    public List<UserJourneyDTO> getUserJourneyHistory(String userId, int limit) {
        List<UserJourneyDTO> history = userJourneyHistory.get(userId);
        if (history == null) return new ArrayList<>();

        // Return most recent entries
        int fromIndex = Math.max(0, history.size() - limit);
        return new ArrayList<>(history.subList(fromIndex, history.size()));
    }

    /**
     * Get active journey for user
     */
    public UserJourneyContext getActiveJourney(String userId, String sessionId) {
        return activeJourneys.values().stream()
                .filter(context -> userId.equals(context.getUserId()) && sessionId.equals(context.getSessionId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * Find or create journey
     */
    private String findOrCreateJourney(String userId, String sessionId, UserJourneyDTO.JourneyType journeyType) {
        UserJourneyContext existing = getActiveJourney(userId, sessionId);
        if (existing != null) {
            return existing.getJourneyId();
        }

        return startUserJourney(userId, sessionId, journeyType, null, null);
    }

    /**
     * Find active journey ID
     */
    private String findActiveJourney(String userId, String sessionId) {
        UserJourneyContext context = getActiveJourney(userId, sessionId);
        return context != null ? context.getJourneyId() : null;
    }

    /**
     * Generate unique journey ID
     */
    private String generateJourneyId(String userId, String sessionId) {
        return "journey_" + userId + "_" + sessionId + "_" + System.currentTimeMillis();
    }

    /**
     * Send journey log to Datadog
     */
    @Async
    protected void sendJourneyLogToDatadog(UserJourneyDTO journeyDTO, UserJourneyContext context) {
        try {
            MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                    .logType(DatadogLogType.USER_JOURNEY_TRACKING)
                    .message(String.format("User Journey: %s - %s - %s",
                            journeyDTO.getUserId(),
                            journeyDTO.getJourneyStage().getStageName(),
                            journeyDTO.getStatus()))
                    .level("ERROR".equals(journeyDTO.getStatus()) ? "ERROR" : "INFO")
                    .timestamp(journeyDTO.getTimestamp())
                    .serviceName("user-journey-tracker")
                    .traceContext(context.getTraceContext())
                    .durationMs(journeyDTO.getDurationMs())
                    .errorMessage(journeyDTO.getErrorMessage())
                    .requestData(journeyDTO.getUserInput() != null ?
                            objectMapper.valueToTree(journeyDTO.getUserInput()) : null)
                    .responseData(journeyDTO.getSystemResponse() != null ?
                            objectMapper.valueToTree(journeyDTO.getSystemResponse()) : null)
                    .metadata(createJourneyMetadata(journeyDTO, context))
                    .build();

            datadogService.sendLogToDatadog(monitoringLog);

        } catch (Exception e) {
            log.error("Error sending journey log to Datadog: {}", e.getMessage(), e);
        }
    }

    /**
     * Create metadata for journey logging
     */
    private Map<String, Object> createJourneyMetadata(UserJourneyDTO journeyDTO, UserJourneyContext context) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("user_id", journeyDTO.getUserId());
        metadata.put("session_id", journeyDTO.getSessionId());
        metadata.put("journey_id", journeyDTO.getJourneyId());
        metadata.put("journey_type", journeyDTO.getJourneyType().getTypeName());
        metadata.put("journey_stage", journeyDTO.getJourneyStage().getStageName());
        metadata.put("robot_id", journeyDTO.getRobotId());
        metadata.put("conversation_id", journeyDTO.getConversationId());
        metadata.put("previous_stage", journeyDTO.getPreviousStage() != null ?
                journeyDTO.getPreviousStage().getStageName() : null);

        // Add context data
        if (journeyDTO.getContextData() != null) {
            metadata.put("context_data", journeyDTO.getContextData());
        }

        // Add journey statistics
        metadata.put("total_stages_completed", context.getStageHistory().size());
        metadata.put("journey_duration_ms", context.getTotalDurationMs());

        return metadata;
    }

    /**
     * Internal context class for tracking journey state
     */
    @lombok.Data
    @lombok.Builder
    public static class UserJourneyContext {
        private String journeyId;
        private String userId;
        private String sessionId;
        private String robotId;
        private String conversationId;
        private UserJourneyDTO.JourneyType journeyType;
        private UserJourneyDTO.JourneyStage currentStage;
        private Instant startTime;
        private Instant endTime;
        private Long totalDurationMs;
        private Map<UserJourneyDTO.JourneyStage, Instant> stageTimings;
        private Map<String, Object> contextData;
        private List<UserJourneyDTO> stageHistory;
        private TraceContextDTO traceContext;
    }
}
