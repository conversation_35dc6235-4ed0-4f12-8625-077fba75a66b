package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.dto.monitoring.ExternalApiCallDTO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ExternalApiCallMonitor {

    @Autowired
    private DatadogService datadogService;

    @Autowired
    private ObjectMapper objectMapper;

    public Response executeMonitoredCall(String apiName, Request request) throws IOException {
        return executeMonitoredCall(apiName, request, 20000L, 0);
    }

    public Response executeMonitoredCall(String apiName, Request request, Long timeoutMs, int retryCount) throws IOException {
        Instant startTime = Instant.now();

        // Create OkHttp client with monitoring
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(timeoutMs != null ? timeoutMs : 20000L, TimeUnit.MILLISECONDS)
                .writeTimeout(timeoutMs != null ? timeoutMs : 20000L, TimeUnit.MILLISECONDS)
                .readTimeout(timeoutMs != null ? timeoutMs : 20000L, TimeUnit.MILLISECONDS)
                .addInterceptor(new MonitoringOkHttpInterceptor(apiName))
                .build();

        Response response = null;
        Exception lastException = null;

        try {
            // Log API call start
            logApiCallStart(apiName, request, timeoutMs, retryCount);

            response = client.newCall(request).execute();

            // Log successful API call
            logApiCallSuccess(apiName, request, response, startTime, timeoutMs, retryCount);

            return response;

        } catch (Exception e) {
            lastException = e;

            // Log failed API call
            logApiCallError(apiName, request, response, e, startTime, timeoutMs, retryCount);

            throw e;
        }
    }

    private void logApiCallStart(String apiName, Request request, Long timeoutMs, int retryCount) {
        try {
            Map<String, String> headers = new HashMap<>();
            if (request.headers() != null) {
                request.headers().names().forEach(name ->
                        headers.put(name, request.header(name)));
            }

            JsonNode requestBody = null;
            if (request.body() != null) {
                // Note: This is a simplified approach. In production, you might want to
                // implement a more sophisticated way to capture request body without consuming it
                requestBody = objectMapper.createObjectNode().put("body_type", request.body().contentType().toString());
            }

            ExternalApiCallDTO apiCall = ExternalApiCallDTO.builder()
                    .apiName(apiName)
                    .url(request.url().toString())
                    .method(request.method())
                    .requestHeaders(headers)
                    .requestBody(requestBody)
                    .timeoutMs(timeoutMs)
                    .retryCount(retryCount)
                    .timestamp(Instant.now())
                    .success(null) // Not determined yet
                    .build();

            log.debug("Starting external API call to {}: {} {}", apiName, request.method(), request.url());

        } catch (Exception e) {
            log.error("Error logging API call start: {}", e.getMessage(), e);
        }
    }

    private void logApiCallSuccess(String apiName, Request request, Response response,
                                   Instant startTime, Long timeoutMs, int retryCount) {
        try {
            long durationMs = Instant.now().toEpochMilli() - startTime.toEpochMilli();

            Map<String, String> requestHeaders = new HashMap<>();
            if (request.headers() != null) {
                request.headers().names().forEach(name ->
                        requestHeaders.put(name, request.header(name)));
            }

            Map<String, String> responseHeaders = new HashMap<>();
            if (response.headers() != null) {
                response.headers().names().forEach(name ->
                        responseHeaders.put(name, response.header(name)));
            }

            JsonNode requestBody = null;
            if (request.body() != null) {
                requestBody = objectMapper.createObjectNode().put("content_type", request.body().contentType().toString());
            }

            JsonNode responseBody = null;
            if (response.body() != null) {
                // Note: Be careful with response body consumption
                responseBody = objectMapper.createObjectNode()
                        .put("content_type", response.body().contentType() != null ? response.body().contentType().toString() : "unknown")
                        .put("content_length", response.body().contentLength());
            }

            ExternalApiCallDTO apiCall = ExternalApiCallDTO.builder()
                    .apiName(apiName)
                    .url(request.url().toString())
                    .method(request.method())
                    .requestHeaders(requestHeaders)
                    .requestBody(requestBody)
                    .responseStatus(response.code())
                    .responseHeaders(responseHeaders)
                    .responseBody(responseBody)
                    .durationMs(durationMs)
                    .timeoutMs(timeoutMs)
                    .retryCount(retryCount)
                    .timestamp(startTime)
                    .success(response.isSuccessful())
                    .build();

            datadogService.logExternalApiCall(apiCall);

        } catch (Exception e) {
            log.error("Error logging API call success: {}", e.getMessage(), e);
        }
    }

    private void logApiCallError(String apiName, Request request, Response response, Exception error,
                                 Instant startTime, Long timeoutMs, int retryCount) {
        try {
            long durationMs = Instant.now().toEpochMilli() - startTime.toEpochMilli();

            Map<String, String> requestHeaders = new HashMap<>();
            if (request.headers() != null) {
                request.headers().names().forEach(name ->
                        requestHeaders.put(name, request.header(name)));
            }

            Map<String, String> responseHeaders = new HashMap<>();
            if (response != null && response.headers() != null) {
                response.headers().names().forEach(name ->
                        responseHeaders.put(name, response.header(name)));
            }

            JsonNode requestBody = null;
            if (request.body() != null) {
                requestBody = objectMapper.createObjectNode().put("content_type", request.body().contentType().toString());
            }

            ExternalApiCallDTO apiCall = ExternalApiCallDTO.builder()
                    .apiName(apiName)
                    .url(request.url().toString())
                    .method(request.method())
                    .requestHeaders(requestHeaders)
                    .requestBody(requestBody)
                    .responseStatus(response != null ? response.code() : null)
                    .responseHeaders(responseHeaders)
                    .durationMs(durationMs)
                    .timeoutMs(timeoutMs)
                    .retryCount(retryCount)
                    .errorMessage(error.getMessage())
                    .timestamp(startTime)
                    .success(false)
                    .build();

            datadogService.logExternalApiCall(apiCall);

        } catch (Exception e) {
            log.error("Error logging API call error: {}", e.getMessage(), e);
        }
    }

    private class MonitoringOkHttpInterceptor implements Interceptor {
        private final String apiName;

        public MonitoringOkHttpInterceptor(String apiName) {
            this.apiName = apiName;
        }

        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();

            // Add tracing headers
            Request.Builder requestBuilder = request.newBuilder();
            requestBuilder.addHeader("X-API-Name", apiName);
            requestBuilder.addHeader("X-Request-ID", java.util.UUID.randomUUID().toString());

            return chain.proceed(requestBuilder.build());
        }
    }
}
