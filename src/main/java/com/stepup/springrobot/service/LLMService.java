package com.stepup.springrobot.service;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.llm.*;
import com.stepup.springrobot.dto.llm_conversation.UpdateUserProfileReqDTO;
import com.stepup.springrobot.exception.business.content.ContentNotFoundException;
import com.stepup.springrobot.exception.business.user.AnonymousAccessException;
import com.stepup.springrobot.model.chat.RobotUserConversation;
import com.stepup.springrobot.model.llm.LlmImage;
import com.stepup.springrobot.model.llm.LlmMood;
import com.stepup.springrobot.model.robot.RobotUser;
import com.stepup.springrobot.model.user.Profile;
import com.stepup.springrobot.model.user.ProfileVariable;
import com.stepup.springrobot.model.user.ProfileVariableInfo;
import com.stepup.springrobot.repository.auth.ProfileRepository;
import com.stepup.springrobot.repository.auth.ProfileVariableInfoRepository;
import com.stepup.springrobot.repository.auth.ProfileVariableRepository;
import com.stepup.springrobot.repository.auth.UserRepository;
import com.stepup.springrobot.repository.chat.RobotUserConversationRepository;
import com.stepup.springrobot.repository.llm.LlmImageRepository;
import com.stepup.springrobot.repository.llm.LlmMoodRepository;
import com.stepup.springrobot.repository.robot.RobotUserRepository;
import com.stepup.springrobot.security.JwtService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.WebClient;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LLMService extends CommonService {
    @Value("${llm_communicate_token}")
    private String llmToken;

    @Value("${retrieve_facts_host}")
    private String retrieveFactsHost;

    @Value("${retrieve_facts_uri}")
    private String retrieveFactsUri;

    @Autowired
    private RobotUserConversationRepository robotUserConversationRepository;

    @Autowired
    private RobotUserRepository robotUserRepository;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private ProfileVariableInfoRepository profileVariableInfoRepository;

    @Autowired
    private ProfileVariableRepository profileVariableRepository;

    @Autowired
    private LlmMoodRepository llmMoodRepository;

    @Autowired
    private LlmImageRepository llmImageRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private UserRepository userRepository;


    @Autowired
    private SlackWarningSystemService slackWarningSystemService;

    protected LLMService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public DataResponseDTO<?> getUserProfile(HttpServletRequest request, String conversationId, String token) {
        if (StringUtils.isEmpty(token) || !token.equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        RobotUserConversation conversation = robotUserConversationRepository.findByExternalConversationId(conversationId);
        if (conversation == null) {
            throw new ContentNotFoundException("hội thoại LLM", conversationId);
        }

        return handleGetUserProfile(conversation.getRobotId());
    }

    /**
     * @param robotId: robot_id, user_id hoặc phone_number
     * Ưu tiên kiểm tra input từ web mvp trước.
     * Case có số điện thoại ở cả MVP và app robot thì lấy profile variable info của id từ web mvp
     */
    public DataResponseDTO<?> handleGetUserProfile(String robotId) {
        String name = null;
        RobotUser robotUser = robotUserRepository.findFirstByRobotIdOrderByIdDesc(robotId).orElse(null);
        if (robotUser != null) {
            Profile profile = profileRepository.getCurrentProfileByUserId(robotUser.getUserId());
            if (profile != null) {
                name = profile.getName();
            }
        } else {
            robotId = robotUserConversationRepository.getMVPUserIdByPhoneOrMVPId(robotId);

            if (robotId == null) {
                robotId = userRepository.findByPhone(robotId).getId();
            }
        }

        ObjectNode objectNode = objectMapper.createObjectNode();
        if (name != null) {
            objectNode.put("name", name);
        }

        List<ProfileVariableInfo> profileVariableInfos = profileVariableInfoRepository.findByRobotIdOrderByCreatedAt(robotId);
        for (ProfileVariableInfo profileVariableInfo : profileVariableInfos) {
            objectNode.put(profileVariableInfo.getKey(), profileVariableInfo.getValue());
        }

        List<ProfileVariable> profileVariables = profileVariableRepository.findAll();
        for (ProfileVariable variable : profileVariables) {
            if (!objectNode.has(variable.getKey())) {
                objectNode.set(variable.getKey(), null);
            }
        }

        List<String> facts = retrieveFacts(robotId);
        objectNode.put("facts", objectMapper.valueToTree(facts));

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy user profile thành công", objectNode);
    }

    private List<String> retrieveFacts(String robotId) {
        try {
            // Create WebClient for HTTP request
            WebClient webClient = WebClient.builder()
                    .baseUrl(retrieveFactsHost)
                    .build();

            // Make GET request to retrieve facts
            String response = webClient.get()
                    .uri(uriBuilder -> uriBuilder
                            .path(retrieveFactsUri)
                            .queryParam("user_id", robotId)
                            .queryParam("limit", 1000)
                            .build())
                    .header("accept", "application/json")
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

            if (response == null) {
                log.warn("Empty response when retrieving facts for robotId: {}", robotId);
                return new ArrayList<>();
            }

            // Parse the JSON response
            FactsResponseDTO factsResponse = objectMapper.readValue(response, FactsResponseDTO.class);

            if (factsResponse == null || !"ok".equals(factsResponse.getStatus()) || factsResponse.getFacts() == null) {
                log.warn("Invalid response structure when retrieving facts for robotId: {}", robotId);
                return new ArrayList<>();
            }

            // Extract just the fact strings
            return factsResponse.getFacts().stream()
                    .map(FactDTO::getFact)
                    .filter(fact -> fact != null && !fact.trim().isEmpty())
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error retrieving facts for robotId: {}", robotId, e);
            return new ArrayList<>();
        }
    }

    // Inner DTOs for parsing the facts response
    private static class FactsResponseDTO {
        private String status;
        private List<FactDTO> facts;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public List<FactDTO> getFacts() {
            return facts;
        }

        public void setFacts(List<FactDTO> facts) {
            this.facts = facts;
        }
    }

    private static class FactDTO {
        private String id;
        @JsonProperty("user_id")
        private String userId;
        private String fact;
        @JsonProperty("conversation_id")
        private String conversationId;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getFact() {
            return fact;
        }

        public void setFact(String fact) {
            this.fact = fact;
        }

        public String getConversationId() {
            return conversationId;
        }

        public void setConversationId(String conversationId) {
            this.conversationId = conversationId;
        }
    }

    public DataResponseDTO<?> getUserProfileDescription(HttpServletRequest request, String token) {
        if (StringUtils.isEmpty(token) || !token.equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        List<ProfileVariable> profileVariables = profileVariableRepository.findAll();
        Map<String, String> variables = new HashMap<>();
        for (ProfileVariable variable : profileVariables) {
            variables.put(variable.getKey(), variable.getDescription());
        }

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy user profile description thành công",
                variables);
    }

    public DataResponseDTO<?> updateUserProfile(HttpServletRequest request, UpdateUserProfileReqDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getToken()) || !reqDTO.getToken().equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        RobotUserConversation conversation = robotUserConversationRepository.findByExternalConversationId(reqDTO.getConversationId());
        if (conversation == null) {
            throw new ContentNotFoundException("hội thoại LLM", reqDTO.getConversationId());
        }

        List<ProfileVariableInfo> profileVariableInfos = profileVariableInfoRepository.findByRobotIdOrderByCreatedAt(conversation.getRobotId());

        for (Map.Entry<String, String> variable : reqDTO.getData().entrySet()) {
            boolean exist = false;
            for (ProfileVariableInfo profileVariableInfo : profileVariableInfos) {
                if (profileVariableInfo.getKey().equals(variable.getKey())) {
                    profileVariableInfo.setValue(variable.getValue());
                    exist = true;
                    break;
                }
            }

            if (!exist) {
                profileVariableInfos.add(ProfileVariableInfo.builder()
                        .robotId(conversation.getRobotId())
                        .key(variable.getKey())
                        .value(variable.getValue())
                        .build());
            }
        }

        profileVariableInfoRepository.saveAll(profileVariableInfos);

        return new DataResponseDTO<>(CodeDefine.OK, "Update user profile thành công");
    }

    public DataResponseDTO<?> getLLMImageByBotId(HttpServletRequest request, String token, Long botId) {
        if (StringUtils.isEmpty(token) || !token.equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        List<LlmImage> llmImages = findLlmImageByBotId(botId);
        List<LlmImageDTO> llmImageDTOS = llmImages.stream().map(llmImage -> LlmImageDTO.builder()
                .image(llmImage.getImage())
                .description(llmImage.getDescription())
                .build()).collect(Collectors.toList());

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách image theo bot id",
                LlmImageResDTO.builder().images(llmImageDTOS).build());
    }

    private List<LlmImage> findLlmImageByBotId(Long botId) {
        RMapCache<String, String> conversationMapCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_LLM_IMAGE);
        String key = "bot_id_" + botId;
        if (conversationMapCache.containsKey(key)) {
            try {
                return objectMapper.readValue(conversationMapCache.get(key), new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("Lỗi lấy llm image theo botId từ Redis: {}", e.getMessage());
                conversationMapCache.removeAsync(key);
            }
        }

        List<LlmImage> llmImages = llmImageRepository.findByBotId(botId);
        if (!CollectionUtils.isEmpty(llmImages)) {
            try {
                conversationMapCache.putAsync(key, objectMapper.writeValueAsString(llmImages), CodeDefine.TTL_KEY_FIX_CONTENT, TimeUnit.DAYS);
            } catch (Exception e) {
                log.error("Lỗi lưu llm image theo botId vào Redis: {}", e.getMessage());
                conversationMapCache.removeAsync(key);
            }
        }

        return llmImages;
    }

    @Transactional
    public DataResponseDTO<?> saveLLMImagesByBotId(HttpServletRequest request, LlmImageReqDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getToken()) || !reqDTO.getToken().equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        llmImageRepository.deleteByBotId(reqDTO.getBotId());

        List<LlmImage> llmImages = reqDTO.getImages().stream()
                .map(image -> LlmImage.builder()
                        .image(image.getImage())
                        .description(image.getDescription())
                        .botId(reqDTO.getBotId())
                        .build())
                .collect(Collectors.toList());
        llmImageRepository.saveAll(llmImages);
        redissonClient.getMapCache(CodeDefine.REDIS_KEY_LLM_IMAGE).delete();

        return new DataResponseDTO<>(CodeDefine.OK, "Lưu danh sách image theo bot id thành công",
                LlmImageResDTO.builder().images(reqDTO.getImages()).build());
    }

    public DataResponseDTO<?> getLLMMood(HttpServletRequest request, String token) {
        if (StringUtils.isEmpty(token) || !token.equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        List<LlmMood> llmMoods = findLlmMood();
        List<LlmMoodDTO> llmImageDTOS = llmMoods.stream()
                .map(llmImage -> LlmMoodDTO.builder()
                        .moodName(llmImage.getMoodName())
                        .description(llmImage.getDescription())
                        .example(llmImage.getExample())
                        .build())
                .collect(Collectors.toList());

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách mood thành công",
                LlmMoodResDTO.builder().moods(llmImageDTOS).build());
    }

    private List<LlmMood> findLlmMood() {
        RBucket<String> conversationMapCache = redissonClient.getBucket(CodeDefine.REDIS_KEY_LLM_MOOD);
        if (conversationMapCache.isExists()) {
            try {
                return objectMapper.readValue(conversationMapCache.get(), new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("Lỗi lấy llm mood từ Redis: {}", e.getMessage());
                conversationMapCache.delete();
            }
        }

        List<LlmMood> llmMoods = llmMoodRepository.findAll();
        if (!CollectionUtils.isEmpty(llmMoods)) {
            try {
                conversationMapCache.setAsync(objectMapper.writeValueAsString(llmMoods), CodeDefine.TTL_KEY_FIX_CONTENT, TimeUnit.DAYS);
            } catch (Exception e) {
                log.error("Lỗi lưu llm mood vào Redis: {}", e.getMessage());
                conversationMapCache.delete();
            }
        }

        return llmMoods;
    }

    @Transactional
    public DataResponseDTO<?> saveLLMMood(HttpServletRequest request, LlmMoodReqDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getToken()) || !reqDTO.getToken().equals(llmToken)) {
            throw new AnonymousAccessException();
        }

        llmMoodRepository.truncateTable();

        List<LlmMood> llmMoods = reqDTO.getMoods().stream()
                .map(mood -> LlmMood.builder()
                        .moodName(mood.getMoodName())
                        .description(mood.getDescription())
                        .example(mood.getExample())
                        .build())
                .collect(Collectors.toList());
        llmMoodRepository.saveAll(llmMoods);
        redissonClient.getBucket(CodeDefine.REDIS_KEY_LLM_MOOD).delete();

        return new DataResponseDTO<>(CodeDefine.OK, "Lưu danh sách moods thành công",
                LlmMoodResDTO.builder().moods(reqDTO.getMoods()).build());
    }
}
