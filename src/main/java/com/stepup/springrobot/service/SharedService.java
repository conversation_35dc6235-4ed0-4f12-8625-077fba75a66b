package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.model.chat.ConversationLogType;
import com.stepup.springrobot.model.chat.RobotUserConversation;
import com.stepup.springrobot.model.personal.UserProfileHistory;
import com.stepup.springrobot.repository.auth.ProfileRepository;
import com.stepup.springrobot.repository.chat.RobotUserConversationRepository;
import com.stepup.springrobot.repository.personal.UserProfileHistoryRepository;
import io.sentry.Sentry;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Log4j2
@Service
public class SharedService {
    @Value("${api_speech_hostname}")
    private String apiSpeechHostname;

    @Value("${api_speech_uri_v1}")
    private String apiSpeechUriV1;

    @Value("${api_speech_token_v1}")
    private String apiSpeechTokenV1;

    @Autowired
    private ProfileRepository profileRepository;

    @Autowired
    private UserProfileHistoryRepository userProfileHistoryRepository;

    @Autowired
    private SlackWarningSystemService slackWarningSystemService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RobotUserConversationRepository robotUserConversationRepository;

    private final ScheduledExecutorService quickService = Executors.newScheduledThreadPool(20); // Creates a thread pool that reuses fixed number of threads(as specified by noOfThreads in this case).

    public void deselectNonCurrentProfile(String userId, String currentProfileId) {
        if (userId == null || currentProfileId == null) {
            return;
        }

        profileRepository.deselectNonCurrentProfile(userId, currentProfileId);
        saveNewUserProfileHistory(userId, currentProfileId);
    }

    public void setCurrentAndDeselectNonCurrentProfile(String userId, String currentProfileId) {
        if (userId == null || currentProfileId == null) {
            return;
        }

        profileRepository.setCurrenProfileAndDeselectNonCurrentProfile(userId, currentProfileId);
        saveNewUserProfileHistory(userId, currentProfileId);
    }

    public void saveNewUserProfileHistory(String userId, String profileId) {
        if (userId == null || profileId == null) {
            return;
        }

        userProfileHistoryRepository.save(UserProfileHistory.builder()
                .userId(userId)
                .profileId(profileId)
                .build());
    }

    public Object callCheckSpeechSingleByOkHttp(File objectFile, String textRefs) {
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, TimeUnit.SECONDS)
                    .writeTimeout(20, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build();

            RequestBody requestBody = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("audio-file", objectFile.getName(), RequestBody.create(okhttp3.MediaType.parse("application/octet-stream"), objectFile))
                    .addFormDataPart("token", apiSpeechTokenV1)
                    .addFormDataPart("text-refs", textRefs)
                    .build();

            Request request = new Request.Builder()
                    .url(apiSpeechHostname + apiSpeechUriV1)
                    .post(requestBody)
                    .build();

            Response response = client.newCall(request).execute();
            return objectMapper.readTree(response.body().string());
        } catch (Exception e) {
            String msgSlack = "(Check phát âm) Lỗi gọi Bot check phát âm, hostname:  " + apiSpeechHostname + apiSpeechUriV1 + " error: " + e.getMessage() + ", file name: " + objectFile.getName();
            slackWarningSystemService.sendWarningSystemToSlack(msgSlack, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            Sentry.captureException(e);
            return null;
        }
    }

    public void saveConversationLog(Long conversationId, ConversationLogType logType, String data, Long time, Boolean isSessionCompleted) {
        quickService.submit(() -> {
            RobotUserConversation aiUserConversation = robotUserConversationRepository.findById(conversationId).orElse(null);
            if (aiUserConversation == null) {
                return;
            }

            String serverLog = aiUserConversation.getServerLog();
            ObjectNode logNode;
            try {
                logNode = StringUtils.isEmpty(serverLog) ? JsonNodeFactory.instance.objectNode() : (ObjectNode) objectMapper.readTree(serverLog);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }

            ArrayNode logArray;
            if (!logNode.has(logType.getDescription())) {
                logArray = JsonNodeFactory.instance.arrayNode();
            } else {
                logArray = (ArrayNode) logNode.get(logType.getDescription());
            }

            ObjectNode dataNode = JsonNodeFactory.instance.objectNode();
            try {
                dataNode.set("data", objectMapper.readTree(data));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            dataNode.put("time", time);
            logArray.add(dataNode);

            logNode.set(logType.getDescription(), logArray);
            try {
                aiUserConversation.setServerLog(objectMapper.writeValueAsString(logNode));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }

            if (isSessionCompleted) {
                aiUserConversation.setCompletedTime(System.currentTimeMillis() - aiUserConversation.getCreatedAt().getTime());
            }

            robotUserConversationRepository.save(aiUserConversation);
        });
    }

    public void triggerConversationErrorLog(Long conversationId, ConversationLogType logType, String data, Long time) {

    }
}
