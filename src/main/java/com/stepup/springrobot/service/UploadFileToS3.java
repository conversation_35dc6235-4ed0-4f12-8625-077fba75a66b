package com.stepup.springrobot.service;

//snippet-sourcedescription:[PutObject.java demonstrates how to upload an object to an Amazon S3 bucket.]
//snippet-keyword:[SDK for Java 2.0]
//snippet-keyword:[Code Sample]
//snippet-service:[Amazon S3]
//snippet-sourcetype:[full-example]
//snippet-sourcedate:[2/6/2020]
//snippet-sourceauthor:[scmacdon-aws]
/*
Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
This file is licensed under the Apache License, Version 2.0 (the "License").
You may not use this file except in compliance with the License. A copy of
the License is located at
http://aws.amazon.com/apache2.0/
This file is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
*/

// snippet-start:[s3.java2.s3_object_upload.import]

import io.sentry.Sentry;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.file.Files;
// snippet-end:[s3.java2.s3_object_upload.import]

@Log4j2
@Service
public class UploadFileToS3 {
    private static S3Client s3;

    @Value("${s3.aws.bucket}")
    private String s3Bucket;

    public UploadFileToS3(@Value("${s3.aws.hostname}") String s3Hostname,
                          @Value("${s3.aws.access_key}") String s3AccessKey,
                          @Value("${s3.aws.secret}") String s3Secret,
                          @Value("${s3.aws.region}") String s3Region) {
        // Create the S3Client object
        Region region = Region.of(s3Region); // cmc is US_EAST_1;
        AwsBasicCredentials awsBasicCredentials = AwsBasicCredentials.create(s3AccessKey, s3Secret);
        s3 = S3Client.builder()
                .endpointOverride(URI.create(s3Hostname))
                .credentialsProvider(StaticCredentialsProvider.create(awsBasicCredentials))
                .region(region)
                .build();
    }

    // snippet-start:[s3.java2.s3_object_upload.main]
    public String putS3ObjectAudio(String bucketName, String objectKey, File file, String format) {
        if (StringUtils.isEmpty(format)) {
            format = "audio/mpeg";
        }

        try {
            //Put a file into the bucket
            PutObjectResponse response = s3.putObject(PutObjectRequest.builder()
                            .bucket(bucketName)
                            .key(objectKey)
//                            .acl(ObjectCannedACL.PUBLIC_READ)
                            .contentType(String.valueOf(MediaType.parseMediaType(format)))
                            .contentLength(file.length())
                            .build(),
                    RequestBody.fromBytes(readBytesFromFile(file)));

            return response.eTag();
        } catch (S3Exception e) {
            log.error("=======error when upload file audio to s3=========" + e.getMessage());
        }

        return null;
    }

    // snippet-start:[s3.java2.s3_object_upload.main]
    public String putS3ObjectWithoutType(String bucketName, String objectKey, File file) {
        try {
            // Auto-detect content type (e.g. image/png, image/jpeg)
            String contentType = Files.probeContentType(file.toPath());
            if (contentType == null) {
                contentType = "application/octet-stream"; // fallback
            }

            //Put a file into the bucket
            PutObjectResponse response = s3.putObject(PutObjectRequest.builder()
                            .bucket(bucketName)
                            .key(objectKey)
                            .contentType(contentType)
                            .contentLength(file.length())
                            .build(),
                    RequestBody.fromBytes(readBytesFromFile(file)));

            return response.eTag();
        } catch (Exception e) {
            log.error("=======error when upload file to s3=========" + e.getMessage());
        }

        return null;
    }

    private static byte[] readBytesFromFile(File file) {
        FileInputStream fileInputStream = null;
        byte[] bytesArray = null;

        try {
            bytesArray = new byte[(int) file.length()];

            //read file into bytes[]
            fileInputStream = new FileInputStream(file);
            fileInputStream.read(bytesArray);

        } catch (IOException e) {
            e.printStackTrace();
            Sentry.captureException(e);
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return bytesArray;
    }
    // snippet-end:[s3.java2.s3_object_upload.main]


    public void deleteS3Object(String bucketName, String objectKey) {
        try {
            //Put a file into the bucket
            s3.deleteObject(DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .build());
            log.info("==delete file from s3=========" + bucketName + "/" + objectKey);
        } catch (S3Exception e) {
            log.error("=======error when delete file from s3=========" + e.getMessage());
        }
    }

    public ListObjectsV2Response getS3Object(String bucketName, String objectKey) {
        try {
            return s3.listObjectsV2(ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(objectKey)
                    .maxKeys(Integer.MAX_VALUE)
                    .build());
        } catch (S3Exception e) {
            log.error("=======error when delete file from s3=========" + e.getMessage());
            return null;
        }
    }

    /**
     * Get ETag from S3 URL by sending HEAD request
     * @param url S3 URL format: https://[hostname]/[path]
     * @return ETag from response header or null if not found
     */
    public String getEtagByUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }

        try {
            URL urlObj = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
            conn.setRequestMethod("HEAD");
            
            String etag = conn.getHeaderField("ETag");
            conn.disconnect();
            
            return etag != null ? etag.replace("\"", "") : null;
        } catch (Exception e) {
            log.error("Error getting ETag from URL: " + e.getMessage());
            return null;
        }
    }
}