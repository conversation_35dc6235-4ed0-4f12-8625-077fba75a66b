package com.stepup.springrobot.service.data;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.services.sheets.v4.Sheets;
import com.google.api.services.sheets.v4.model.ValueRange;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.admin.MvpLessonReqDTO;
import com.stepup.springrobot.dto.ielts.ChatResDTO;
import com.stepup.springrobot.dto.learn.LessonScenarioDTO;
import com.stepup.springrobot.model.entrance_test.EntranceTestDetail;
import com.stepup.springrobot.model.entrance_test.EntranceTestDetailType;
import com.stepup.springrobot.model.learn.Lesson;
import com.stepup.springrobot.model.learn.LessonDetail;
import com.stepup.springrobot.model.learn.LessonDetailType;
import com.stepup.springrobot.model.onboarding.OnboardingAnswerType;
import com.stepup.springrobot.model.onboarding.OnboardingQuestion;
import com.stepup.springrobot.model.robot.RobotFirmwareVersion;
import com.stepup.springrobot.repository.entrance_test.EntranceTestDetailRepository;
import com.stepup.springrobot.repository.game.RobotFirmwareVersionRepository;
import com.stepup.springrobot.repository.learn.LessonDetailRepository;
import com.stepup.springrobot.repository.learn.LessonRepository;
import com.stepup.springrobot.repository.onboarding.OnboardingQuestionRepository;
import lombok.extern.log4j.Log4j2;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionInterceptor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static com.stepup.springrobot.config.ImportDataUtils.*;
import static com.stepup.springrobot.config.LambdaExceptionUtil.rethrowConsumer;

@Log4j2
@Service
public class ImportService extends ImportCommonService {
    @Value("${mvp_host}")
    private String mvpHost;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private LessonRepository lessonRepository;

    @Autowired
    private LessonDetailRepository lessonDetailRepository;

    @Autowired
    private OnboardingQuestionRepository onboardingQuestionRepository;

    @Autowired
    private EntranceTestDetailRepository entranceTestDetailRepository;

    @Autowired
    private RobotFirmwareVersionRepository robotFirmwareVersionRepository;

    @Transactional(rollbackFor = Exception.class)
    public void initCommunicateLessonData() throws Exception {
        try {
            lessonRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "lesson!A2:C", processCommunicateLessonSheet(), true);

        } catch (Exception e) {
            log.error("Throw exception when synced google lesson sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processCommunicateLessonSheet() throws Exception {
        class LessonColumn {
            public static final int ID = 0;
            public static final int TITLE = 1;
            public static final int IMAGE = 2;

            private LessonColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<Lesson> lessons = new ArrayList<>();
            for (List<Object> value : rows) {
                if (value == null || value.isEmpty()) {
                    continue;
                }

                long id = Long.parseLong(getRequiredField(value, LessonColumn.ID));
                String title = trimData(getRequiredField(value, LessonColumn.TITLE));
                String image = trimData(getRequiredField(value, LessonColumn.IMAGE));

                lessons.add(Lesson.builder()
                        .id(id)
                        .title(title)
                        .image(image)
                        .build());
            }

            lessonRepository.saveAll(lessons);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void initCommunicateLessonDetailData() throws Exception {
        try {
            lessonDetailRepository.truncateTable();
            Map<String, LessonScenarioDTO> scenarioDTOS = getScenarioFromSheet(SHEET_LEARN_DATA, "lesson_scenario!A2:C");

            processSheet(SHEET_LEARN_DATA, "detail_free_talk!A2:B", processCommunicateLessonDetailFreeTalk(scenarioDTOS), true);
            processSheet(SHEET_LEARN_DATA, "detail_listening!A2:D", processCommunicateLessonDetailListening(scenarioDTOS), true);
            processSheet(SHEET_LEARN_DATA, "detail_choose_picture!A2:B", processCommunicateLessonDetailChoosePicture(scenarioDTOS), true);
            processSheet(SHEET_LEARN_DATA, "detail_describe_picture!A2:B", processCommunicateLessonDetailDescribe(scenarioDTOS), true);


        } catch (Exception e) {
            log.error("Throw exception when synced google lesson sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    public Map<String, LessonScenarioDTO> getScenarioFromSheet(final String sheetId, final String range) throws Exception {
        class LessonScenarioColumn {
            public static final int LESSON_ID = 0;
            public static final int ORDER = 1;
            public static final int LESSON_DETAIL_ID = 2;

            private LessonScenarioColumn() {
            }
        }

        try {
            NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
            Sheets service = new Sheets.Builder(HTTP_TRANSPORT, JSON_FACTORY, getCredentials(HTTP_TRANSPORT, JSON_FACTORY)).setApplicationName(APPLICATION_NAME).build();
            ValueRange response = service.spreadsheets().values().get(sheetId, range).execute();

            List<List<Object>> rows = response.getValues();
            if (rows == null || rows.isEmpty()) {
                log.info("=====No data found in google sheets {} =====", range);
                throw new Exception("No data found in google sheets " + range);
            }

            log.info("=======> syncing data: " + rows.size());
            Map<String, LessonScenarioDTO> lessonScenarioDTOS = new HashMap<>();
            for (List<Object> value : rows) {
                if (value == null || value.isEmpty()) {
                    continue;
                }

                Long lessonId = Long.parseLong(getRequiredField(value, LessonScenarioColumn.LESSON_ID));
                String lessonDetailId = trimData(getRequiredField(value, LessonScenarioColumn.LESSON_DETAIL_ID));
                Double order = Double.parseDouble(getRequiredField(value, LessonScenarioColumn.ORDER));

                lessonScenarioDTOS.put(lessonDetailId, LessonScenarioDTO.builder()
                        .lessonId(lessonId)
                        .lessonDetailId(lessonDetailId)
                        .order(order)
                        .build());
            }

            return lessonScenarioDTOS;
        } catch (Exception e) {
            log.error("Throw exception when synced google sheets =============== {} {}", range, e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processCommunicateLessonDetailFreeTalk(Map<String, LessonScenarioDTO> communicateScenarioDTOS) throws Exception {
        class FreeTalkColumn {
            public static final int ID = 0;
            public static final int BOT_ID = 1;

            private FreeTalkColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<LessonDetail> communicateLessonDetails = new ArrayList<>();
            for (int rowIndex = 0; rowIndex < rows.size(); rowIndex++) {
                List<Object> value = rows.get(rowIndex);
                if (value == null || value.isEmpty()) {
                    continue;
                }

                try {
                    String id = getRequiredField(value, FreeTalkColumn.ID);
                    LessonScenarioDTO lessonScenarioDTO = communicateScenarioDTOS.get(id);
                    if (lessonScenarioDTO == null) {
                        continue;
                    }

                    Long botId = Long.parseLong(getRequiredField(value, FreeTalkColumn.BOT_ID));

                    ObjectNode data = JsonNodeFactory.instance.objectNode();
                    data.put("bot_id", botId);

                    communicateLessonDetails.add(LessonDetail.builder()
                            .id(id)
                            .data(data)
                            .type(LessonDetailType.FREE_TALK)
                            .order(lessonScenarioDTO.getOrder())
                            .lessonId(lessonScenarioDTO.getLessonId())
                            .build());
                } catch (Exception e) {
                    log.error("Error in sheet at row {}: {}", rowIndex + 2, e.getMessage());
                    throw e;
                }
            }

            lessonDetailRepository.saveAll(communicateLessonDetails);
        });
    }

    private Consumer<List<List<Object>>> processCommunicateLessonDetailListening(Map<String, LessonScenarioDTO> communicateScenarioDTOS) throws Exception {
        class ListeningColumn {
            public static final int ID = 0;
            public static final int INTRO = 1;
            public static final int AUDIO = 2;
            public static final int TRANSCRIPT = 3;

            private ListeningColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<LessonDetail> communicateLessonDetails = new ArrayList<>();
            for (int rowIndex = 0; rowIndex < rows.size(); rowIndex++) {
                List<Object> value = rows.get(rowIndex);
                if (value == null || value.isEmpty()) {
                    continue;
                }

                try {
                    String id = getRequiredField(value, ListeningColumn.ID);
                    String audio = getNullableField(value, ListeningColumn.AUDIO);
                    String introStr = getNullableField(value, ListeningColumn.INTRO);
                    JsonNode intro = !StringUtils.isEmpty(introStr) ? objectMapper.readTree(introStr) : null;
                    String transcript = getNullableField(value, ListeningColumn.TRANSCRIPT);
                    ObjectNode data = null;
                    if (!StringUtils.isEmpty(audio) && !StringUtils.isEmpty(transcript)) {
                        data = JsonNodeFactory.instance.objectNode();
                        data.put("audio", audio);
                        data.put("transcript", transcript);
                        data.set("intro", intro);
                    }

                    LessonScenarioDTO lessonScenarioDTO = communicateScenarioDTOS.get(id);
                    if (lessonScenarioDTO == null) {
                        continue;
                    }

                    communicateLessonDetails.add(LessonDetail.builder()
                            .id(id)
                            .type(LessonDetailType.LISTENING)
                            .order(lessonScenarioDTO.getOrder())
                            .lessonId(lessonScenarioDTO.getLessonId())
                            .data(data)
                            .build());
                } catch (Exception e) {
                    log.error("Error in sheet at row {}: {}", rowIndex + 2, e.getMessage());
                    throw e;
                }
            }

            lessonDetailRepository.saveAll(communicateLessonDetails);
        });
    }

    private Consumer<List<List<Object>>> processCommunicateLessonDetailChoosePicture(Map<String, LessonScenarioDTO> communicateScenarioDTOS) throws Exception {
        class FreeTalkColumn {
            public static final int ID = 0;
            public static final int DATA = 1;

            private FreeTalkColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<LessonDetail> communicateLessonDetails = new ArrayList<>();
            for (int rowIndex = 0; rowIndex < rows.size(); rowIndex++) {
                List<Object> value = rows.get(rowIndex);
                if (value == null || value.isEmpty()) {
                    continue;
                }

                try {
                    String id = getRequiredField(value, FreeTalkColumn.ID);
                    LessonScenarioDTO lessonScenarioDTO = communicateScenarioDTOS.get(id);
                    if (lessonScenarioDTO == null) {
                        continue;
                    }

                    JsonNode data = objectMapper.readTree(getRequiredField(value, FreeTalkColumn.DATA));

                    communicateLessonDetails.add(LessonDetail.builder()
                            .id(id)
                            .data(data)
                            .type(LessonDetailType.CHOOSE_PICTURE)
                            .order(lessonScenarioDTO.getOrder())
                            .lessonId(lessonScenarioDTO.getLessonId())
                            .build());
                } catch (Exception e) {
                    log.error("Error in sheet at row {}: {}", rowIndex + 2, e.getMessage());
                    throw e;
                }
            }

            lessonDetailRepository.saveAll(communicateLessonDetails);
        });
    }

    private Consumer<List<List<Object>>> processCommunicateLessonDetailDescribe(Map<String, LessonScenarioDTO> communicateScenarioDTOS) throws Exception {
        class FreeTalkColumn {
            public static final int ID = 0;
            public static final int DATA = 1;

            private FreeTalkColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<LessonDetail> communicateLessonDetails = new ArrayList<>();
            for (int rowIndex = 0; rowIndex < rows.size(); rowIndex++) {
                List<Object> value = rows.get(rowIndex);
                if (value == null || value.isEmpty()) {
                    continue;
                }

                try {
                    String id = getRequiredField(value, FreeTalkColumn.ID);
                    LessonScenarioDTO lessonScenarioDTO = communicateScenarioDTOS.get(id);
                    if (lessonScenarioDTO == null) {
                        continue;
                    }

                    JsonNode data = objectMapper.readTree(getRequiredField(value, FreeTalkColumn.DATA));

                    communicateLessonDetails.add(LessonDetail.builder()
                            .id(id)
                            .data(data)
                            .type(LessonDetailType.DESCRIBE_PICTURE)
                            .order(lessonScenarioDTO.getOrder())
                            .lessonId(lessonScenarioDTO.getLessonId())
                            .build());
                } catch (Exception e) {
                    log.error("Error in sheet at row {}: {}", rowIndex + 2, e.getMessage());
                    throw e;
                }
            }

            lessonDetailRepository.saveAll(communicateLessonDetails);
        });
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void importOnboardingData() throws Exception {
        try {
            onboardingQuestionRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "onboarding!A2:G", processOnboardingSheet(), true);
        } catch (Exception e) {
            log.error("Throw exception when synced google onboarding_question sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processOnboardingSheet() throws Exception {
        class OnboardingColumn {
            public static final int ID = 0;
            public static final int BACKGROUND = 1;
            public static final int CHARACTER = 2;
            public static final int TEXT = 3;
            public static final int TYPE = 4;
            public static final int ANSWER_TYPE = 5;
            public static final int DATA = 6;

            private OnboardingColumn() {
            }
        }
        return rethrowConsumer(rows -> {
            List<OnboardingQuestion> onboardingQuestions = new ArrayList<>();
            for (List<Object> value : rows) {
                if (value == null || value.isEmpty()) {
                    continue;
                }

                Integer id = Integer.parseInt(getRequiredField(value, OnboardingColumn.ID));
                String background = getRequiredField(value, OnboardingColumn.BACKGROUND);
                String character = getRequiredField(value, OnboardingColumn.CHARACTER);
                String text = getRequiredField(value, OnboardingColumn.TEXT);
                String type = getRequiredField(value, OnboardingColumn.TYPE);
                OnboardingAnswerType answerType = OnboardingAnswerType.from(getNullableField(value, OnboardingColumn.ANSWER_TYPE));
                String dataStr = getNullableField(value, OnboardingColumn.DATA);
                JsonNode data = StringUtils.isEmpty(dataStr) ? null : objectMapper.readTree(dataStr);
                onboardingQuestions.add(OnboardingQuestion.builder()
                        .id(id)
                        .background(background)
                        .character(character)
                        .text(text)
                        .type(type)
                        .answerType(answerType)
                        .data(data)
                        .build());
            }

            onboardingQuestionRepository.saveAll(onboardingQuestions);
        });
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void importEntranceTestData() throws Exception {
        try {
            entranceTestDetailRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "entrance_test!A2:D", processEntranceTestSheet(), true);
        } catch (Exception e) {
            log.error("Throw exception when synced google entrance test sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processEntranceTestSheet() throws Exception {
        class EntranceTestColumn {
            public static final int ID = 0;
            public static final int ORDER = 1;
            public static final int TYPE = 2;
            public static final int DATA = 3;

            private EntranceTestColumn() {
            }
        }
        return rethrowConsumer(rows -> {
            List<EntranceTestDetail> entranceTestDetails = new ArrayList<>();
            for (List<Object> value : rows) {
                if (value == null || value.isEmpty()) {
                    continue;
                }

                String id = getRequiredField(value, EntranceTestColumn.ID);
                Double order = Double.parseDouble(getRequiredField(value, EntranceTestColumn.ORDER));
                EntranceTestDetailType type = EntranceTestDetailType.from(getRequiredField(value, EntranceTestColumn.TYPE));
                JsonNode data = objectMapper.readTree(getRequiredField(value, EntranceTestColumn.DATA));

                entranceTestDetails.add(EntranceTestDetail.builder()
                        .id(id)
                        .order(order)
                        .type(type)
                        .data(data)
                        .build());
            }

            entranceTestDetailRepository.saveAll(entranceTestDetails);
        });
    }

    @Async
    @Transactional(rollbackFor = Exception.class)
    public void importRobotFirmwareData() throws Exception {
        try {
            robotFirmwareVersionRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "firmware_version!A2:D", processRobotFirmwareSheet(), true);
        } catch (Exception e) {
            log.error("Throw exception when synced google firmware_version sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processRobotFirmwareSheet() throws Exception {
        class FirmwareColumn {
            public static final int ID = 0;
            public static final int VERSION = 1;
            public static final int URL = 2;

            private FirmwareColumn() {
            }
        }
        return rethrowConsumer(rows -> {
            List<RobotFirmwareVersion> robotFirmwareVersions = new ArrayList<>();
            for (List<Object> value : rows) {
                if (value == null || value.isEmpty()) {
                    continue;
                }

                Long id = Long.parseLong(getRequiredField(value, FirmwareColumn.ID));
                String version = getRequiredField(value, FirmwareColumn.VERSION);
                String url = getRequiredField(value, FirmwareColumn.URL);

                robotFirmwareVersions.add(RobotFirmwareVersion.builder()
                        .id(id)
                        .version(version)
                        .build());
            }

            robotFirmwareVersionRepository.saveAll(robotFirmwareVersions);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void initMvpLessonData() throws Exception {
        try {
            lessonRepository.truncateTable();
            lessonDetailRepository.truncateTable();
            processSheet(SHEET_LEARN_DATA, "lesson_mvp!A2:E", processMvpLessonSheet(), true);

        } catch (Exception e) {
            log.error("Throw exception when synced google lesson sheets =============== {0}", e);
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    private Consumer<List<List<Object>>> processMvpLessonSheet() throws Exception {
        class LessonColumn {
            public static final int ID = 0;
            public static final int TITLE = 1;
            public static final int BOT_ID = 2;
            public static final int IMAGE = 3;
            public static final int CAMPAIGN_NAME = 4;

            private LessonColumn() {
            }
        }

        return rethrowConsumer(rows -> {
            List<LessonDetail> lessonDetails = new ArrayList<>();
            List<MvpLessonReqDTO> mvpLessonReqDTOS = new ArrayList<>();
            for (List<Object> value : rows) {
                if (value == null || value.isEmpty()) {
                    continue;
                }

                long id = Long.parseLong(getRequiredField(value, LessonColumn.ID));
                String title = trimData(getRequiredField(value, LessonColumn.TITLE));
                long botId = Long.parseLong(getRequiredField(value, LessonColumn.BOT_ID));
                String image = trimData(getRequiredField(value, LessonColumn.IMAGE));
                String campaignName = trimData(getRequiredField(value, LessonColumn.CAMPAIGN_NAME));

                ObjectNode data = JsonNodeFactory.instance.objectNode();
                data.put("bot_id", botId);

                Lesson lesson = lessonRepository.save(Lesson.builder()
                        .id(id)
                        .title(title)
                        .image(image)
                        .build());
                lessonDetails.add(LessonDetail.builder()
                        .id(title + lesson.getId())
                        .data(data)
                        .type(LessonDetailType.FREE_TALK)
                        .order(1.0)
                        .lessonId(lesson.getId())
                        .build());
                mvpLessonReqDTOS.add(MvpLessonReqDTO.builder()
                        .lessonId(lesson.getId())
                        .image(image)
                        .title(title)
                        .campaignName(campaignName)
                        .build());
            }

            lessonDetailRepository.saveAll(lessonDetails);
            resetMvpRobotDemos(mvpLessonReqDTOS);
        });
    }

    private void resetMvpRobotDemos(List<MvpLessonReqDTO> mvpLessonReqDTOS) throws IOException {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(20, TimeUnit.SECONDS)
                .writeTimeout(20, TimeUnit.SECONDS)
                .readTimeout(20, TimeUnit.SECONDS)
                .build();

        Request request = new Request.Builder()
                .url(mvpHost + "/api/v1/admin/demos/reset_robot_demo")
                .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsString(mvpLessonReqDTOS)))
                .build();

        Response response = client.newCall(request).execute();
        DataResponseDTO<ChatResDTO> chatResDTO = objectMapper.readValue(response.body().string(), new TypeReference<>() {
        });
        log.info("======= MVP response: {}", objectMapper.writeValueAsString(chatResDTO));
    }
}
