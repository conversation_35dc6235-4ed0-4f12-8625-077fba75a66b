package com.stepup.springrobot.service.data;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.sheets.v4.Sheets;
import com.google.api.services.sheets.v4.SheetsScopes;
import com.google.api.services.sheets.v4.model.*;
import com.stepup.springrobot.dto.learn.LessonDetailDTO;
import com.stepup.springrobot.dto.study.DataTrackingTimeDTO;
import com.stepup.springrobot.dto.study.StudyDataExportDTO;
import com.stepup.springrobot.dto.study.UserExportInfoDTO;
import com.stepup.springrobot.repository.chat.RobotUserConversationRecordHistoryRepository;
import com.stepup.springrobot.repository.chat.RobotUserConversationRepository;
import com.stepup.springrobot.repository.learn.LessonDetailRepository;
import com.stepup.springrobot.repository.study.StudyLessonRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ExportStudyDataService {
    private static final String APPLICATION_NAME = "Robot Study Data Export";
    private static final JsonFactory JSON_FACTORY = JacksonFactory.getDefaultInstance();
    private static final String SPREADSHEET_ID = "1FCBSEIeRQJXqczIb1QkHErzVvCSt378LjjTIG5myXQk";
    private static final int DEFAULT_START_ROW = 2;
    private static final List<String> SCOPES = Collections.singletonList(SheetsScopes.SPREADSHEETS);
    private static final String CREDENTIALS_FILE_PATH = "/cmsmkt-1601868101732-899297121cb2.json";

    private Sheets sheetsService;

    @Autowired
    private RobotUserConversationRepository robotUserConversationRepository;

    @Autowired
    private RobotUserConversationRecordHistoryRepository robotUserConversationRecordHistoryRepository;

    @Autowired
    private LessonDetailRepository lessonDetailRepository;

    @Autowired
    private StudyLessonRepository studyLessonRepository;

    public ExportStudyDataService() throws GeneralSecurityException, IOException {
        this.sheetsService = createSheetsService();
    }

    private Sheets createSheetsService() throws GeneralSecurityException, IOException {
        final NetHttpTransport HTTP_TRANSPORT = GoogleNetHttpTransport.newTrustedTransport();
        GoogleCredential credential = GoogleCredential
                .fromStream(Objects.requireNonNull(getClass().getResourceAsStream(CREDENTIALS_FILE_PATH)))
                .createScoped(SCOPES);

        return new Sheets.Builder(HTTP_TRANSPORT, JSON_FACTORY, credential)
                .setApplicationName(APPLICATION_NAME)
                .build();
    }

    /**
     * Check if sheet exists and create it if not
     */
    private void ensureSheetExists(String sheetName) throws IOException {
        try {
            // Get spreadsheet metadata to check if sheet exists
            Spreadsheet spreadsheet = sheetsService.spreadsheets()
                    .get(SPREADSHEET_ID)
                    .execute();

            boolean sheetExists = spreadsheet.getSheets().stream()
                    .anyMatch(sheet -> sheetName.equals(sheet.getProperties().getTitle()));

            if (!sheetExists) {
                // Create new sheet
                SheetProperties sheetProperties = new SheetProperties().setTitle(sheetName);
                AddSheetRequest addSheetRequest = new AddSheetRequest().setProperties(sheetProperties);
                Request request = new Request().setAddSheet(addSheetRequest);

                BatchUpdateSpreadsheetRequest batchUpdateRequest = new BatchUpdateSpreadsheetRequest()
                        .setRequests(Collections.singletonList(request));

                sheetsService.spreadsheets().batchUpdate(SPREADSHEET_ID, batchUpdateRequest).execute();
                log.info("Created new sheet: {}", sheetName);

                // Add header row to new sheet
                List<List<Object>> headerValues = new ArrayList<>();
                headerValues.add(List.of("Name", "SĐT", "Ngày", "Bài học (BÀI HỌC CÓ THỜI LƯỢNG DÀI NHẤT)", "Logs conversation", "Start time", "End time", "Bot ID"));

                ValueRange headerBody = new ValueRange().setValues(headerValues);
                String headerRange = String.format("%s!A1:H1", sheetName);

                sheetsService.spreadsheets().values()
                        .update(SPREADSHEET_ID, headerRange, headerBody)
                        .setValueInputOption("RAW")
                        .execute();

                log.info("Added header row to sheet: {}", sheetName);
            }
        } catch (Exception e) {
            log.error("Error ensuring sheet exists: {}", e.getMessage(), e);
            throw new IOException("Failed to ensure sheet exists: " + sheetName, e);
        }
    }

    /**
     * Clear existing data from sheet (except header row)
     */
    private void clearSheetData(String sheetName) throws IOException {
        try {
            // Clear data from row 2 onwards (keeping header row)
            String clearRange = String.format("%s!A2:H", sheetName);
            ClearValuesRequest clearRequest = new ClearValuesRequest();

            sheetsService.spreadsheets().values()
                    .clear(SPREADSHEET_ID, clearRange, clearRequest)
                    .execute();

            log.info("Cleared existing data from sheet: {}", sheetName);
        } catch (Exception e) {
            log.warn("Could not clear existing data from sheet {}: {}", sheetName, e.getMessage());
        }
    }

    /**
     * Export a list of study data to specified sheet starting from the specified row
     */
    private void exportStudyDataList(List<StudyDataExportDTO> dataList, String sheetName, int startRow) throws IOException {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("No data to export");
            return;
        }

        // Ensure sheet exists before trying to write data
        ensureSheetExists(sheetName);

        // Clear existing data to avoid duplication
        clearSheetData(sheetName);

        // Prepare data rows
        List<List<Object>> values = new ArrayList<>();
        for (StudyDataExportDTO data : dataList) {
            values.add(List.of(
                    data.getName() != null ? data.getName() : "",
                    data.getPhoneNumber() != null ? data.getPhoneNumber() : "",
                    data.getDate() != null ? data.getDate() : "",
                    data.getCompletedLessons() != null ? data.getCompletedLessons() : "",
                    data.getLessonUrl() != null ? data.getLessonUrl() : "",
                    data.getStartTime() != null ? data.getStartTime() : "",
                    data.getEndTime() != null ? data.getEndTime() : "",
                    data.getBotId() != null ? data.getBotId() : ""
            ));
        }

        // Calculate range for all rows
        int endRow = startRow + dataList.size() - 1;
        String range = String.format("%s!A%d:H%d", sheetName, startRow, endRow);

        ValueRange body = new ValueRange().setValues(values);

        sheetsService.spreadsheets().values()
                .update(SPREADSHEET_ID, range, body)
                .setValueInputOption("RAW")
                .execute();

        log.info("Exported {} records to Google Sheets range: {}", dataList.size(), range);
    }

    // Method to export data by phone number to specified sheet
    public void exportStudyDataByPhone(String phoneNumber, String sheetName) throws IOException {
        List<StudyDataExportDTO> studyDataList = getStudyDataByPhone(phoneNumber);

        if (studyDataList.isEmpty()) {
            log.warn("No study data found for phone number: {}", phoneNumber);
            return;
        }

        // Export all data as a list starting from row 4
        exportStudyDataList(studyDataList, sheetName, DEFAULT_START_ROW);
    }

    // Method to get study data from database by phone number
    public List<StudyDataExportDTO> getStudyDataByPhone(String phoneNumber) {
        // Get conversation details with optimized query (one conversation per day with max duration)
        List<Object[]> conversationDetails = robotUserConversationRepository
                .findLongestConversationDetailsPerDay(phoneNumber);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");

        return conversationDetails.stream().map(row -> {
            // Extract data from Object[] result
            Long conversationId = ((Number) row[0]).longValue();
            String phone = (String) row[1];
            Long botId = row[2] != null ? ((Number) row[2]).longValue() : null;
            java.sql.Timestamp startTimestamp = (java.sql.Timestamp) row[3];
            java.sql.Timestamp endTimestamp = (java.sql.Timestamp) row[4];
            java.sql.Date conversationDate = (java.sql.Date) row[5];

            // Format data
            String startTime = timeFormat.format(startTimestamp);
            String endTime = timeFormat.format(endTimestamp);
            String date = dateFormat.format(conversationDate);

            List<LessonDetailDTO> lessonDetailDTO = lessonDetailRepository.getStudyLessonIdByBotId(botId.toString());

            String studyLessonIds = lessonDetailDTO.stream()
                    .map(projection -> String.valueOf(projection.getLessonId()))
                    .collect(Collectors.joining(" - "));

            // Create lesson URL using conversation_id
            String lessonUrl = String.format("https://robot-api.hacknao.edu.vn/web/admin/conversations/%s", conversationId);

            String completedLessons = lessonDetailDTO.stream()
                    .map(LessonDetailDTO::getId)
                    .collect(Collectors.joining(" | "));

            return StudyDataExportDTO.builder()
                    .name(getNameByPhone(phoneNumber))
                    .phoneNumber(phoneNumber)
                    .date(date)
                    .completedLessons(completedLessons)
                    .lessonUrl(lessonUrl)
                    .startTime(startTime)
                    .endTime(endTime)
                    .botId(botId)
                    .build();
        }).collect(Collectors.toList());
    }

    // Helper method to get name by phone - you might want to implement based on your user table
    private String getNameByPhone(String phoneNumber) {
        // Return "default" as requested, can be updated later
        return "default";
    }

    /**
     * Read user list from Google Sheets with sheet name "list_user_to_export"
     * @return List of UserExportInfoDTO containing phone numbers and sheet names
     */
    public List<UserExportInfoDTO> readUserListFromSheet() throws IOException {
        String sheetName = "list_user_to_export";
        String range = sheetName + "!A:B"; // Phone numbers in column A, sheet names in column B

        ValueRange response = sheetsService.spreadsheets().values()
                .get(SPREADSHEET_ID, range)
                .execute();

        List<List<Object>> values = response.getValues();
        List<UserExportInfoDTO> userInfoList = new ArrayList<>();

        if (values != null && !values.isEmpty()) {
            // Skip header row if exists
            for (int i = 1; i < values.size(); i++) {
                List<Object> row = values.get(i);
                if (!row.isEmpty() && row.get(0) != null) {
                    String phoneNumber = row.get(0).toString().trim();
                    if (!phoneNumber.isEmpty()) {
                        // Get sheet name from column B, or use default if empty
                        String customSheetName = null;
                        if (row.size() > 1 && row.get(1) != null) {
                            customSheetName = row.get(1).toString().trim();
                            if (customSheetName.isEmpty()) {
                                customSheetName = null;
                            }
                        }

                        // Use custom sheet name or default format
                        String finalSheetName = customSheetName != null ? customSheetName : "User-" + phoneNumber;
                        userInfoList.add(UserExportInfoDTO.builder()
                                .phoneNumber(phoneNumber)
                                .sheetName(finalSheetName)
                                .build());
                    }
                }
            }
        }

        log.info("Read {} user export info from sheet: {}", userInfoList.size(), sheetName);
        return userInfoList;
    }

    /**
     * Scheduled job to run daily at 2:00 AM to export study data for users in the list
     */
    @Scheduled(cron = "0 0 2 * * ?") // Run daily at 2:00 AM
    public void dailyExportStudyData() {
        try {
            log.info("Starting daily export study data job");

            // Read user list from Google Sheets
            List<UserExportInfoDTO> userExportInfoList = readUserListFromSheet();

            if (userExportInfoList.isEmpty()) {
                log.warn("No user export info found in list_user_to_export sheet");
                return;
            }

            // Export study data for each user
            for (UserExportInfoDTO userInfo : userExportInfoList) {
                try {
                    String phoneNumber = userInfo.getPhoneNumber();
                    String sheetName = userInfo.getSheetName();
                    exportStudyDataByPhone(phoneNumber, sheetName);
                    log.info("Successfully exported data for phone: {} to sheet: {}", phoneNumber, sheetName);
                } catch (Exception e) {
                    log.error("Failed to export data for phone: {}, error: {}", userInfo.getPhoneNumber(), e.getMessage(), e);
                }
            }

            log.info("Completed daily export study data job, processed {} users", userExportInfoList.size());

        } catch (Exception e) {
            log.error("Error in daily export study data job: {}", e.getMessage(), e);
        }
    }

    /**
     * Convert Object[] from native query to DataTrackingTimeDTO
     */
    private DataTrackingTimeDTO convertToDataTrackingTimeDTO(Object[] row) {
        if (row == null || row.length < 4) {
            return null;
        }

        Long botId = row[0] != null ? ((Number) row[0]).longValue() : null;
        Long totalUsers = row[1] != null ? ((Number) row[1]).longValue() : null;
        Long completedUsers = row[2] != null ? ((Number) row[2]).longValue() : null;
        String formattedTotalDuration = row[3] != null ? row[3].toString() : "0 mins .000 secs";

        return new DataTrackingTimeDTO(botId, totalUsers, completedUsers, formattedTotalDuration);
    }

    /**
     * Get data tracking time from database
     */
    public List<DataTrackingTimeDTO> getDataTrackingTime() {
        List<Object[]> rawData = studyLessonRepository.getDataTrackingTimeRaw();

        return rawData.stream()
                .map(this::convertToDataTrackingTimeDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * Export data tracking time to Google Sheets
     */
    public void exportDataTrackingTime() throws IOException {
        String sheetName = "data_tracking_time";

        // Ensure sheet exists before trying to write data
        ensureSheetExists(sheetName);

        // Clear existing data to avoid duplication
        clearSheetData(sheetName);

        // Add header row to new sheet if it doesn't exist
        List<List<Object>> headerValues = new ArrayList<>();
        headerValues.add(List.of("bot_id", "Tổng số người học", "số người hoàn thành", "Tổng thời gian hoàn thành"));

        ValueRange headerBody = new ValueRange().setValues(headerValues);
        String headerRange = String.format("%s!A1:D1", sheetName);

        sheetsService.spreadsheets().values()
                .update(SPREADSHEET_ID, headerRange, headerBody)
                .setValueInputOption("RAW")
                .execute();

        // Get data from database
        List<DataTrackingTimeDTO> dataList = getDataTrackingTime();

        if (dataList.isEmpty()) {
            log.warn("No data tracking time data found");
            return;
        }

        // Prepare data rows
        List<List<Object>> values = new ArrayList<>();
        for (DataTrackingTimeDTO data : dataList) {
            values.add(List.of(
                    data.getBotId() != null ? data.getBotId() : "",
                    data.getTotalUsers() != null ? data.getTotalUsers() : 0,
                    data.getCompletedUsers() != null ? data.getCompletedUsers() : 0,
                    data.getFormattedTotalDuration() != null ? data.getFormattedTotalDuration() : ""
            ));
        }

        // Calculate range for all rows
        int startRow = 2;
        int endRow = startRow + dataList.size() - 1;
        String range = String.format("%s!A%d:D%d", sheetName, startRow, endRow);

        ValueRange body = new ValueRange().setValues(values);

        sheetsService.spreadsheets().values()
                .update(SPREADSHEET_ID, range, body)
                .setValueInputOption("RAW")
                .execute();

        log.info("Exported {} data tracking time records to Google Sheets range: {}", dataList.size(), range);
    }

    /**
     * Scheduled job to export data tracking time daily at 3:00 AM
     */
    @Scheduled(cron = "0 0 3 * * ?") // Run daily at 3:00 AM
    public void dailyExportDataTrackingTime() {
        try {
            log.info("Starting daily export data tracking time job");
            exportDataTrackingTime();
            log.info("Completed daily export data tracking time job");
        } catch (Exception e) {
            log.error("Error in daily export data tracking time job: {}", e.getMessage(), e);
        }
    }
}
