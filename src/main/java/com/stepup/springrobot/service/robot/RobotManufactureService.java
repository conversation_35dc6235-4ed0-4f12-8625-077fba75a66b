package com.stepup.springrobot.service.robot;

import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.robot_manufacture.CreateRobotManufactureReqDTO;
import com.stepup.springrobot.dto.robot_manufacture.RobotManufactureDTO;
import com.stepup.springrobot.exception.business.manufacturing.ManufacturingSecurityException;
import com.stepup.springrobot.model.ModelAIProvider;
import com.stepup.springrobot.model.robot.RobotManufacture;
import com.stepup.springrobot.repository.ExternalProviderTokensRepository;
import com.stepup.springrobot.repository.robot.RobotManufactureRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RobotManufactureService {
    private final RobotManufactureRepository robotManufactureRepository;
    private final ExternalProviderTokensRepository externalProviderTokensRepository;

    @Autowired
    public RobotManufactureService(RobotManufactureRepository robotManufactureRepository, ExternalProviderTokensRepository externalProviderTokensRepository) {
        this.robotManufactureRepository = robotManufactureRepository;
        this.externalProviderTokensRepository = externalProviderTokensRepository;
    }

    @Transactional
    public RobotManufactureDTO createRobotManufacture(CreateRobotManufactureReqDTO request) {
        validateManufacturingKey(request.getManufacturingKey());

        if (robotManufactureRepository.existsByDeviceSerialNumber(request.getDeviceSerialNumber())) {
            throw new ManufacturingSecurityException(CodeDefine.DUPLICATE_DEVICE_SERIAL_ERROR);
        }

        log.info("Creating new robot manufacture with device serial: {}, batch: {}",
                request.getDeviceSerialNumber(), request.getBatchNumber());

        RobotManufacture robotManufacture = convertToEntity(request);

        RobotManufacture savedEntity = robotManufactureRepository.save(robotManufacture);

        log.info("Successfully created robot manufacture with ID: {}", savedEntity.getId());

        return convertToDTO(savedEntity);
    }

    @Transactional(readOnly = true)
    public RobotManufactureDTO getRobotManufactureById(String id) {
        log.info("Getting robot manufacture by ID: {}", id);

        Optional<RobotManufacture> robotManufacture = robotManufactureRepository.findById(id);
        if (robotManufacture.isEmpty()) {
            log.warn("Robot manufacture not found with ID: {}", id);
            return null;
        }

        return convertToDTO(robotManufacture.get());
    }

    @Transactional(readOnly = true)
    public RobotManufactureDTO getRobotManufactureByDeviceSerial(String deviceSerialNumber) {
        log.info("Getting robot manufacture by device serial: {}", deviceSerialNumber);

        Optional<RobotManufacture> robotManufacture = robotManufactureRepository.findByDeviceSerialNumber(deviceSerialNumber);
        if (robotManufacture.isEmpty()) {
            log.warn("Robot manufacture not found with device serial: {}", deviceSerialNumber);
            return null;
        }

        return convertToDTO(robotManufacture.get());
    }

    @Transactional(readOnly = true)
    public List<RobotManufactureDTO> getRobotManufacturesByBatchNumber(String batchNumber) {
        log.info("Getting robot manufactures by batch number: {}", batchNumber);

        List<RobotManufacture> robotManufactures = robotManufactureRepository.findByBatchNumber(batchNumber);

        return robotManufactures.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<RobotManufactureDTO> getRobotManufacturesByStatus(RobotManufacture.ManufacturingStatus status) {
        log.info("Getting robot manufactures by status: {}", status);

        List<RobotManufacture> robotManufactures = robotManufactureRepository.findByManufacturingStatus(status);

        return robotManufactures.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Transactional
    public boolean deleteRobotManufacture(String id) {
        log.info("Deleting robot manufacture with ID: {}", id);

        Optional<RobotManufacture> robotManufacture = robotManufactureRepository.findById(id);
        if (robotManufacture.isEmpty()) {
            log.warn("Cannot delete - Robot manufacture not found with ID: {}", id);
            return false;
        }

        robotManufactureRepository.deleteById(id);
        log.info("Successfully deleted robot manufacture with ID: {}", id);
        return true;
    }

    @Transactional(readOnly = true)
    public List<RobotManufactureDTO> getAllRobotManufactures() {
        log.info("Getting all robot manufactures");

        List<RobotManufacture> robotManufactures = robotManufactureRepository.findAll();

        return robotManufactures.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    // Helper method for converting entity to DTO
    private RobotManufactureDTO convertToDTO(RobotManufacture entity) {
        return RobotManufactureDTO.builder()
                .id(entity.getId())
                .deviceSerialNumber(entity.getDeviceSerialNumber())
                .batchNumber(entity.getBatchNumber())
                .manufacturingStatus(entity.getManufacturingStatus())
                .hardwareVersion(entity.getHardwareVersion())
                .manufacturedDate(entity.getManufacturedDate())
                .deliveryDate(entity.getDeliveryDate())
                .robotId(entity.getRobotId())
                .manufacturingNotes(entity.getManufacturingNotes())
                .build();
    }

    // Helper method for converting DTO to entity
    private RobotManufacture convertToEntity(CreateRobotManufactureReqDTO dto) {
        return RobotManufacture.builder()
                .deviceSerialNumber(dto.getDeviceSerialNumber())
                .batchNumber(dto.getBatchNumber())
                .manufacturingStatus(dto.getManufacturingStatus())
                .hardwareVersion(dto.getHardwareVersion())
                .manufacturedDate(dto.getManufacturedDate())
                .manufacturingNotes(dto.getManufacturingNotes())
                .build();
    }

    // Validate manufacturing security key
    private void validateManufacturingKey(String providedKey) {
        if (providedKey == null || providedKey.trim().isEmpty()) {
            throw new ManufacturingSecurityException(CodeDefine.MISSING_MANUFACTURING_KEY_ERROR);
        }

        String manufacturingKey = externalProviderTokensRepository
                .findByProvider(ModelAIProvider.stepup_robot_manufacture).get(0).getToken();

        if (!manufacturingKey.equals(providedKey.trim())) {
            log.warn("Invalid manufacturing key attempt: {}", providedKey);
            throw new ManufacturingSecurityException(CodeDefine.INVALID_MANUFACTURING_KEY_ERROR);
        }

        log.info("Manufacturing key validated successfully");
    }
} 