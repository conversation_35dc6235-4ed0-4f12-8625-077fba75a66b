package com.stepup.springrobot.service;

import com.stepup.springrobot.dto.monitoring.TraceContextDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.UUID;

@Slf4j
@Service
public class TraceContextService {
    private static final ThreadLocal<TraceContextDTO> TRACE_CONTEXT = new ThreadLocal<>();

    public void setTraceContext(TraceContextDTO context) {
        TRACE_CONTEXT.set(context);
    }

    public TraceContextDTO getTraceContext() {
        return TRACE_CONTEXT.get();
    }

    public void clearTraceContext() {
        TRACE_CONTEXT.remove();
    }

    public TraceContextDTO createNewTrace(String userId, String sessionId) {
        TraceContextDTO context = TraceContextDTO.builder()
                .traceId(generateTraceId())
                .spanId(generateSpanId())
                .userId(userId)
                .sessionId(sessionId)
                .timestamp(Instant.now())
                .build();

        setTraceContext(context);
        return context;
    }

    public TraceContextDTO createChildSpan(String operation) {
        TraceContextDTO parentContext = getTraceContext();
        if (parentContext == null) {
            log.warn("No parent trace context found for operation: {}", operation);
            return createNewTrace("unknown", "unknown");
        }

        TraceContextDTO childContext = TraceContextDTO.builder()
                .traceId(parentContext.getTraceId())
                .spanId(generateSpanId())
                .parentSpanId(parentContext.getSpanId())
                .userId(parentContext.getUserId())
                .sessionId(parentContext.getSessionId())
                .robotId(parentContext.getRobotId())
                .conversationId(parentContext.getConversationId())
                .requestId(parentContext.getRequestId())
                .timestamp(Instant.now())
                .build();

        setTraceContext(childContext);
        return childContext;
    }

    public void updateTraceContext(String robotId, String conversationId, String requestId) {
        TraceContextDTO currentContext = getTraceContext();
        if (currentContext != null) {
            TraceContextDTO updatedContext = TraceContextDTO.builder()
                    .traceId(currentContext.getTraceId())
                    .spanId(currentContext.getSpanId())
                    .parentSpanId(currentContext.getParentSpanId())
                    .userId(currentContext.getUserId())
                    .sessionId(currentContext.getSessionId())
                    .robotId(robotId)
                    .conversationId(conversationId)
                    .requestId(requestId)
                    .timestamp(currentContext.getTimestamp())
                    .build();

            setTraceContext(updatedContext);
        }
    }

    private String generateTraceId() {
        return "trace_" + UUID.randomUUID().toString().replace("-", "");
    }

    private String generateSpanId() {
        return "span_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
}
