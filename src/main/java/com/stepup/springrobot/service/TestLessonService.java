package com.stepup.springrobot.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.dto.chat.AIRobotConversationResDTO;
import com.stepup.springrobot.dto.chat.ConversationMessageDTO;
import com.stepup.springrobot.dto.chat.FullTestLessonReqDTO;
import com.stepup.springrobot.dto.chat.FullTestLessonResDTO;
import com.stepup.springrobot.dto.chat.FullTestSummaryDTO;
import com.stepup.springrobot.dto.chat.GenerateUserResponseReqDTO;
import com.stepup.springrobot.dto.chat.GenerateUserResponseResDTO;
import com.stepup.springrobot.dto.chat.RobotConversationMsgResDTO;
import com.stepup.springrobot.dto.chat.SingleRandomTestReqDTO;
import com.stepup.springrobot.dto.chat.SingleRandomTestResDTO;
import com.stepup.springrobot.dto.chat.TestFlowResultDTO;
import com.stepup.springrobot.dto.chat.TestInteractionDTO;
import com.stepup.springrobot.dto.chat.TestLessonResDTO;
import com.stepup.springrobot.dto.chat.TestExecutionStateDTO;
import com.stepup.springrobot.dto.chat.WorkflowDetailDTO;
import com.stepup.springrobot.dto.chat.WorkflowExcelDTO;
import com.stepup.springrobot.dto.chat.WorkflowResDTO;
import com.stepup.springrobot.dto.chat.LessonApiResponseDTO;
import com.stepup.springrobot.model.chat.ConversationSource;

import lombok.extern.log4j.Log4j2;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

@Service
@Log4j2
public class TestLessonService {

    private final OkHttpClient httpClient = new OkHttpClient();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AIRobotConversationService aiRobotConversationService;
    private final OpenAIService openAIService;

    private final AlertWebhookService alertWebhookService;

    public TestLessonService(AIRobotConversationService aiRobotConversationService, OpenAIService openAIService,
            AlertWebhookService alertWebhookService) {
        this.aiRobotConversationService = aiRobotConversationService;
        this.openAIService = openAIService;
        this.alertWebhookService = alertWebhookService;
    }

    /**
     * Execute full test lesson workflow with actual conversation simulation
     */
    public FullTestLessonResDTO executeFullTestLesson(FullTestLessonReqDTO request) {
        log.info("Starting full test lesson execution for lesson bot ID: {}", request.getBotId());
        long startTime = System.currentTimeMillis();

        try {
            // Step 1: Call lesson API to get bot data and find workflow robot_type_ids
            log.info("Step 1: Calling lesson API to find workflow bot IDs for lesson bot ID: {}", request.getBotId());
            List<Long> workflowBotIds = fetchWorkflowBotIdsFromLesson(request.getBotId());
            if (workflowBotIds == null || workflowBotIds.isEmpty()) {
                throw new RuntimeException("No workflows found for lesson bot ID: " + request.getBotId());
            }

            log.info("Step 1 completed: Found {} workflow bot IDs for lesson bot ID: {}", workflowBotIds.size(),
                    request.getBotId());

            // Step 2: Fetch and aggregate workflow data using multiple workflow bot IDs
            log.info("Step 2: Fetching and aggregating workflow data from {} workflow bot IDs", workflowBotIds.size());
            WorkflowAnalysis analysis = fetchAndAggregateWorkflowData(workflowBotIds);
            if (analysis == null || analysis.getAllIntents().isEmpty()) {
                throw new RuntimeException("Invalid aggregated workflow data for workflow bot IDs: " + workflowBotIds);
            }

            log.info("Step 2 completed: Successfully aggregated workflow data with {} total intents from {} questions",
                    analysis.getAllIntents().size(), analysis.getQuestions().size());

            List<ConversationFlow> conversationFlows = generateConversationFlows(analysis);

            // Step 3: Execute each conversation flow concurrently using multiple workflows
            int poolSize = Math.min(8, Math.max(1, conversationFlows.size()));
            ExecutorService executor = Executors.newFixedThreadPool(poolSize);
            List<CompletableFuture<TestFlowResultDTO>> futures = new ArrayList<>();
            for (int i = 0; i < conversationFlows.size(); i++) {
                final int flowIdx = i;
                final ConversationFlow flow = conversationFlows.get(i);
                futures.add(CompletableFuture.supplyAsync(() -> {
                    log.info("Executing enhanced tracking multi-workflow test flow {} of {} (async)", flowIdx + 1,
                            conversationFlows.size());
                    return executeMultipleWorkflowsFlowWithTracking(request, workflowBotIds, flow, flowIdx + 1);
                }, executor));
            }
            List<TestFlowResultDTO> testResults = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());
            executor.shutdown();

            int successfulFlows = (int) testResults.stream()
                    .filter(r -> Boolean.TRUE.equals(r.getCompletedSuccessfully()))
                    .count();
            int totalInteractions = testResults.stream()
                    .mapToInt(TestFlowResultDTO::getTotalInteractions)
                    .sum();
            int totalTokensUsed = testResults.stream()
                    .flatMap(r -> r.getInteractions().stream())
                    .mapToInt(interaction -> interaction.getTokensUsed() != null ? interaction.getTokensUsed() : 0)
                    .sum();
            long totalResponseTime = testResults.stream()
                    .flatMap(r -> r.getInteractions().stream())
                    .mapToLong(interaction -> interaction.getResponseTimeMs() != null ? interaction.getResponseTimeMs()
                            : 0)
                    .sum();

            // Step 3: Build summary
            long totalExecutionTime = (System.currentTimeMillis() - startTime) / 1000;
            FullTestSummaryDTO summary = FullTestSummaryDTO.builder()
                    .totalFlowsTested(conversationFlows.size())
                    .successfulFlows(successfulFlows)
                    .failedFlows(conversationFlows.size() - successfulFlows)
                    .successRatePercentage(
                            conversationFlows.size() > 0 ? (double) successfulFlows / conversationFlows.size() * 100
                                    : 0.0)
                    .totalInteractions(totalInteractions)
                    .averageInteractionsPerFlow(
                            conversationFlows.size() > 0 ? (double) totalInteractions / conversationFlows.size() : 0.0)
                    .totalExecutionTimeSeconds(totalExecutionTime)
                    .averageResponseTimeMs(totalInteractions > 0 ? (double) totalResponseTime / totalInteractions : 0.0)
                    .totalTokensUsed(totalTokensUsed)
                    .build();

            log.info("Full test lesson completed. Success rate: {}%, Total time: {}s",
                    summary.getSuccessRatePercentage(), totalExecutionTime);

            return FullTestLessonResDTO.builder()
                    .botId(request.getBotId())
                    .totalTestFlows(conversationFlows.size())
                    .testResults(testResults)
                    .summary(summary)
                    .build();

        } catch (Exception e) {
            log.error("Error executing full test lesson for bot ID: {}", request.getBotId(), e);
            throw new RuntimeException("Failed to execute full test lesson: " + e.getMessage(), e);
        }
    }

    /**
     * Test lesson workflow by calculating minimum loops to cover all intents
     */
    public TestLessonResDTO testLessonWorkflow(Long botId) {
        try {
            log.info("Starting lesson workflow test for lesson bot ID: {}", botId);

            // Step 1: Call lesson API to get bot data and find workflow robot_type_ids
            log.info("Step 1: Calling lesson API to find workflow bot IDs for lesson bot ID: {}", botId);
            List<Long> workflowBotIds = fetchWorkflowBotIdsFromLesson(botId);
            if (workflowBotIds == null || workflowBotIds.isEmpty()) {
                throw new RuntimeException("No workflows found for lesson bot ID: " + botId);
            }

            log.info("Step 1 completed: Found {} workflow bot IDs for lesson bot ID: {}", workflowBotIds.size(), botId);

            // Step 2: Fetch and aggregate workflow data using multiple workflow bot IDs
            log.info("Step 2: Fetching and aggregating workflow data from {} workflow bot IDs", workflowBotIds.size());
            WorkflowAnalysis analysis = fetchAndAggregateWorkflowData(workflowBotIds);
            if (analysis == null || analysis.getAllIntents().isEmpty()) {
                throw new RuntimeException("Invalid aggregated workflow data for workflow bot IDs: " + workflowBotIds);
            }

            log.info("Step 2 completed: Successfully aggregated workflow data with {} total intents from {} questions",
                    analysis.getAllIntents().size(), analysis.getQuestions().size());

            // Step 3: Generate conversation flows
            List<ConversationFlow> conversationFlows = generateConversationFlows(analysis);

            // Build response
            return buildTestLessonResponse(botId, analysis, conversationFlows);

        } catch (Exception e) {
            log.error("Error testing lesson workflow for lesson bot ID: {}", botId, e);
            throw new RuntimeException("Failed to test lesson workflow: " + e.getMessage(), e);
        }
    }

    /**
     * Fetch and aggregate workflow data from multiple workflow bot IDs
     */
    private WorkflowAnalysis fetchAndAggregateWorkflowData(List<Long> workflowBotIds) throws IOException {
        List<WorkflowExcelDTO> aggregatedDataExcel = new ArrayList<>();
        Map<String, Long> intentToWorkflowMap = new HashMap<>(); // Track intent -> workflow ID mapping
        List<WorkflowInfo> workflowInfos = new ArrayList<>(); // Maintain workflow order

        for (int i = 0; i < workflowBotIds.size(); i++) {
            Long workflowBotId = workflowBotIds.get(i);
            log.info("Fetching workflow data for workflow bot ID: {} ({}/{})", workflowBotId, i + 1,
                    workflowBotIds.size());

            WorkflowResDTO workflow = fetchWorkflowData(workflowBotId);
            if (workflow != null && workflow.getResult() != null && workflow.getResult().getDataExcel() != null) {
                List<WorkflowExcelDTO> workflowData = workflow.getResult().getDataExcel();
                
                // Track intent-workflow mapping
                for (WorkflowExcelDTO item : workflowData) {
                    if (item.getIntentName() != null && !item.getIntentName().trim().isEmpty()) {
                        intentToWorkflowMap.put(item.getIntentName(), workflowBotId);
                    }
                }
                
                // Analyze this specific workflow to get its questions
                List<QuestionInfo> workflowQuestions = analyzeWorkflowQuestions(workflowData, workflowBotId);
                
                // Create WorkflowInfo for this workflow
                WorkflowInfo workflowInfo = WorkflowInfo.builder()
                        .workflowId(workflowBotId)
                        .order(i + 1)
                        .questions(workflowQuestions)
                        .build();
                workflowInfos.add(workflowInfo);
                
                aggregatedDataExcel.addAll(workflowData);
                log.info("Added {} excel entries from workflow bot ID: {} with {} questions",
                        workflowData.size(), workflowBotId, workflowQuestions.size());
            } else {
                log.warn("Invalid workflow data for workflow bot ID: {}", workflowBotId);
            }
        }

        log.info("Total aggregated excel entries: {}", aggregatedDataExcel.size());
        WorkflowAnalysis analysis = analyzeWorkflow(aggregatedDataExcel, intentToWorkflowMap);
        analysis.setWorkflows(workflowInfos); // Add workflow order information
        
        log.info("Created WorkflowAnalysis with {} workflows:", workflowInfos.size());
        for (WorkflowInfo workflow : workflowInfos) {
            log.info("  Workflow {} (order {}): {} questions", 
                    workflow.getWorkflowId(), workflow.getOrder(), workflow.getQuestions().size());
        }
        
        return analysis;
    }

    /**
     * Fetch workflow data from external API
     */
    private WorkflowResDTO fetchWorkflowData(Long botId) throws IOException {
        String url = "https://ai-tools-api.hacknao.edu.vn:19403/robot-ai-workflow/api/v1/database/getDataBot?bot_id="
                + botId;
        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to fetch workflow. Response code: " + response.code());
            }

            String responseBody = response.body().string();
            return objectMapper.readValue(responseBody, WorkflowResDTO.class);
        }
    }

    /**
     * Fetch workflow bot IDs from lesson API by calling the lesson API first
     */
    private List<Long> fetchWorkflowBotIdsFromLesson(Long lessonBotId) throws IOException {
        String url = "https://ai-tools-api.hacknao.edu.vn:19404/robot-ai-lesson/api/v1/database/getDataBot?bot_id="
                + lessonBotId;
        log.info("Calling lesson API: {}", url);

        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Failed to fetch lesson data from API. Response code: {}, URL: {}", response.code(), url);
                throw new IOException("Failed to fetch lesson data. Response code: " + response.code());
            }

            String responseBody = response.body().string();
            log.debug("Lesson API response body: {}", responseBody);

            try {
                LessonApiResponseDTO lessonResponse = objectMapper.readValue(responseBody, LessonApiResponseDTO.class);

                if (lessonResponse.getStatus() != 0) {
                    log.warn("Lesson API returned non-zero status: {} for bot ID: {}", lessonResponse.getStatus(),
                            lessonBotId);
                    return new ArrayList<>();
                }

                if (lessonResponse.getResult() == null) {
                    log.warn("Lesson API returned null result for bot ID: {}", lessonBotId);
                    return new ArrayList<>();
                }

                if (lessonResponse.getResult().getScenario() == null
                        || lessonResponse.getResult().getScenario().isEmpty()) {
                    log.warn("Lesson API returned null or empty scenario for bot ID: {}", lessonBotId);
                    return new ArrayList<>();
                }

                log.info("Lesson API returned {} scenarios for bot ID: {}",
                        lessonResponse.getResult().getScenario().size(), lessonBotId);

                // Find all scenarios with robot_type = "Workflow"
                List<Long> workflowBotIds = new ArrayList<>();
                for (LessonApiResponseDTO.LessonScenarioDTO scenario : lessonResponse.getResult().getScenario()) {
                    log.debug("Checking scenario: robot_type={}, robot_type_id={}",
                            scenario.getRobotType(), scenario.getRobotTypeId());

                    if ("workflow".equalsIgnoreCase(scenario.getRobotType()) && scenario.getRobotTypeId() != null) {
                        log.info("Found workflow scenario with robot_type_id: {} for lesson bot ID: {}",
                                scenario.getRobotTypeId(), lessonBotId);
                        workflowBotIds.add(scenario.getRobotTypeId());
                    }
                }

                if (workflowBotIds.isEmpty()) {
                    log.warn("No workflow scenario found for lesson bot ID: {}. Available scenarios: {}",
                            lessonBotId, lessonResponse.getResult().getScenario().stream()
                                    .map(s -> s.getRobotType() + ":" + s.getRobotTypeId())
                                    .collect(Collectors.joining(", ")));
                }

                return workflowBotIds;

            } catch (Exception e) {
                log.error("Failed to parse lesson API response for bot ID: {}. Response body: {}", lessonBotId,
                        responseBody, e);
                throw new IOException("Failed to parse lesson API response: " + e.getMessage(), e);
            }
        } catch (IOException e) {
            log.error("IO error while calling lesson API for bot ID: {}", lessonBotId, e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error while calling lesson API for bot ID: {}", lessonBotId, e);
            throw new IOException("Unexpected error while calling lesson API: " + e.getMessage(), e);
        }
    }

    /**
     * Analyze single workflow data to get questions for that workflow
     */
    private List<QuestionInfo> analyzeWorkflowQuestions(List<WorkflowExcelDTO> workflowData, Long workflowId) {
        List<QuestionInfo> questions = new ArrayList<>();
        log.info("Analyzing workflow {} with {} data entries", workflowId, workflowData.size());

        for (int i = 0; i < workflowData.size(); i++) {
            WorkflowExcelDTO item = workflowData.get(i);

            // Check if this is a question (has QUESTION field with content)
            if (item.getQuestion() != null && !item.getQuestion().trim().isEmpty()) {
                List<String> intents = new ArrayList<>();
                List<String> intentDescription = new ArrayList<>();
                List<Integer> intentLoopCounts = new ArrayList<>();
                List<Long> intentWorkflowIds = new ArrayList<>();

                // Find all intents that follow this question
                for (int j = i + 1; j < workflowData.size(); j++) {
                    WorkflowExcelDTO intentItem = workflowData.get(j);
                    if (intentItem.getIntentName() != null && !intentItem.getIntentName().trim().isEmpty()) {
                        intents.add(intentItem.getIntentName());
                        intentDescription.add(intentItem.getIntentDescription());
                        
                        // Parse loop count (default 1 if null or invalid)
                        int loopCount = parseLoopCount(intentItem.getLoopCount());
                        intentLoopCounts.add(loopCount);
                        
                        // All intents in this workflow belong to the same workflow ID
                        intentWorkflowIds.add(workflowId);
                    } else {
                        // Stop when we hit another question
                        break;
                    }
                }

                if (!intents.isEmpty()) {
                    // Deduplicate intents by name and keep the maximum loop count
                    Map<String, QuestionIntentInfo> intentMap = deduplicateIntents(intents, intentDescription, intentLoopCounts, intentWorkflowIds);
                    
                    // Convert back to lists maintaining order
                    List<String> deduplicatedIntents = new ArrayList<>();
                    List<String> deduplicatedDescriptions = new ArrayList<>();
                    List<Integer> deduplicatedLoopCounts = new ArrayList<>();
                    List<Long> deduplicatedWorkflowIds = new ArrayList<>();
                    
                    for (QuestionIntentInfo intentInfo : intentMap.values()) {
                        deduplicatedIntents.add(intentInfo.getIntentName());
                        deduplicatedDescriptions.add(intentInfo.getDescription());
                        deduplicatedLoopCounts.add(intentInfo.getLoopCount());
                        deduplicatedWorkflowIds.add(intentInfo.getWorkflowId());
                    }
                    
                    QuestionInfo questionInfo = QuestionInfo.builder()
                            .index(i)
                            .questionText(extractQuestionText(item.getQuestion()))
                            .intents(deduplicatedIntents)
                            .intentDescription(deduplicatedDescriptions)
                            .intentLoopCounts(deduplicatedLoopCounts)
                            .intentWorkflowIds(deduplicatedWorkflowIds)
                            .build();
                    questions.add(questionInfo);
                    
                    log.info("Found question {} in workflow {}: {} intents (deduplicated from {} original intents)", 
                            i, workflowId, deduplicatedIntents.size(), intents.size());
                }
            }
        }

        log.info("Completed analyzing workflow {}: found {} questions", workflowId, questions.size());
        return questions;
    }

    /**
     * Analyze workflow structure to identify questions and intents
     */
    private WorkflowAnalysis analyzeWorkflow(List<WorkflowExcelDTO> dataExcel, Map<String, Long> intentToWorkflowMap) {
        List<QuestionInfo> questions = new ArrayList<>();
        // Note: allIntents will be calculated after deduplication

        for (int i = 0; i < dataExcel.size(); i++) {
            WorkflowExcelDTO item = dataExcel.get(i);

            // Check if this is a question (has QUESTION field with content)
            if (item.getQuestion() != null && !item.getQuestion().trim().isEmpty()) {
                List<String> intents = new ArrayList<>();
                List<String> intentDescription = new ArrayList<>();
                List<Integer> intentLoopCounts = new ArrayList<>();
                List<Long> intentWorkflowIds = new ArrayList<>();

                // Find all intents that follow this question
                for (int j = i + 1; j < dataExcel.size(); j++) {
                    WorkflowExcelDTO intentItem = dataExcel.get(j);
                    if (intentItem.getIntentName() != null && !intentItem.getIntentName().trim().isEmpty()) {
                        intents.add(intentItem.getIntentName());
                        intentDescription.add(intentItem.getIntentDescription());
                        
                        // Parse loop count (default 1 if null or invalid)
                        int loopCount = parseLoopCount(intentItem.getLoopCount());
                        intentLoopCounts.add(loopCount);
                        
                        // Add workflow ID for this intent
                        Long workflowId = intentToWorkflowMap.get(intentItem.getIntentName());
                        intentWorkflowIds.add(workflowId);
                    } else {
                        // Stop when we hit another question
                        break;
                    }
                }

                if (!intents.isEmpty()) {
                    // Deduplicate intents by name and keep the maximum loop count
                    Map<String, QuestionIntentInfo> intentMap = deduplicateIntents(intents, intentDescription, intentLoopCounts, intentWorkflowIds);
                    
                    // Convert back to lists maintaining order
                    List<String> deduplicatedIntents = new ArrayList<>();
                    List<String> deduplicatedDescriptions = new ArrayList<>();
                    List<Integer> deduplicatedLoopCounts = new ArrayList<>();
                    List<Long> deduplicatedWorkflowIds = new ArrayList<>();
                    
                    for (QuestionIntentInfo intentInfo : intentMap.values()) {
                        deduplicatedIntents.add(intentInfo.getIntentName());
                        deduplicatedDescriptions.add(intentInfo.getDescription());
                        deduplicatedLoopCounts.add(intentInfo.getLoopCount());
                        deduplicatedWorkflowIds.add(intentInfo.getWorkflowId());
                        // Note: allIntents will be populated after deduplication
                    }
                    
                    questions.add(QuestionInfo.builder()
                            .index(i)
                            .questionText(extractQuestionText(item.getQuestion()))
                            .intents(deduplicatedIntents)
                            .intentDescription(deduplicatedDescriptions)
                            .intentLoopCounts(deduplicatedLoopCounts)
                            .intentWorkflowIds(deduplicatedWorkflowIds)
                            .build());
                    
                    log.info("Found aggregated question {} with {} intents (deduplicated from {} original intents)", 
                            i, deduplicatedIntents.size(), intents.size());
                }
            }
        }

        // Recalculate allIntents after deduplication
        Set<String> finalAllIntents = new HashSet<>();
        for (QuestionInfo question : questions) {
            finalAllIntents.addAll(question.getIntents());
        }
        
        // Recalculate maxIntentsPerQuestion after deduplication
        int maxIntentsPerQuestion = questions.stream()
                .mapToInt(q -> q.getIntents().size())
                .max()
                .orElse(0);
        
        log.info("After deduplication: {} total unique intents, max {} intents per question", 
                finalAllIntents.size(), maxIntentsPerQuestion);

        return WorkflowAnalysis.builder()
                .questions(questions)
                .allIntents(finalAllIntents)
                .maxIntentsPerQuestion(maxIntentsPerQuestion)
                .workflows(new ArrayList<>()) // Initialize empty, will be populated later
                .build();
    }

    /**
     * Extract readable question text from QUESTION field (may contain JSON)
     */
    private String extractQuestionText(String questionField) {
        if (questionField == null || questionField.trim().isEmpty()) {
            return "";
        }

        try {
            // Try to parse as JSON first
            List<Map<String, Object>> questionData = objectMapper.readValue(questionField,
                    new TypeReference<List<Map<String, Object>>>() {
                    });
            StringBuilder questionText = new StringBuilder();

            for (Map<String, Object> item : questionData) {
                Object text = item.get("text");
                if (text != null && !text.toString().trim().isEmpty()) {
                    questionText.append(text.toString().trim()).append(" ");
                }
            }

            return questionText.toString().trim();
        } catch (JsonProcessingException e) {
            // If not JSON, return as is
            return questionField.trim();
        }
    }

    /**
     * Generate conversation flows to cover all intents following workflow order
     */
    private List<ConversationFlow> generateConversationFlows(WorkflowAnalysis analysis) {
        List<ConversationFlow> flows = new ArrayList<>();
        int maxIntents = analysis.getMaxIntentsPerQuestion();
        
        log.info("generateConversationFlows: Starting with {} workflows, maxIntents per question: {}", 
                analysis.getWorkflows() != null ? analysis.getWorkflows().size() : 0, maxIntents);

        // Create flows for each intent index
        for (int intentIndex = 0; intentIndex < maxIntents; intentIndex++) {
            List<ConversationStep> steps = new ArrayList<>();

            // Đi theo thứ tự workflow -> question -> intent
            if (analysis.getWorkflows() != null && !analysis.getWorkflows().isEmpty()) {
                for (WorkflowInfo workflowInfo : analysis.getWorkflows()) {
                    log.info("Processing workflow {} (order {}) for intent index {}", 
                            workflowInfo.getWorkflowId(), workflowInfo.getOrder(), intentIndex);
                    
                    // Đi qua từng câu hỏi trong workflow này
                    for (QuestionInfo question : workflowInfo.getQuestions()) {
                        // Get intent at current index, wrap around if needed
                        int intentPos = intentIndex % question.getIntents().size();
                        String intentName = question.getIntents().get(intentPos);
                        String intentDescription = question.getIntentDescription().get(intentPos);
                        int loopCount = question.getIntentLoopCounts().get(intentPos);
                        Long workflowId = question.getIntentWorkflowIds().get(intentPos);

                        steps.add(ConversationStep.builder()
                                .questionIndex(question.getIndex())
                                .questionText(question.getQuestionText())
                                .intentName(intentName)
                                .intentDescription(intentDescription)
                                .expectedResponse("Expected response for " + intentName)
                                .loopCount(loopCount)
                                .workflowId(workflowId)
                                .build());
                    }
                }
            } else {
                log.warn("No workflows found in analysis, falling back to legacy questions method");
                // Fallback to legacy method if workflows not available
                for (QuestionInfo question : analysis.getQuestions()) {
                    // Get intent at current index, wrap around if needed
                    int intentPos = intentIndex % question.getIntents().size();
                    String intentName = question.getIntents().get(intentPos);
                    String intentDescription = question.getIntentDescription().get(intentPos);
                    int loopCount = question.getIntentLoopCounts().get(intentPos);
                    Long workflowId = question.getIntentWorkflowIds() != null && 
                            !question.getIntentWorkflowIds().isEmpty() ? 
                            question.getIntentWorkflowIds().get(intentPos) : null;

                    steps.add(ConversationStep.builder()
                            .questionIndex(question.getIndex())
                            .questionText(question.getQuestionText())
                            .intentName(intentName)
                            .intentDescription(intentDescription)
                            .expectedResponse("Expected response for " + intentName)
                            .loopCount(loopCount)
                            .workflowId(workflowId)
                            .build());
                }
            }

            // Calculate total steps considering loop count for each intent
            int totalStepsWithLoopCount = steps.stream()
                    .mapToInt(ConversationStep::getLoopCount)
                    .sum();
            
            flows.add(ConversationFlow.builder()
                    .conversationId(intentIndex + 1)
                    .steps(steps)
                    .totalSteps(totalStepsWithLoopCount)
                    .build());
        }

        int totalStepsAllFlows = flows.stream().mapToInt(ConversationFlow::getTotalSteps).sum();
        log.info("Generated {} conversation flows following workflow order with {} total steps (including loop counts)", 
                flows.size(), totalStepsAllFlows);
        return flows;
    }

    /**
     * Execute multiple workflows sequentially with ACTION handling between
     * workflows
     * @deprecated Use executeMultipleWorkflowsFlowWithTracking for enhanced tracking
     */
    @Deprecated
    @SuppressWarnings("unused")
    private TestFlowResultDTO executeMultipleWorkflowsFlow(FullTestLessonReqDTO request, List<Long> workflowBotIds,
            ConversationFlow flow, int flowId) {
        // Check if this is a random test flow (has workflow IDs in steps)
        boolean isRandomTest = flow.getSteps().stream()
                .anyMatch(step -> step.getWorkflowId() != null);
        log.info("Starting multiple workflows execution for flow ID: {} with {} workflows", flowId,
                workflowBotIds.size());
        long flowStartTime = System.currentTimeMillis();

        String sessionId = "test-session-" + flowId + "-" + System.currentTimeMillis();
        List<TestInteractionDTO> interactions = new ArrayList<>();
        List<ConversationMessageDTO> conversationHistory = new ArrayList<>();

        String finalStatus = null;
        String errorMessage = null;
        boolean completedSuccessfully = false;
        Long conversationId = null;
        
        // Track workflow details
        List<WorkflowDetailDTO> workflowDetails = new ArrayList<>();
        int currentWorkflowIndex = 0;
        int workflowStartStep = 1;
        List<WorkflowDetailDTO.IntentWithLoopCount> currentWorkflowIntents = new ArrayList<>(); // ordered intents with loop counts

        try {
            // Step 3.1: Initialize conversation with the lesson
            String initText = "Hello";
            String ip = "127.0.0.1";
            ConversationSource conversationSource = ConversationSource.TEST;

            log.info("Step 3.1: Initializing conversation for flow {}", flowId);
            long stepStartTime = System.currentTimeMillis();

            RobotConversationMsgResDTO initResponse = aiRobotConversationService.sendCustomUserSentence(
                    request.getUserId(), null, initText, sessionId, ip,
                    request.getBotId(), new StringBuilder(), null, null,
                    false, conversationSource);

            long stepResponseTime = System.currentTimeMillis() - stepStartTime;

            // Extract initial AI response text for saving to conversation history
            String initialAIResponseText = extractResponseText(initResponse);
            // Extract conversation id if available
            conversationId = extractConversationId(initResponse);

            // Add initial interaction
            interactions.add(TestInteractionDTO.builder()
                    .stepNumber(1)
                    .userInput(initText)
                    .targetIntent("init")
                    .robotResponse(initialAIResponseText)
                    .robotStatus(extractResponseStatus(initResponse))
                    .timestamp(System.currentTimeMillis())
                    .tokensUsed(0)
                    .responseTimeMs(stepResponseTime)
                    .build());

            // Add to conversation history
            conversationHistory.add(ConversationMessageDTO.builder()
                    .role("user")
                    .content(initText)
                    .timestamp(System.currentTimeMillis())
                    .build());
            conversationHistory.add(ConversationMessageDTO.builder()
                    .role("assistant")
                    .content(initialAIResponseText)
                    .timestamp(System.currentTimeMillis())
                    .build());

            finalStatus = extractResponseStatus(initResponse);

            // Execute conversation through each workflow sequentially
            int maxInteractions = 50;
            int currentStep = 2;
            int flowStepIndex = 0;
            int currentStepLoopCount = 0; // Track how many times we've looped on current step

            // Keep track of the last robot response for language extraction
            RobotConversationMsgResDTO lastRobotResponse = initResponse;

            while (!isEndStatus(finalStatus) && currentStep <= maxInteractions
                    && flowStepIndex < flow.getSteps().size()) {
                ConversationStep currentFlowStep = flow.getSteps().get(flowStepIndex);

                log.info("Step 3.3: Generating user response for intent: {} (step {}/{})",
                        currentFlowStep.getIntentName(), currentStep, maxInteractions);
                
                // Track current intent for workflow details
                String intentToTrack = currentFlowStep.getIntentName();
                Long stepWorkflowId = currentFlowStep.getWorkflowId();
                int intentLoopCountFromExcel = currentFlowStep.getLoopCount(); // Loop count from Excel data
                
                // Check if workflow has changed (for random test flow)
                if (isRandomTest && stepWorkflowId != null) {
                    Long currentTrackingWorkflowId = currentWorkflowIndex < workflowBotIds.size() ? 
                            workflowBotIds.get(currentWorkflowIndex) : null;
                    
                    if (!stepWorkflowId.equals(currentTrackingWorkflowId)) {
                        // Workflow transition - save current workflow if it has intents
                        if (!currentWorkflowIntents.isEmpty()) {
                            WorkflowDetailDTO workflowDetail = WorkflowDetailDTO.builder()
                                    .workflowId(currentTrackingWorkflowId)
                                    .workflowOrder(currentWorkflowIndex + 1)
                                    .testedIntents(new ArrayList<>(currentWorkflowIntents))
                                    .startStep(workflowStartStep)
                                    .endStep(currentStep - 1)
                                    .build();
                            workflowDetails.add(workflowDetail);
                            
                            log.info("Workflow transition detected: Saved workflow {} with intents: {}", 
                                    currentTrackingWorkflowId, currentWorkflowIntents);
                        }
                        
                        // Find new workflow index
                        for (int i = 0; i < workflowBotIds.size(); i++) {
                            if (workflowBotIds.get(i).equals(stepWorkflowId)) {
                                currentWorkflowIndex = i;
                                break;
                            }
                        }
                        
                        // Reset for new workflow
                        workflowStartStep = currentStep;
                        currentWorkflowIntents = new ArrayList<>();
                        
                        log.info("Started tracking new workflow {} (index {})", stepWorkflowId, currentWorkflowIndex);
                    }
                }
                
                // Add current intent to workflow tracking - use loop count from Excel data
                currentWorkflowIntents.add(WorkflowDetailDTO.IntentWithLoopCount.builder()
                        .intentName(intentToTrack)
                        .loopCount(intentLoopCountFromExcel) // Use loop count from Excel data
                        .build());

                // Check if the next intent is "silence"
                if ("silence".equalsIgnoreCase(currentFlowStep.getIntentName())) {
                    log.info("Step 3.3: Detected silence intent, sending 'uhm...' to LLM service");

                    // For silence intent, send "uhm..." to LLM service and store "..." in
                    // conversation history
                    String userInput = "uhm...";
                    String aiResponseText = "...";

                    // Save silence input to conversation history as USER
                    try {
                        aiRobotConversationService.saveUserInputForTest(sessionId, userInput, "en");
                        log.info("Saved silence user input to conversation history: {}", userInput);
                    } catch (Exception e) {
                        log.warn("Failed to save silence user input to conversation history: {}", e.getMessage());
                    }

                    // Send "uhm..." to LLM service
                    stepStartTime = System.currentTimeMillis();
                    RobotConversationMsgResDTO robotResponse = aiRobotConversationService.sendCustomUserSentence(
                            request.getUserId(), null, userInput, sessionId, ip,
                            request.getBotId(), new StringBuilder(), null, null,
                            false, conversationSource);

                    // Update last robot response for next iteration's language extraction
                    lastRobotResponse = robotResponse;

                    stepResponseTime = System.currentTimeMillis() - stepStartTime;
                    finalStatus = extractResponseStatus(robotResponse);

                    // Record interaction with silence handling
                    interactions.add(TestInteractionDTO.builder()
                            .stepNumber(currentStep)
                            .userInput(userInput)
                            .targetIntent(currentFlowStep.getIntentName())
                            .robotResponse(aiResponseText)
                            .robotStatus(finalStatus)
                            .timestamp(System.currentTimeMillis())
                            .tokensUsed(0) // No tokens used for silence
                            .responseTimeMs(stepResponseTime)
                            .build());

                    // Update conversation history with silence
                    conversationHistory.add(ConversationMessageDTO.builder()
                            .role("user")
                            .content(userInput)
                            .timestamp(System.currentTimeMillis())
                            .build());
                    conversationHistory.add(ConversationMessageDTO.builder()
                            .role("assistant")
                            .content(aiResponseText)
                            .timestamp(System.currentTimeMillis())
                            .build());

                    log.info("Step 3.2: Silence intent handled, checking response status: {}", finalStatus);

                    // Check status - if END or ERROR, stop the loop
                    if (isEndStatus(finalStatus)) {
                        log.info("Conversation ended with status: {}", finalStatus);
                        break;
                    }

                    currentStep++;
                    flowStepIndex++;

                    // If we've gone through all flow steps, loop back to the beginning
                    if (flowStepIndex >= flow.getSteps().size()) {
                        flowStepIndex = 0;
                    }

                    // Small delay to avoid overwhelming the API
                    Thread.sleep(100);
                    continue;
                }

                // Step 3.3: Generate user response using OpenAI for non-silence intents
                stepStartTime = System.currentTimeMillis();

                // Get language from LLM response, fallback to request language, then default to
                // null (which will be "en" in OpenAI service)
                String languageToUse = "en";
                if (lastRobotResponse.getLanguage() != null) {
                    languageToUse = lastRobotResponse.getLanguage();
                }

                GenerateUserResponseReqDTO generateRequest = GenerateUserResponseReqDTO.builder()
                        .conversationHistory(conversationHistory)
                        .targetIntent(currentFlowStep.getIntentName())
                        .intentDescription(currentFlowStep.getIntentDescription())
                        .language(languageToUse)
                        .build();

                GenerateUserResponseResDTO userResponse = openAIService.generateUserResponse(generateRequest);
                String userInput = userResponse.getGeneratedResponse();

                // Save AI-generated user input to conversation history as USER
                if (userInput != null && !userInput.trim().isEmpty()) {
                    try {
                        aiRobotConversationService.saveUserInputForTest(sessionId, userInput, languageToUse);
                        log.info("Saved AI-generated user input to conversation history: {}", userInput);
                    } catch (Exception e) {
                        log.warn("Failed to save AI-generated user input to conversation history: {}", e.getMessage());
                    }
                }

                // Step 3.4: Send user response to LLM service
                log.info("Step 3.4: Sending user response to LLM: {}", userInput);

                RobotConversationMsgResDTO robotResponse = aiRobotConversationService.sendCustomUserSentence(
                        request.getUserId(), null, userInput, sessionId, ip,
                        request.getBotId(), new StringBuilder(), null, null,
                        false, conversationSource);

                // Update last robot response for next iteration's language extraction
                lastRobotResponse = robotResponse;

                stepResponseTime = System.currentTimeMillis() - stepStartTime;
                finalStatus = extractResponseStatus(robotResponse);
                if (conversationId == null) {
                    conversationId = extractConversationId(robotResponse);
                }

                // Extract AI response text for saving to conversation history
                String aiResponseText = extractResponseText(robotResponse);

                // Validate image if exists in messages
                validateImages(robotResponse);

                // Record interaction
                interactions.add(TestInteractionDTO.builder()
                        .stepNumber(currentStep)
                        .userInput(userInput)
                        .targetIntent(currentFlowStep.getIntentName())
                        .robotResponse(aiResponseText)
                        .robotStatus(finalStatus)
                        .timestamp(System.currentTimeMillis())
                        .tokensUsed(userResponse.getTokensUsed())
                        .responseTimeMs(stepResponseTime)
                        .build());

                // Update conversation history
                conversationHistory.add(ConversationMessageDTO.builder()
                        .role("user")
                        .content(userInput)
                        .timestamp(System.currentTimeMillis())
                        .build());
                conversationHistory.add(ConversationMessageDTO.builder()
                        .role("assistant")
                        .content(aiResponseText)
                        .timestamp(System.currentTimeMillis())
                        .build());

                log.info("Step 3.2: Checking response status: {}", finalStatus);

                // Step 3.5: Handle ACTION status - transition to next workflow (only for non-random tests)
                if (!isRandomTest && "ACTION".equalsIgnoreCase(finalStatus)) {
                    log.info("Step 3.5: Detected ACTION status, sending ACTION message to transition to next workflow");
                    
                    // Save current workflow details before transitioning
                    if (currentWorkflowIndex < workflowBotIds.size() && !currentWorkflowIntents.isEmpty()) {
                        WorkflowDetailDTO workflowDetail = WorkflowDetailDTO.builder()
                                .workflowId(workflowBotIds.get(currentWorkflowIndex))
                                .workflowOrder(currentWorkflowIndex + 1)
                                .testedIntents(new ArrayList<>(currentWorkflowIntents))
                                .startStep(workflowStartStep)
                                .endStep(currentStep)
                                .build();
                        workflowDetails.add(workflowDetail);
                        
                        log.info("Step 3.5: Completed workflow {} (order {}) with intents: {}", 
                                workflowBotIds.get(currentWorkflowIndex), 
                                currentWorkflowIndex + 1, 
                                currentWorkflowIntents);
                        
                        // Reset for next workflow
                        currentWorkflowIndex++;
                        workflowStartStep = currentStep + 1;
                        currentWorkflowIntents = new ArrayList<>();
                    }

                    // Send ACTION message (without saving to conversation history)
                    stepStartTime = System.currentTimeMillis();
                    RobotConversationMsgResDTO actionResponse = aiRobotConversationService.sendCustomUserSentence(
                            request.getUserId(), null, "ACTION", sessionId, ip,
                            request.getBotId(), new StringBuilder(), null, null,
                            false, conversationSource);

                    lastRobotResponse = actionResponse;
                    stepResponseTime = System.currentTimeMillis() - stepStartTime;
                    finalStatus = extractResponseStatus(actionResponse);

                    // Extract ACTION response text
                    String actionResponseText = extractResponseText(actionResponse);

                    // Record ACTION interaction (but don't add to conversation history)
                    interactions.add(TestInteractionDTO.builder()
                            .stepNumber(currentStep + 1)
                            .userInput("ACTION")
                            .targetIntent("workflow_transition")
                            .robotResponse(actionResponseText)
                            .robotStatus(finalStatus)
                            .timestamp(System.currentTimeMillis())
                            .tokensUsed(0)
                            .responseTimeMs(stepResponseTime)
                            .build());

                    currentStep++;

                    log.info("Step 3.5: ACTION message sent, new status: {}", finalStatus);

                    // Continue to next iteration without updating flowStepIndex
                    // to allow the current workflow to complete before moving to next one
                }

                // Small delay to avoid overwhelming the API
                Thread.sleep(100);
            }

            // Save the last workflow if it has intents and hasn't been saved yet
            if (currentWorkflowIndex < workflowBotIds.size() && !currentWorkflowIntents.isEmpty()) {
                WorkflowDetailDTO workflowDetail = WorkflowDetailDTO.builder()
                        .workflowId(workflowBotIds.get(currentWorkflowIndex))
                        .workflowOrder(currentWorkflowIndex + 1)
                        .testedIntents(new ArrayList<>(currentWorkflowIntents))
                        .startStep(workflowStartStep)
                        .endStep(currentStep - 1)
                        .build();
                workflowDetails.add(workflowDetail);
                
                log.info("Saved final workflow {} (order {}) with intents: {}", 
                        workflowBotIds.get(currentWorkflowIndex), 
                        currentWorkflowIndex + 1, 
                        currentWorkflowIntents);
            }

            // Determine if completed successfully
            completedSuccessfully = "END".equalsIgnoreCase(finalStatus) ||
                    (!isErrorStatus(finalStatus) && flowStepIndex >= flow.getSteps().size());

        } catch (Exception e) {
            log.error("Error executing multiple workflows flow {}: {}", flowId, e.getMessage(), e);
            errorMessage = e.getMessage();
            finalStatus = "ERROR";
            completedSuccessfully = false;
        }

        long flowExecutionTime = (System.currentTimeMillis() - flowStartTime) / 1000;

        return TestFlowResultDTO.builder()
                .flowId(flowId)
                .flowName("Multi-Workflow Flow " + flowId + " - " + workflowBotIds.size() + " workflows, "
                        + flow.getSteps().size() + " steps")
                .conversationId(conversationId)
                .sessionId(sessionId)
                .totalInteractions(interactions.size())
                .completedSuccessfully(completedSuccessfully)
                .finalStatus(finalStatus)
                .errorMessage(errorMessage)
                .interactions(interactions)
                .conversationHistory(conversationHistory)
                .executionTimeSeconds(flowExecutionTime)
                .workflowDetails(workflowDetails)
                .build();
    }

    /**
     * Execute multiple workflows with enhanced tracking mechanism
     */
    private TestFlowResultDTO executeMultipleWorkflowsFlowWithTracking(FullTestLessonReqDTO request, List<Long> workflowBotIds,
            ConversationFlow flow, int flowId) {
        log.info("Starting enhanced tracking multiple workflows execution for flow ID: {}", flowId);
        long flowStartTime = System.currentTimeMillis();

        String sessionId = "test-session-" + flowId + "-" + System.currentTimeMillis();
        List<TestInteractionDTO> interactions = new ArrayList<>();
        List<ConversationMessageDTO> conversationHistory = new ArrayList<>();

        String finalStatus = null;
        String errorMessage = null;
        boolean completedSuccessfully = false;
        Long conversationId = null;
        
        // Enhanced tracking state
        TestExecutionStateDTO executionState = initializeExecutionState(flow, workflowBotIds);
        List<WorkflowDetailDTO> workflowDetails = new ArrayList<>();

        try {
            // Step 1: Initialize conversation
            String initText = "Hello";
            String ip = "127.0.0.1";
            ConversationSource conversationSource = ConversationSource.TEST;

            log.info("Step 1: Initializing conversation for flow {}", flowId);
            long stepStartTime = System.currentTimeMillis();

            RobotConversationMsgResDTO initResponse = aiRobotConversationService.sendCustomUserSentence(
                    request.getUserId(), null, initText, sessionId, ip,
                    request.getBotId(), new StringBuilder(), null, null,
                    false, conversationSource);

            long stepResponseTime = System.currentTimeMillis() - stepStartTime;
            String initialAIResponseText = extractResponseText(initResponse);
            conversationId = extractConversationId(initResponse);

            // Add initial interaction
            interactions.add(TestInteractionDTO.builder()
                    .stepNumber(1)
                    .userInput(initText)
                    .targetIntent("init")
                    .robotResponse(initialAIResponseText)
                    .robotStatus(extractResponseStatus(initResponse))
                    .timestamp(System.currentTimeMillis())
                    .tokensUsed(0)
                    .responseTimeMs(stepResponseTime)
                    .build());

            conversationHistory.add(ConversationMessageDTO.builder()
                    .role("user")
                    .content(initText)
                    .timestamp(System.currentTimeMillis())
                    .build());
            conversationHistory.add(ConversationMessageDTO.builder()
                    .role("assistant")
                    .content(initialAIResponseText)
                    .timestamp(System.currentTimeMillis())
                    .build());

            finalStatus = extractResponseStatus(initResponse);
            RobotConversationMsgResDTO lastRobotResponse = initResponse;

            // Step 2: Execute conversation with enhanced tracking
            int maxInteractions = 50;
            int currentStep = 2;

            while (!isEndStatus(finalStatus) && currentStep <= maxInteractions) {
                // Get current step to execute
                ConversationStep currentFlowStep = null;
                if (executionState.getCurrentStepIndex() < flow.getSteps().size()) {
                    currentFlowStep = flow.getSteps().get(executionState.getCurrentStepIndex());
                } else {
                    log.info("All flow steps completed, ending conversation");
                    break;
                }

                log.info("Executing intent: {} (step {}/{}) - executed {}/{} times",
                        currentFlowStep.getIntentName(), currentStep, maxInteractions,
                        executionState.getIntentExecutionCounts().getOrDefault(currentFlowStep.getIntentName(), 0),
                        currentFlowStep.getLoopCount());

                // Handle silence intent
                if ("silence".equalsIgnoreCase(currentFlowStep.getIntentName())) {
                    String userInput = "uhm...";
                    String aiResponseText = "...";

                    try {
                        aiRobotConversationService.saveUserInputForTest(sessionId, userInput, "en");
                    } catch (Exception e) {
                        log.warn("Failed to save silence user input: {}", e.getMessage());
                    }

                    stepStartTime = System.currentTimeMillis();
                    RobotConversationMsgResDTO robotResponse = aiRobotConversationService.sendCustomUserSentence(
                            request.getUserId(), null, userInput, sessionId, ip,
                            request.getBotId(), new StringBuilder(), null, null,
                            false, conversationSource);

                    lastRobotResponse = robotResponse;
                    stepResponseTime = System.currentTimeMillis() - stepStartTime;
                    finalStatus = extractResponseStatus(robotResponse);

                    interactions.add(TestInteractionDTO.builder()
                            .stepNumber(currentStep)
                            .userInput(userInput)
                            .targetIntent(currentFlowStep.getIntentName())
                            .robotResponse(aiResponseText)
                            .robotStatus(finalStatus)
                            .timestamp(System.currentTimeMillis())
                            .tokensUsed(0)
                            .responseTimeMs(stepResponseTime)
                            .build());

                    conversationHistory.add(ConversationMessageDTO.builder()
                            .role("user")
                            .content(userInput)
                            .timestamp(System.currentTimeMillis())
                            .build());
                    conversationHistory.add(ConversationMessageDTO.builder()
                            .role("assistant")
                            .content(aiResponseText)
                            .timestamp(System.currentTimeMillis())
                            .build());
                } else {
                    // Handle normal intent with OpenAI generation
                    stepStartTime = System.currentTimeMillis();

                    String languageToUse = "en";
                    if (lastRobotResponse.getLanguage() != null) {
                        languageToUse = lastRobotResponse.getLanguage();
                    }

                    GenerateUserResponseReqDTO generateRequest = GenerateUserResponseReqDTO.builder()
                            .conversationHistory(conversationHistory)
                            .targetIntent(currentFlowStep.getIntentName())
                            .intentDescription(currentFlowStep.getIntentDescription())
                            .language(languageToUse)
                            .build();

                    GenerateUserResponseResDTO userResponse = openAIService.generateUserResponse(generateRequest);
                    String userInput = userResponse.getGeneratedResponse();

                    if (userInput != null && !userInput.trim().isEmpty()) {
                        try {
                            aiRobotConversationService.saveUserInputForTest(sessionId, userInput, languageToUse);
                        } catch (Exception e) {
                            log.warn("Failed to save user input: {}", e.getMessage());
                        }
                    }

                    RobotConversationMsgResDTO robotResponse = aiRobotConversationService.sendCustomUserSentence(
                            request.getUserId(), null, userInput, sessionId, ip,
                            request.getBotId(), new StringBuilder(), null, null,
                            false, conversationSource);

                    lastRobotResponse = robotResponse;
                    stepResponseTime = System.currentTimeMillis() - stepStartTime;
                    finalStatus = extractResponseStatus(robotResponse);
                    if (conversationId == null) {
                        conversationId = extractConversationId(robotResponse);
                    }

                    String aiResponseText = extractResponseText(robotResponse);
                    validateImages(robotResponse);

                    interactions.add(TestInteractionDTO.builder()
                            .stepNumber(currentStep)
                            .userInput(userInput)
                            .targetIntent(currentFlowStep.getIntentName())
                            .robotResponse(aiResponseText)
                            .robotStatus(finalStatus)
                            .timestamp(System.currentTimeMillis())
                            .tokensUsed(userResponse.getTokensUsed())
                            .responseTimeMs(stepResponseTime)
                            .build());

                    conversationHistory.add(ConversationMessageDTO.builder()
                            .role("user")
                            .content(userInput)
                            .timestamp(System.currentTimeMillis())
                            .build());
                    conversationHistory.add(ConversationMessageDTO.builder()
                            .role("assistant")
                            .content(aiResponseText)
                            .timestamp(System.currentTimeMillis())
                            .build());
                }

                // Update execution state after intent execution
                updateExecutionState(executionState, currentFlowStep);

                // Check if we should advance to next question/workflow
                if (executionState.getShouldMoveToNextQuestion()) {
                    log.info("Intent '{}' completed required loops, advancing to next question",
                            currentFlowStep.getIntentName());
                    executionState.setShouldMoveToNextQuestion(false);
                    executionState.setCurrentStepIndex(executionState.getCurrentStepIndex() + 1);
                    
                    // Reset intent execution count for the new question
                    if (executionState.getCurrentStepIndex() < flow.getSteps().size()) {
                        ConversationStep nextStep = flow.getSteps().get(executionState.getCurrentStepIndex());
                        executionState.setCurrentIntentName(nextStep.getIntentName());
                        executionState.setCurrentIntentLoopCount(nextStep.getLoopCount());
                        executionState.setCurrentIntentExecutedCount(0);
                    }
                }

                if (isEndStatus(finalStatus)) {
                    log.info("Conversation ended with status: {}", finalStatus);
                    break;
                }

                currentStep++;
                Thread.sleep(100);
            }

            // Build workflow details with execution history based on question order
            workflowDetails = buildWorkflowDetailsFromQuestionExecution(flow, workflowBotIds, executionState);

            completedSuccessfully = "END".equalsIgnoreCase(finalStatus) || !isErrorStatus(finalStatus);

        } catch (Exception e) {
            log.error("Error executing enhanced tracking flow {}: {}", flowId, e.getMessage(), e);
            errorMessage = e.getMessage();
            finalStatus = "ERROR";
            completedSuccessfully = false;
        }

        long flowExecutionTime = (System.currentTimeMillis() - flowStartTime) / 1000;

        return TestFlowResultDTO.builder()
                .flowId(flowId)
                .flowName("Enhanced Tracking Flow " + flowId + " - " + workflowBotIds.size() + " workflows, " 
                        + flow.getTotalSteps() + " total steps (with loop counts)")
                .conversationId(conversationId)
                .sessionId(sessionId)
                .totalInteractions(interactions.size())
                .completedSuccessfully(completedSuccessfully)
                .finalStatus(finalStatus)
                .errorMessage(errorMessage)
                .interactions(interactions)
                .conversationHistory(conversationHistory)
                .executionTimeSeconds(flowExecutionTime)
                .workflowDetails(workflowDetails)
                .build();
    }

    /**
     * Find which workflow an intent belongs to
     */
    @SuppressWarnings("unused")
    private Long findWorkflowForIntent(String intentName, ConversationFlow flow) {
        for (ConversationStep step : flow.getSteps()) {
            if (intentName.equals(step.getIntentName())) {
                return step.getWorkflowId();
            }
        }
        return null;
    }

    /**
     * Find expected loop count for an intent
     */
    @SuppressWarnings("unused")
    private Integer findExpectedLoopCount(String intentName, ConversationFlow flow) {
        for (ConversationStep step : flow.getSteps()) {
            if (intentName.equals(step.getIntentName())) {
                return step.getLoopCount();
            }
        }
        return 1;
    }
    
    /**
     * Build workflow details with execution history based on question execution order
     * Each question gets its own entry even if intents have same name
     */
    private List<WorkflowDetailDTO> buildWorkflowDetailsFromQuestionExecution(
            ConversationFlow flow, List<Long> workflowBotIds, TestExecutionStateDTO executionState) {
        
        Map<Long, List<WorkflowDetailDTO.IntentExecutionRecord>> workflowExecutionMap = new LinkedHashMap<>();
        
        // Initialize workflow maps
        for (Long workflowId : workflowBotIds) {
            workflowExecutionMap.put(workflowId, new ArrayList<>());
        }
        
        // Process each step in flow order to maintain question sequence
        for (ConversationStep step : flow.getSteps()) {
            Long workflowId = step.getWorkflowId();
            if (workflowId != null && workflowExecutionMap.containsKey(workflowId)) {
                // For reporting, we show the loop count from Excel data (not actual execution count)
                // This represents the expected loop count for this specific question
                workflowExecutionMap.get(workflowId).add(
                    WorkflowDetailDTO.IntentExecutionRecord.builder()
                            .intentName(step.getIntentName())
                            .executionCount(step.getLoopCount()) // Use expected loop count from Excel data
                            .expectedLoopCount(step.getLoopCount())
                            .build()
                );
                
                log.debug("Added question intent '{}' to workflow {} with loop count {} from Excel data",
                        step.getIntentName(), workflowId, step.getLoopCount());
            }
        }
        
        // Build workflow details list
        List<WorkflowDetailDTO> result = new ArrayList<>();
        for (Map.Entry<Long, List<WorkflowDetailDTO.IntentExecutionRecord>> entry : workflowExecutionMap.entrySet()) {
            Long workflowId = entry.getKey();
            List<WorkflowDetailDTO.IntentExecutionRecord> executionHistory = entry.getValue();
            
            if (!executionHistory.isEmpty()) {
                result.add(WorkflowDetailDTO.builder()
                        .workflowId(workflowId)
                        .workflowOrder(workflowBotIds.indexOf(workflowId) + 1)
                        .executionHistory(executionHistory)
                        .testedIntents(new ArrayList<>()) // Keep for backward compatibility
                        .startStep(1)
                        .endStep(999) // Will be updated with actual values
                        .build());
                        
                log.info("Built workflow detail for workflow {}: {} questions with intents {}",
                        workflowId, executionHistory.size(), 
                        executionHistory.stream().map(WorkflowDetailDTO.IntentExecutionRecord::getIntentName)
                                .collect(Collectors.toList()));
            }
        }
        
        return result;
    }

    /**
     * Execute a single conversation flow according to the specified logic
     * 
     * @deprecated Use executeMultipleWorkflowsFlowWithTracking for new enhanced tracking support
     */
    @Deprecated
    @SuppressWarnings("unused")
    private TestFlowResultDTO executeConversationFlow(FullTestLessonReqDTO request, ConversationFlow flow, int flowId) {
        log.info("Starting conversation flow execution for flow ID: {}", flowId);
        long flowStartTime = System.currentTimeMillis();

        String sessionId = "test-session-" + flowId + "-" + System.currentTimeMillis();
        List<TestInteractionDTO> interactions = new ArrayList<>();
        List<ConversationMessageDTO> conversationHistory = new ArrayList<>();

        String finalStatus = null;
        String errorMessage = null;
        boolean completedSuccessfully = false;
        Long conversationId = null;

        try {
            // Step 3.1: Initialize conversation with the lesson
            String initText = "Hello";
            String ip = "127.0.0.1";
            ConversationSource conversationSource = ConversationSource.TEST;
            /*
             * ConversationSource conversationSource = request.getSource() != null ?
             * ConversationSource.from(request.getSource()) : ConversationSource.TEST;
             */

            log.info("Step 3.1: Initializing conversation for flow {}", flowId);
            long stepStartTime = System.currentTimeMillis();

            RobotConversationMsgResDTO initResponse = aiRobotConversationService.sendCustomUserSentence(
                    request.getUserId(), null, initText, sessionId, ip,
                    request.getBotId(), new StringBuilder(), null, null,
                    false, conversationSource);

            long stepResponseTime = System.currentTimeMillis() - stepStartTime;

            // Extract initial AI response text for saving to conversation history
            String initialAIResponseText = extractResponseText(initResponse);
            // Extract conversation id if available
            conversationId = extractConversationId(initResponse);

            // Add initial interaction
            interactions.add(TestInteractionDTO.builder()
                    .stepNumber(1)
                    .userInput(initText)
                    .targetIntent("init")
                    .robotResponse(initialAIResponseText)
                    .robotStatus(extractResponseStatus(initResponse))
                    .timestamp(System.currentTimeMillis())
                    .tokensUsed(0)
                    .responseTimeMs(stepResponseTime)
                    .build());

            // Add to conversation history
            conversationHistory.add(ConversationMessageDTO.builder()
                    .role("user")
                    .content(initText)
                    .timestamp(System.currentTimeMillis())
                    .build());
            conversationHistory.add(ConversationMessageDTO.builder()
                    .role("assistant")
                    .content(initialAIResponseText)
                    .timestamp(System.currentTimeMillis())
                    .build());

            finalStatus = extractResponseStatus(initResponse);

            // Execute conversation through each workflow sequentially
            int maxInteractions = 50;
            int currentStep = 2;
            int flowStepIndex = 0;
            int currentStepLoopCount = 0; // Track how many times we've looped on current step

            // Keep track of the last robot response for language extraction
            RobotConversationMsgResDTO lastRobotResponse = initResponse;

            while (!isEndStatus(finalStatus) && currentStep <= maxInteractions
                    && flowStepIndex < flow.getSteps().size()) {
                ConversationStep currentFlowStep = flow.getSteps().get(flowStepIndex);

                log.info("Step 3.3: Generating user response for intent: {} (step {}/{})",
                        currentFlowStep.getIntentName(), currentStep, maxInteractions);

                // Check if the next intent is "silence"
                if ("silence".equalsIgnoreCase(currentFlowStep.getIntentName())) {
                    log.info("Step 3.3: Detected silence intent, sending 'uhm...' to LLM service");

                    // For silence intent, send "uhm..." to LLM service and store "..." in
                    // conversation history
                    String userInput = "uhm...";
                    String aiResponseText = "...";

                    // Save silence input to conversation history as USER
                    try {
                        aiRobotConversationService.saveUserInputForTest(sessionId, userInput, "en");
                        log.info("Saved silence user input to conversation history: {}", userInput);
                    } catch (Exception e) {
                        log.warn("Failed to save silence user input to conversation history: {}", e.getMessage());
                    }

                    // Send "uhm..." to LLM service
                    stepStartTime = System.currentTimeMillis();
                    RobotConversationMsgResDTO robotResponse = aiRobotConversationService.sendCustomUserSentence(
                            request.getUserId(), null, userInput, sessionId, ip,
                            request.getBotId(), new StringBuilder(), null, null,
                            false, conversationSource);

                    // Update last robot response for next iteration's language extraction
                    lastRobotResponse = robotResponse;

                    stepResponseTime = System.currentTimeMillis() - stepStartTime;
                    finalStatus = extractResponseStatus(robotResponse);

                    // Record interaction with silence handling
                    interactions.add(TestInteractionDTO.builder()
                            .stepNumber(currentStep)
                            .userInput(userInput)
                            .targetIntent(currentFlowStep.getIntentName())
                            .robotResponse(aiResponseText)
                            .robotStatus(finalStatus)
                            .timestamp(System.currentTimeMillis())
                            .tokensUsed(0) // No tokens used for silence
                            .responseTimeMs(stepResponseTime)
                            .build());

                    // Update conversation history with silence
                    conversationHistory.add(ConversationMessageDTO.builder()
                            .role("user")
                            .content(userInput)
                            .timestamp(System.currentTimeMillis())
                            .build());
                    conversationHistory.add(ConversationMessageDTO.builder()
                            .role("assistant")
                            .content(aiResponseText)
                            .timestamp(System.currentTimeMillis())
                            .build());

                    log.info("Step 3.2: Silence intent handled, checking response status: {}", finalStatus);

                    // Check status - if END or ERROR, stop the loop
                    if (isEndStatus(finalStatus)) {
                        log.info("Conversation ended with status: {}", finalStatus);
                        break;
                    }

                    currentStep++;
                    flowStepIndex++;

                    // If we've gone through all flow steps, loop back to the beginning
                    if (flowStepIndex >= flow.getSteps().size()) {
                        flowStepIndex = 0;
                    }

                    // Small delay to avoid overwhelming the API
                    Thread.sleep(100);
                    continue;
                }

                // Step 3.3: Generate user response using OpenAI for non-silence intents
                stepStartTime = System.currentTimeMillis();

                // Get language from LLM response, fallback to request language, then default to
                // null (which will be "en" in OpenAI service)
                String languageToUse = "en";
                if (lastRobotResponse.getLanguage() != null) {
                    languageToUse = lastRobotResponse.getLanguage();
                }

                GenerateUserResponseReqDTO generateRequest = GenerateUserResponseReqDTO.builder()
                        .conversationHistory(conversationHistory)
                        .targetIntent(currentFlowStep.getIntentName())
                        .intentDescription(currentFlowStep.getIntentDescription())
                        .language(languageToUse)
                        .build();

                GenerateUserResponseResDTO userResponse = openAIService.generateUserResponse(generateRequest);
                String userInput = userResponse.getGeneratedResponse();

                // Save AI-generated user input to conversation history as USER
                if (userInput != null && !userInput.trim().isEmpty()) {
                    try {
                        aiRobotConversationService.saveUserInputForTest(sessionId, userInput, languageToUse);
                        log.info("Saved AI-generated user input to conversation history: {}", userInput);
                    } catch (Exception e) {
                        log.warn("Failed to save AI-generated user input to conversation history: {}", e.getMessage());
                    }
                }

                // Step 3.4: Send user response to LLM service
                log.info("Step 3.4: Sending user response to LLM: {}", userInput);

                RobotConversationMsgResDTO robotResponse = aiRobotConversationService.sendCustomUserSentence(
                        request.getUserId(), null, userInput, sessionId, ip,
                        request.getBotId(), new StringBuilder(), null, null,
                        false, conversationSource);

                // Update last robot response for next iteration's language extraction
                lastRobotResponse = robotResponse;

                stepResponseTime = System.currentTimeMillis() - stepStartTime;
                finalStatus = extractResponseStatus(robotResponse);
                if (conversationId == null) {
                    conversationId = extractConversationId(robotResponse);
                }

                // Extract AI response text for saving to conversation history
                String aiResponseText = extractResponseText(robotResponse);

                // Validate image if exists in messages
                validateImages(robotResponse);

                // Record interaction
                interactions.add(TestInteractionDTO.builder()
                        .stepNumber(currentStep)
                        .userInput(userInput)
                        .targetIntent(currentFlowStep.getIntentName())
                        .robotResponse(aiResponseText)
                        .robotStatus(finalStatus)
                        .timestamp(System.currentTimeMillis())
                        .tokensUsed(userResponse.getTokensUsed())
                        .responseTimeMs(stepResponseTime)
                        .build());

                // Update conversation history
                conversationHistory.add(ConversationMessageDTO.builder()
                        .role("user")
                        .content(userInput)
                        .timestamp(System.currentTimeMillis())
                        .build());
                conversationHistory.add(ConversationMessageDTO.builder()
                        .role("assistant")
                        .content(aiResponseText)
                        .timestamp(System.currentTimeMillis())
                        .build());

                log.info("Step 3.2: Checking response status: {}", finalStatus);

                // Step 3.2: Check status - if END or ERROR, stop the loop
                if (isEndStatus(finalStatus)) {
                    log.info("Conversation ended with status: {}", finalStatus);
                    break;
                }

                currentStep++;
                currentStepLoopCount++;
                
                // Check if we need to stay on current step for more loops
                ConversationStep stepForLoopCheck = flow.getSteps().get(flowStepIndex);
                if (currentStepLoopCount < stepForLoopCheck.getLoopCount()) {
                    // Continue looping on current step
                    log.info("Step loop {}/{} for intent: {}", 
                            currentStepLoopCount + 1, 
                            stepForLoopCheck.getLoopCount(), 
                            stepForLoopCheck.getIntentName());
                } else {
                    // Move to next step and reset loop count
                    flowStepIndex++;
                    currentStepLoopCount = 0;
                    
                    // If we've gone through all flow steps, loop back to the beginning
                    if (flowStepIndex >= flow.getSteps().size()) {
                        flowStepIndex = 0;
                    }
                    
                    log.info("Moving to next step. Flow step index: {}/{}", 
                            flowStepIndex + 1, 
                            flow.getSteps().size());
                }

                // Small delay to avoid overwhelming the API
                Thread.sleep(100);
            }

            // Determine if completed successfully
            completedSuccessfully = "END".equalsIgnoreCase(finalStatus) ||
                    (!isErrorStatus(finalStatus) && flowStepIndex >= flow.getSteps().size());

        } catch (Exception e) {
            log.error("Error executing conversation flow {}: {}", flowId, e.getMessage(), e);
            errorMessage = e.getMessage();
            finalStatus = "ERROR";
            completedSuccessfully = false;
        }

        long flowExecutionTime = (System.currentTimeMillis() - flowStartTime) / 1000;

        return TestFlowResultDTO.builder()
                .flowId(flowId)
                .flowName("Flow " + flowId + " - " + flow.getSteps().size() + " steps")
                .conversationId(conversationId)
                .sessionId(sessionId)
                .totalInteractions(interactions.size())
                .completedSuccessfully(completedSuccessfully)
                .finalStatus(finalStatus)
                .errorMessage(errorMessage)
                .interactions(interactions)
                .conversationHistory(conversationHistory)
                .executionTimeSeconds(flowExecutionTime)
                .build();
    }

    /**
     * Extract response text from robot response
     */
    private String extractResponseText(RobotConversationMsgResDTO response) {
        if (response == null || response.getMessages() == null || response.getMessages().isEmpty()) {
            return "";
        }

        return response.getMessages().stream()
                .map(AIRobotConversationResDTO::getText)
                .filter(text -> text != null && !text.trim().isEmpty())
                .collect(Collectors.joining(" "));
    }

    /**
     * Validate image URLs in response messages. If an image URL exists and not
     * gif/jpg, send error webhook.
     */
    private void validateImages(RobotConversationMsgResDTO response) {
        try {
            if (response == null || response.getMessages() == null)
                return;
            for (AIRobotConversationResDTO msg : response.getMessages()) {
                if (msg == null || msg.getMedia() == null)
                    continue;
                String url = msg.getMedia().getUrl();
                if (url == null || url.trim().isEmpty())
                    continue;
                String lower = url.toLowerCase();
                boolean ok = lower.endsWith(".gif") || lower.endsWith(".jpg") || lower.endsWith(".jpeg");
                if (!ok) {
                    alertWebhookService.sendError("Ảnh không đúng định dạng (yêu cầu gif/jpg): " + url);
                }
            }
        } catch (Exception e) {
            log.warn("Lỗi khi validate image trong response: {}", e.getMessage());
        }
    }

    /**
     * Extract status from robot response
     */
    private String extractResponseStatus(RobotConversationMsgResDTO response) {
        if (response == null || response.getMessages() == null || response.getMessages().isEmpty()) {
            return "UNKNOWN";
        }

        return response.getMessages().stream()
                .map(AIRobotConversationResDTO::getStatus)
                .filter(status -> status != null && !status.trim().isEmpty())
                .findFirst()
                .orElse("CONTINUE");
    }

    /**
     * Extract conversation id from robot response
     */
    private Long extractConversationId(RobotConversationMsgResDTO response) {
        if (response == null || response.getMessages() == null || response.getMessages().isEmpty()) {
            return null;
        }
        return response.getMessages().stream()
                .map(AIRobotConversationResDTO::getConversationId)
                .filter(id -> id != null)
                .findFirst()
                .orElse(null);
    }

    /**
     * Check if status indicates end of conversation
     */
    private boolean isEndStatus(String status) {
        if (status == null)
            return false;
        return "END".equalsIgnoreCase(status) || "ERROR".equalsIgnoreCase(status) ||
                "FINISHED".equalsIgnoreCase(status) || "COMPLETED".equalsIgnoreCase(status);
    }

    /**
     * Check if status indicates error
     */
    private boolean isErrorStatus(String status) {
        if (status == null)
            return false;
        return "ERROR".equalsIgnoreCase(status) || "FAILED".equalsIgnoreCase(status);
    }

    /**
     * Build final response DTO
     */
    private TestLessonResDTO buildTestLessonResponse(Long botId, WorkflowAnalysis analysis,
            List<ConversationFlow> flows) {
        // Convert internal models to DTOs
        List<com.stepup.springrobot.dto.chat.ConversationFlowDTO> conversationDTOs = flows.stream()
                .map(flow -> com.stepup.springrobot.dto.chat.ConversationFlowDTO.builder()
                        .conversationId(flow.getConversationId())
                        .steps(flow.getSteps().stream()
                                .map(step -> com.stepup.springrobot.dto.chat.ConversationStepDTO.builder()
                                        .questionIndex(step.getQuestionIndex())
                                        .questionText(step.getQuestionText())
                                        .intentName(step.getIntentName())
                                        .intentDescription(step.getIntentDescription())
                                        .expectedResponse(step.getExpectedResponse())
                                        .build())
                                .collect(Collectors.toList()))
                        .totalSteps(flow.getTotalSteps())
                        .build())
                .collect(Collectors.toList());

        // Calculate total steps across all flows considering loop counts
        int totalStepsAcrossAllFlows = flows.stream()
                .mapToInt(ConversationFlow::getTotalSteps)
                .sum();
        
        com.stepup.springrobot.dto.chat.TestSummaryDTO summary = com.stepup.springrobot.dto.chat.TestSummaryDTO
                .builder()
                .totalQuestions(analysis.getQuestions().size())
                .totalIntents(analysis.getAllIntents().size())
                .maxIntentsPerQuestion(analysis.getMaxIntentsPerQuestion())
                .estimatedDurationMinutes((totalStepsAcrossAllFlows / 10) + 5) // More realistic estimate based on total steps
                .build();

        return TestLessonResDTO.builder()
                .botId(botId)
                .totalConversations(flows.size())
                .conversations(conversationDTOs)
                .summary(summary)
                .build();
    }

    // Internal data classes
    @lombok.Data
    @lombok.Builder
    private static class WorkflowAnalysis {
        private List<QuestionInfo> questions;
        private Set<String> allIntents;
        private int maxIntentsPerQuestion;
        private List<WorkflowInfo> workflows; // Maintain workflow order for random test
    }
    
    @lombok.Data
    @lombok.Builder
    private static class WorkflowInfo {
        private Long workflowId;
        private int order; // 1, 2, 3 for workflow execution order
        private List<QuestionInfo> questions; // Questions in this specific workflow
    }

    @lombok.Data
    @lombok.Builder
    private static class QuestionInfo {
        private int index;
        private String questionText;
        private List<String> intents;
        private List<String> intentDescription;
        private List<Integer> intentLoopCounts; // Loop count cho từng intent tương ứng
        private List<Long> intentWorkflowIds; // Workflow ID cho từng intent tương ứng
    }

    @lombok.Data
    @lombok.Builder
    private static class ConversationFlow {
        private int conversationId;
        private List<ConversationStep> steps;
        private int totalSteps;
    }

    @lombok.Data
    @lombok.Builder
    private static class ConversationStep {
        private int questionIndex;
        private String questionText;
        private String intentName;
        private String intentDescription;
        private String expectedResponse;
        private int loopCount; // Số lần phải trả lời với intent này (default 1)
        private Long workflowId; // Workflow ID mà intent này thuộc về
    }
    
    @lombok.Data
    @lombok.Builder
    private static class QuestionIntentInfo {
        private String intentName;
        private String description;
        private Integer loopCount;
        private Long workflowId;
    }
    
    /**
     * Execute single random test with randomly selected intents
     */
    public SingleRandomTestResDTO executeSingleRandomTest(SingleRandomTestReqDTO request) {
        log.info("Starting single random test execution for lesson bot ID: {}", request.getBotId());
        long startTime = System.currentTimeMillis();
        
        try {
            // Step 1: Get workflow bot IDs
            log.info("Step 1: Calling lesson API to find workflow bot IDs for lesson bot ID: {}", request.getBotId());
            List<Long> workflowBotIds = fetchWorkflowBotIdsFromLesson(request.getBotId());
            if (workflowBotIds == null || workflowBotIds.isEmpty()) {
                throw new RuntimeException("No workflows found for lesson bot ID: " + request.getBotId());
            }
            
            log.info("Step 1 completed: Found {} workflow bot IDs for lesson bot ID: {}", workflowBotIds.size(), request.getBotId());
            
            // Step 2: Fetch and aggregate workflow data
            log.info("Step 2: Fetching and aggregating workflow data from {} workflow bot IDs", workflowBotIds.size());
            WorkflowAnalysis analysis = fetchAndAggregateWorkflowData(workflowBotIds);
            if (analysis == null || analysis.getAllIntents().isEmpty()) {
                throw new RuntimeException("Invalid aggregated workflow data for workflow bot IDs: " + workflowBotIds);
            }
            
            log.info("Step 2 completed: Successfully aggregated workflow data with {} total intents", analysis.getAllIntents().size());
            
            // Step 3: Create single conversation flow with random intent per question
            ConversationFlow randomFlow = createRandomConversationFlow(analysis);
            log.info("Step 3: Created random conversation flow with {} steps covering all questions", randomFlow.getSteps().size());
            
            // Step 4: Execute the random conversation flow with enhanced tracking
            TestFlowResultDTO testResult = executeMultipleWorkflowsFlowWithTracking(
                FullTestLessonReqDTO.builder()
                    .botId(request.getBotId())
                    .userId(request.getUserId())
                    .build(),
                workflowBotIds,
                randomFlow,
                1
            );
            
            long executionTime = (System.currentTimeMillis() - startTime) / 1000;
            
            // Extract random intents from conversation flow
            List<String> randomIntentsSelected = randomFlow.getSteps().stream()
                    .map(ConversationStep::getIntentName)
                    .collect(Collectors.toList());
            
            // Extract random flow details for proper conversation detail generation
            List<SingleRandomTestResDTO.RandomIntentDetailDTO> randomFlowDetails = randomFlow.getSteps().stream()
                    .map(step -> SingleRandomTestResDTO.RandomIntentDetailDTO.builder()
                            .intentName(step.getIntentName())
                            .loopCount(step.getLoopCount())
                            .workflowId(step.getWorkflowId())
                            .build())
                    .collect(Collectors.toList());
            
            return SingleRandomTestResDTO.builder()
                    .botId(request.getBotId())
                    .conversationId(testResult.getConversationId())
                    .sessionId(testResult.getSessionId())
                    .totalInteractions(testResult.getTotalInteractions())
                    .completedSuccessfully(testResult.getCompletedSuccessfully())
                    .finalStatus(testResult.getFinalStatus())
                    .errorMessage(testResult.getErrorMessage())
                    .executionTimeSeconds(executionTime)
                    .workflowDetails(testResult.getWorkflowDetails())
                    .randomIntentsSelected(randomIntentsSelected)
                    .randomFlowDetails(randomFlowDetails)
                    .interactions(testResult.getInteractions())
                    .build();
                    
        } catch (Exception e) {
            log.error("Error executing single random test for bot ID: {}", request.getBotId(), e);
            long executionTime = (System.currentTimeMillis() - startTime) / 1000;
            
            return SingleRandomTestResDTO.builder()
                    .botId(request.getBotId())
                    .completedSuccessfully(false)
                    .finalStatus("ERROR")
                    .errorMessage(e.getMessage())
                    .executionTimeSeconds(executionTime)
                    .randomIntentsSelected(new ArrayList<>())
                    .randomFlowDetails(new ArrayList<>())
                    .interactions(new ArrayList<>())
                    .build();
        }
    }
    
    /**
     * Create conversation flow with random intent per question following workflow order
     */
    private ConversationFlow createRandomConversationFlow(WorkflowAnalysis analysis) {
        List<ConversationStep> steps = new ArrayList<>();
        Random random = new Random();
        
        // Đi theo thứ tự workflow -> question -> random intent
        for (WorkflowInfo workflowInfo : analysis.getWorkflows()) {
            log.info("Processing workflow {} (order {}) with {} questions", 
                    workflowInfo.getWorkflowId(), workflowInfo.getOrder(), workflowInfo.getQuestions().size());
            
            // Đi qua từng câu hỏi trong workflow này
            for (QuestionInfo question : workflowInfo.getQuestions()) {
                if (question.getIntents() == null || question.getIntents().isEmpty()) {
                    continue; // Skip questions without intents
                }
                
                // Random chọn 1 intent từ câu hỏi này
                int randomIntentIndex = random.nextInt(question.getIntents().size());
                String selectedIntent = question.getIntents().get(randomIntentIndex);
                String intentDescription = question.getIntentDescription().get(randomIntentIndex);
                int loopCount = question.getIntentLoopCounts().get(randomIntentIndex);
                Long workflowId = question.getIntentWorkflowIds().get(randomIntentIndex);
                
                log.info("Selected random intent '{}' for question {} in workflow {}", 
                        selectedIntent, question.getIndex(), workflowId);
                
                // Tạo conversation step với intent được random chọn
                steps.add(ConversationStep.builder()
                        .questionIndex(question.getIndex())
                        .questionText(question.getQuestionText())
                        .intentName(selectedIntent)
                        .intentDescription(intentDescription)
                        .expectedResponse("Expected response for " + selectedIntent)
                        .loopCount(loopCount)
                        .workflowId(workflowId)
                        .build());
            }
        }
        
        // Calculate total steps considering loop count for each intent
        int totalStepsWithLoopCount = steps.stream()
                .mapToInt(ConversationStep::getLoopCount)
                .sum();
        
        log.info("Created random conversation flow with {} steps ({} total steps with loop count) following workflow order", 
                steps.size(), totalStepsWithLoopCount);
        return ConversationFlow.builder()
                .conversationId(1)
                .steps(steps)
                .totalSteps(totalStepsWithLoopCount)
                .build();
    }
    
    /**
     * Deduplicate intents within a question by intent name, keeping the maximum loop count
     * This handles cases where the same intent appears multiple times with different loop counts
     * 
     * @param intents List of intent names
     * @param intentDescriptions List of intent descriptions  
     * @param intentLoopCounts List of loop counts
     * @param intentWorkflowIds List of workflow IDs
     * @return QuestionIntentInfo map with deduplicated intents
     */
    private Map<String, QuestionIntentInfo> deduplicateIntents(List<String> intents, 
            List<String> intentDescriptions, 
            List<Integer> intentLoopCounts, 
            List<Long> intentWorkflowIds) {
        Map<String, QuestionIntentInfo> intentMap = new LinkedHashMap<>();
        
        for (int i = 0; i < intents.size(); i++) {
            String intentName = intents.get(i);
            String description = intentDescriptions.get(i);
            int loopCount = intentLoopCounts.get(i);
            Long workflowId = intentWorkflowIds.get(i);
            
            QuestionIntentInfo existing = intentMap.get(intentName);
            if (existing == null || loopCount > existing.getLoopCount()) {
                intentMap.put(intentName, QuestionIntentInfo.builder()
                        .intentName(intentName)
                        .description(description)
                        .loopCount(loopCount)
                        .workflowId(workflowId)
                        .build());
                        
                if (existing != null) {
                    log.debug("Intent '{}' found with higher loop count {} (was {}), using higher value", 
                            intentName, loopCount, existing.getLoopCount());
                }
            }
        }
        
        return intentMap;
    }

    /**
     * Parse loop count from string, default to 1 if null or invalid
     */
    private int parseLoopCount(String loopCountStr) {
        if (loopCountStr == null || loopCountStr.trim().isEmpty()) {
            return 1; // Default loop count
        }
        
        try {
            int loopCount = Integer.parseInt(loopCountStr.trim());
            return Math.max(1, loopCount); // Ensure at least 1
        } catch (NumberFormatException e) {
            log.warn("Invalid loop count value: '{}', using default value 1", loopCountStr);
            return 1;
        }
    }
    
    /**
     * Initialize execution state for tracking current question/workflow and intent counts
     */
    private TestExecutionStateDTO initializeExecutionState(ConversationFlow flow, List<Long> workflowBotIds) {
        TestExecutionStateDTO state = TestExecutionStateDTO.builder()
                .currentWorkflowIndex(0)
                .currentQuestionIndex(0)
                .currentStepIndex(0)
                .intentExecutionCounts(new HashMap<>())
                .executionHistory(new ArrayList<>())
                .currentIntentExecutedCount(0)
                .isWorkflowCompleted(false)
                .isQuestionCompleted(false)
                .shouldMoveToNextQuestion(false)
                .shouldMoveToNextWorkflow(false)
                .build();
                
        // Set initial values if flow has steps
        if (flow.getSteps() != null && !flow.getSteps().isEmpty()) {
            ConversationStep firstStep = flow.getSteps().get(0);
            state.setCurrentIntentName(firstStep.getIntentName());
            state.setCurrentIntentLoopCount(firstStep.getLoopCount());
            state.setCurrentWorkflowId(firstStep.getWorkflowId());
            state.setCurrentQuestionText(firstStep.getQuestionText());
        }
        
        return state;
    }
    
    /**
     * Update execution state after an intent execution
     */
    private void updateExecutionState(TestExecutionStateDTO state, ConversationStep executedStep) {
        // Update intent execution count
        String intentName = executedStep.getIntentName();
        state.getIntentExecutionCounts().merge(intentName, 1, Integer::sum);
        state.getExecutionHistory().add(intentName);
        
        // Update current intent executed count
        state.setCurrentIntentExecutedCount(
            state.getIntentExecutionCounts().getOrDefault(intentName, 0)
        );
        
        // Check if current intent has reached its loop count
        if (state.getCurrentIntentExecutedCount() >= executedStep.getLoopCount()) {
            state.setShouldMoveToNextQuestion(true);
            log.info("Intent '{}' completed {} loops (required: {}), moving to next question", 
                    intentName, state.getCurrentIntentExecutedCount(), executedStep.getLoopCount());
        }
    }
    
    /**
     * Advance to next question/workflow based on execution state
     * Currently unused but kept for future enhancements
     */
    @SuppressWarnings("unused")
    private ConversationStep advanceToNextStep(TestExecutionStateDTO state, ConversationFlow flow, List<Long> workflowBotIds) {
        if (state.getShouldMoveToNextQuestion()) {
            // Reset for next question
            state.setShouldMoveToNextQuestion(false);
            state.setCurrentIntentExecutedCount(0);
            
            // Move to next step in flow
            state.setCurrentStepIndex(state.getCurrentStepIndex() + 1);
            
            // Check if we've completed all steps in current workflow
            if (state.getCurrentStepIndex() >= flow.getSteps().size()) {
                // Check if we need to move to next workflow or restart
                if (state.getCurrentWorkflowIndex() < workflowBotIds.size() - 1) {
                    state.setShouldMoveToNextWorkflow(true);
                    state.setCurrentWorkflowIndex(state.getCurrentWorkflowIndex() + 1);
                    state.setCurrentStepIndex(0); // Reset to first step of new workflow
                } else {
                    // All workflows completed, restart from beginning
                    state.setCurrentWorkflowIndex(0);
                    state.setCurrentStepIndex(0);
                }
            }
            
            // Get next step if available
            if (state.getCurrentStepIndex() < flow.getSteps().size()) {
                ConversationStep nextStep = flow.getSteps().get(state.getCurrentStepIndex());
                state.setCurrentIntentName(nextStep.getIntentName());
                state.setCurrentIntentLoopCount(nextStep.getLoopCount());
                state.setCurrentWorkflowId(nextStep.getWorkflowId());
                state.setCurrentQuestionText(nextStep.getQuestionText());
                return nextStep;
            }
        }
        
        // Return current step if no advancement needed
        if (state.getCurrentStepIndex() < flow.getSteps().size()) {
            return flow.getSteps().get(state.getCurrentStepIndex());
        }
        
        return null; // No more steps
    }
    
    /**
     * Create execution history record for a workflow
     * Currently unused but kept for future enhancements
     */
    @SuppressWarnings("unused")
    private List<WorkflowDetailDTO.IntentExecutionRecord> createExecutionHistory(
            TestExecutionStateDTO state, Long workflowId) {
        List<WorkflowDetailDTO.IntentExecutionRecord> history = new ArrayList<>();
        Map<String, Integer> intentCounts = new HashMap<>();
        
        // Count occurrences of each intent in execution history
        for (String intent : state.getExecutionHistory()) {
            intentCounts.merge(intent, 1, Integer::sum);
        }
        
        // Create execution records
        for (Map.Entry<String, Integer> entry : intentCounts.entrySet()) {
            history.add(WorkflowDetailDTO.IntentExecutionRecord.builder()
                    .intentName(entry.getKey())
                    .executionCount(entry.getValue())
                    .expectedLoopCount(1) // Will be updated with actual expected count
                    .build());
        }
        
        return history;
    }
}

