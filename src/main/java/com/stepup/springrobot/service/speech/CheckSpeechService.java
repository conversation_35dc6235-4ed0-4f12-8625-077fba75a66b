package com.stepup.springrobot.service.speech;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.config.ConfigUtil;
import com.stepup.springrobot.config.HttpUtils;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.auth.AccountInfoDTO;
import com.stepup.springrobot.dto.speech.AsrQcUpdateRequestDTO;
import com.stepup.springrobot.dto.speech.SpeechBotResponseDTO;
import com.stepup.springrobot.dto.speech.WakeWordDetectionResponseDTO;
import com.stepup.springrobot.exception.business.audio.AudioErrorException;
import com.stepup.springrobot.exception.business.audio.AudioTooLongException;
import com.stepup.springrobot.model.speech.UserLimitCheckType;
import com.stepup.springrobot.model.speech.WakeWordDetection;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.CommonService;
import com.stepup.springrobot.service.SharedService;
import com.stepup.springrobot.service.SlackWarningSystemService;
import com.stepup.springrobot.service.UploadFileToS3;
import io.sentry.Sentry;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;

@Slf4j
@Service
public class CheckSpeechService extends CommonService {
    @Value("${api_speech_hostname}")
    private String apiSpeechHostname;

    @Value("${wake_word_detection_host}")
    private String wakeWordDetectionHost;

    @Value("${wake_word_detection_uri}")
    private String wakeWordDetectionUri;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SlackWarningSystemService slackWarningSystemService;

    @Autowired
    private RedisRateLimiter redisRateLimiter;

    @Autowired
    private SharedService sharedService;

    @Autowired
    private com.stepup.springrobot.repository.chat.RobotUserConversationRecordHistoryRepository robotUserConversationRecordHistoryRepository;

    @Autowired
    private com.stepup.springrobot.repository.speech.WakeWordDetectionRepository wakeWordDetectionRepository;

    protected CheckSpeechService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public String uploadSpeech(HttpServletRequest request, MultipartFile multipartFile, String textRefs) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        boolean isAllowed = redisRateLimiter.checkRateLimit(userId, true, UserLimitCheckType.SPEECH);
        if (!isAllowed) {
            redisRateLimiter.renderTooManyRequestResponseData(userId, true, UserLimitCheckType.SPEECH);
        }

        File objectFile = convertMultiPartFileToFile(multipartFile, userId, true);
        //tmp
        if (objectFile == null) {
            throw new AudioErrorException();
        }
        return encrypt(handleUploadSpeech(userId, objectFile, textRefs));
    }

    public DataResponseDTO<?> updateAsrQc(AsrQcUpdateRequestDTO req) {
        try {
            if (req.getMessageId() == null) {
                return new DataResponseDTO<>(CodeDefine.ERR_COMMON, "messageId is required", null);
            }

            var recordOpt = robotUserConversationRecordHistoryRepository.findById(req.getMessageId());
            if (recordOpt.isEmpty()) {
                return new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Message not found", null);
            }

            var record = recordOpt.get();
            // Update fields only if provided (allow partial updates)
            if (req.getIsAsrCorrect() != null) {
                record.setIsAsrCorrect(req.getIsAsrCorrect());
            }
            if (req.getAsrCorrectedContent() != null) {
                record.setAsrCorrectedContent(req.getAsrCorrectedContent());
            }
            if (req.getIsIntentAffected() != null) {
                record.setIsIntentAffected(req.getIsIntentAffected());
            }
            if (req.getIntentCorrectedContent() != null) {
                record.setIntentCorrectedContent(req.getIntentCorrectedContent());
            }

            robotUserConversationRecordHistoryRepository.save(record);
            return new DataResponseDTO<>(CodeDefine.OK, CodeDefine.SUCCESS_MESSAGE, record);
        } catch (Exception e) {
            Sentry.captureException(e);
            return new DataResponseDTO<>(CodeDefine.SERVER_ERROR, e.getMessage(), null);
        }
    }

    public DataResponseDTO<?> handleUploadSpeech(String userId, File objectFile, String textRefs) {
        LocalDateTime startTimeReq = null;
        Object objectMono = null;
        SpeechBotResponseDTO speechBotResponseDTO = null;
        try {
            // run async
            redisRateLimiter.saveCountRequestCheck(UserLimitCheckType.SPEECH);

            startTimeReq = LocalDateTime.now();
            objectMono = sharedService.callCheckSpeechSingleByOkHttp(objectFile, textRefs);
            if (objectMono == null) {
                throw new Exception(CodeDefine.MSG_CALL_OK_HTTP_NULL);
            }

            speechBotResponseDTO = objectMapper.convertValue(objectMono, new TypeReference<>() {
            });
            if (!speechBotResponseDTO.isSuccess()) {
                throw new Exception(CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE);
            }

            deleteFile(objectFile);// delete file after upload
            return new DataResponseDTO<>(CodeDefine.OK, CodeDefine.SUCCESS_MESSAGE, objectMono);
        } catch (Exception e) {
            String msgSlack = "(Check phát âm) Start time: " + startTimeReq + " ---End time: " + LocalDateTime.now() +
                    " ---Hostname API check speech " + apiSpeechHostname +
                    " ---user_id: " + userId +
                    " ---Text textRefs " + "---" + textRefs +
                    " ---Bot response " + "---" + objectMono +
                    " ---File name " + "---" + objectFile.getName() +
                    "---" + e.getMessage();

            if (!CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE.equals(e.getMessage()) || isNotShortAudioStatusCode(speechBotResponseDTO)) {
                slackWarningSystemService.sendWarningSystemToSlack(msgSlack, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            }

            if (CodeDefine.MSG_CALL_OK_HTTP_NULL.equals(e.getMessage())) {
                // delete file after upload if error, temp comment out
                deleteFile(objectFile);
                return new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Kết nối đang không ổn định, bạn thử lại lần nữa nhé!", null);
            } else if (CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE.equals(e.getMessage()) && speechBotResponseDTO != null) {
                // delete file after upload if error, temp comment out để check file
                deleteFile(objectFile);
                renderBotErrorResponse(speechBotResponseDTO);
            }

            try {
                objectMono = sharedService.callCheckSpeechSingleByOkHttp(objectFile, textRefs);
                String responseStr = objectMapper.writeValueAsString(objectMono);
                log.info("Hostname API backup check speech {}  Single check speech ---{}---{}", apiSpeechHostname, userId, responseStr.substring(0, Math.min(30, responseStr.length())));
                speechBotResponseDTO = objectMapper.convertValue(objectMono, new TypeReference<>() {
                });
                if (!speechBotResponseDTO.isSuccess()) {
                    throw new Exception(CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE);
                }

                // delete file after upload if error, temp comment out
                deleteFile(objectFile);
                return new DataResponseDTO<>(CodeDefine.OK, CodeDefine.SUCCESS_MESSAGE, objectMono);
            } catch (Exception exp2) {
                log.error("====Checking speech at server backup downtime==== {}", exp2.getMessage());

                msgSlack = "(Check phát âm) Start time: " + LocalDateTime.now() + " ---End time: " + LocalDateTime.now() +
                        " ---Hostname API backup check speech " + apiSpeechHostname +
                        " ---user_id: " + userId +
                        " ---Text textRefs " + "---" + textRefs +
                        " ---Bot response " + "---" + objectMono +
                        " ---File name " + "---" + objectFile.getName() +
                        "---" + exp2.getMessage();
                if (!CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE.equals(e.getMessage()) || isNotShortAudioStatusCode(speechBotResponseDTO)) {
                    slackWarningSystemService.sendWarningSystemToSlack(msgSlack, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                }

                if (CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE.equals(e.getMessage()) && speechBotResponseDTO != null) {
                    // delete file after upload if error
                    deleteFile(objectFile);
                    renderBotErrorResponse(speechBotResponseDTO);
                }

                Sentry.captureException(exp2);
            }

            // delete file after upload if error, temp comment out
            deleteFile(objectFile);
            Sentry.captureException(e);
            return new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Kết nối đang không ổn định, bạn thử lại lần nữa nhé!");
        }
    }

    public DataResponseDTO<?> detectWakeWordFromBytes(byte[] audioData) {
        long startTime = System.currentTimeMillis();
        try {
            String url = wakeWordDetectionHost + wakeWordDetectionUri;
            
            long apiStartTime = System.currentTimeMillis();
            HttpResponse<String> response = Unirest.post(url)
                    .header("Content-Type", "application/octet-stream")
                    .body(audioData)
                    .asString();
            long apiEndTime = System.currentTimeMillis();
            long apiResponseTime = apiEndTime - apiStartTime;

            log.info("Wake word detection API response time: {}ms", apiResponseTime);

            if (response.getStatus() != 200) {
                long totalTime = System.currentTimeMillis() - startTime;
                log.warn("Wake word detection failed with status {} in {}ms", response.getStatus(), totalTime);
                return new DataResponseDTO<>(CodeDefine.ERR_COMMON,
                        "Wake word detection failed: " + response.getStatusText());
            }

            JsonNode fullResponse = objectMapper.readTree(response.getBody());

            JsonNode dataNode = fullResponse.has("data") ? fullResponse.get("data") : fullResponse;

            boolean detected = dataNode.has("detected") && dataNode.get("detected").asBoolean();
            String audioUrl = dataNode.has("audio_url") && !dataNode.get("audio_url").isNull() ? dataNode.get("audio_url").asText() : null;
            Double maxScore = dataNode.has("max_score") && !dataNode.get("max_score").isNull() ? dataNode.get("max_score").asDouble() : null;
            Double threshold = dataNode.has("threshold") && !dataNode.get("threshold").isNull() ? dataNode.get("threshold").asDouble() : null;

            WakeWordDetectionResponseDTO responseDTO = WakeWordDetectionResponseDTO.builder()
                    .detected(detected)
                    .audioUrl(audioUrl)
                    .maxScore(maxScore)
                    .threshold(threshold)
                    .build();

            // persist
            WakeWordDetection entity = WakeWordDetection.builder()
                    .detected(detected)
                    .audioUrl(audioUrl)
                    .maxScore(maxScore)
                    .threshold(threshold)
                    .build();
            wakeWordDetectionRepository.save(entity);

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("Wake word detection completed successfully in {}ms (API: {}ms, detected: {})", 
                    totalTime, apiResponseTime, detected);

            return new DataResponseDTO<>(CodeDefine.OK, CodeDefine.SUCCESS_MESSAGE, responseDTO);
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("Wake word detection failed after {}ms: {}", totalTime, e.getMessage());
            Sentry.captureException(e);
            return new DataResponseDTO<>(CodeDefine.ERR_COMMON,
                    "Wake word service currently unavailable, please try again later.");
        }
    }

    private boolean isNotShortAudioStatusCode(SpeechBotResponseDTO speechBotResponseDTO) {
        return speechBotResponseDTO != null
                && !speechBotResponseDTO.getMsg().equals(CodeDefine.ERROR_MESSAGE_PROCESS)
                && !speechBotResponseDTO.getMsg().equals(CodeDefine.ERROR_MESSAGE_TOO_SHORT)
                && !speechBotResponseDTO.getMsg().equals(CodeDefine.ERROR_MESSAGE_SOMETHING_WRONG);
    }

    private void deleteFile(File file) {
        Path path = file.toPath();
        try {
            Files.delete(path);
        } catch (Exception e) {
            log.error("Error delete file {}", e.getMessage());
        }
    }

    private void renderBotErrorResponse(SpeechBotResponseDTO speechBotResponseDTO) {
        if (speechBotResponseDTO.getMsg().equals("Audio is too long!")) {
            throw new AudioTooLongException();
        } else {
            throw new AudioTooLongException();
        }
    }

    public String uploadSpeechOpen(HttpServletRequest request, MultipartFile multipartFile, String textRefs) throws IOException {
        return encrypt(handleUploadSpeechOpen(request, multipartFile, textRefs));
    }

    public DataResponseDTO<?> handleUploadSpeechOpen(HttpServletRequest request, MultipartFile multipartFile, String textRefs) throws JsonProcessingException {
        String ipAddress = HttpUtils.getClientIpAddress(request);

        boolean isAllowed = redisRateLimiter.checkRateLimitIP(ipAddress, UserLimitCheckType.SPEECH);
        if (!isAllowed) {
            redisRateLimiter.renderTooManyRequestResponseData(null, false, UserLimitCheckType.SPEECH);
        }

        File objectFile = convertMultiPartFileToFile(multipartFile, ipAddress, true);
        //tmp
        if (objectFile == null) {
            throw new AudioErrorException();
        }

        LocalDateTime startTimeReq = null;
        Object objectMono = null;
        SpeechBotResponseDTO speechBotResponseDTO = null;
        try {
            // run async
            redisRateLimiter.saveCountRequestCheck(UserLimitCheckType.SPEECH);

            startTimeReq = LocalDateTime.now();

            objectMono = sharedService.callCheckSpeechSingleByOkHttp(objectFile, textRefs);

            if (objectMono == null) {
                throw new Exception(CodeDefine.MSG_CALL_OK_HTTP_NULL);
            }

            speechBotResponseDTO = objectMapper.convertValue(objectMono, new TypeReference<>() {
            });
            if (!speechBotResponseDTO.isSuccess()) {
                throw new Exception(CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE);
            }

            deleteFile(objectFile);// delete file after upload
            return new DataResponseDTO<>(CodeDefine.OK, CodeDefine.SUCCESS_MESSAGE, objectMono);
        } catch (Exception e) {
            log.error("Single check speech downtime === {}", e.getMessage());
            String msgSlack = "(Check phát âm) Start time: " + startTimeReq + " ---End time: " + LocalDateTime.now() +
                    " ---Hostname API check speech " + apiSpeechHostname + " ---Ip: " + ipAddress +
                    " ---Text textRefs " + "---" + textRefs +
                    " ---Bot response " + "---" + objectMono +
                    " ---File name " + "---" + objectFile.getName() +
                    "---" + e.getMessage();
            if (!CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE.equals(e.getMessage()) || isNotShortAudioStatusCode(speechBotResponseDTO)) {
                slackWarningSystemService.sendWarningSystemToSlack(msgSlack, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
            }

            if (CodeDefine.MSG_CALL_OK_HTTP_NULL.equals(e.getMessage())) {
                // delete file after upload if error, temp comment out
                deleteFile(objectFile);
                return new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Bạn thao tác quá nhanh, bạn làm chậm thôi nha :D", null);
            } else if (CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE.equals(e.getMessage()) && speechBotResponseDTO != null) {
                // delete file after upload if error, temp comment out để check file
                deleteFile(objectFile);
                renderBotErrorResponse(speechBotResponseDTO);
            }

            try {
                startTimeReq = LocalDateTime.now();

                objectMono = sharedService.callCheckSpeechSingleByOkHttp(objectFile, textRefs);
                String responseStr = objectMapper.writeValueAsString(objectMono);
                log.info("Hostname API backup check speech {} ---Ip: {} Single check speech ---{}", apiSpeechHostname, ipAddress, responseStr.substring(0, Math.min(30, responseStr.length())));

                speechBotResponseDTO = objectMapper.convertValue(objectMono, new TypeReference<>() {
                });
                if (!speechBotResponseDTO.isSuccess()) {
                    throw new Exception(CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE);
                }

                // delete file after upload if error, temp comment out
                deleteFile(objectFile);
                return new DataResponseDTO<>(CodeDefine.OK, CodeDefine.SUCCESS_MESSAGE, objectMono);
            } catch (Exception exp2) {
                log.error("====Checking speech at server backup downtime==== {}", exp2.getMessage());
                msgSlack = "(Check phát âm) Start time: " + startTimeReq + " ---End time: " + LocalDateTime.now() + " ---Hostname API backup check speech " + apiSpeechHostname +
                        " ---Ip: " + ipAddress + "---" +
                        " ---" + textRefs +
                        " ---Bot response " + objectMono +
                        " ---File name " + "---" + objectFile.getName() +
                        " ---" + exp2.getMessage();
                if (!CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE.equals(e.getMessage()) || isNotShortAudioStatusCode(speechBotResponseDTO)) {
                    slackWarningSystemService.sendWarningSystemToSlack(msgSlack, ConfigUtil.INSTANCE.getPATH_WP_ALERT_SYSTEM_CRITICAL());
                }

                Sentry.captureException(exp2);
                if (CodeDefine.BOT_CHECK_SPEECH_ERROR_RESPONSE.equals(e.getMessage()) && speechBotResponseDTO != null) {
                    // delete file after upload if error, temp comment out để check file
                    deleteFile(objectFile);
                    renderBotErrorResponse(speechBotResponseDTO);
                }
            }

            // delete file after upload if error, temp comment out
            deleteFile(objectFile);
            Sentry.captureException(e);
            return new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Hệ thống check phát âm đang bảo trì, bạn vui lòng chờ xíu nha.");
        }
    }
}
