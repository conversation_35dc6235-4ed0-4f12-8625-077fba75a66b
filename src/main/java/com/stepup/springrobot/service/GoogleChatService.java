package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

@Log4j2
@Service
public class GoogleChatService {

    @Value("${google.chat.webhook.url}")
    private String webhookUrl;

    @Value("${google.chat.enabled:true}")
    private boolean enabled;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private OkHttpClient okHttpClient;

    /**
     * Send error notification to Google Chat
     */
    @Async("taskExecutor")
    public void sendErrorNotification(String errorMessage) {
        if (!enabled) {
            log.info("Google Chat notifications are disabled");
            return;
        }

        try {
            ObjectNode message = createErrorMessage(errorMessage);
            sendMessage(message);
            log.info("Error notification sent to Google Chat successfully");
        } catch (Exception e) {
            log.error("Failed to send error notification to Google Chat", e);
        }
    }

    /**
     * Send error notification with exception details
     */
    @Async("taskExecutor")
    public void sendErrorNotification(String errorMessage, Exception exception) {
        if (!enabled) {
            log.info("Google Chat notifications are disabled");
            return;
        }

        try {
            ObjectNode message = createErrorMessage(errorMessage, exception);
            sendMessage(message);
            log.info("Error notification sent to Google Chat successfully");
        } catch (Exception e) {
            log.error("Failed to send error notification to Google Chat", e);
        }
    }

    /**
     * Create simple error message
     */
    private ObjectNode createErrorMessage(String errorMessage) {
        ObjectNode message = JsonNodeFactory.instance.objectNode();
        String text = "🚨 **ERROR NOTIFICATION**\n\n**Message:** " + errorMessage +
                "\n**Timestamp:** " + DateTimeFormatter.ISO_INSTANT.format(Instant.now());
        message.put("text", text);
        return message;
    }

    /**
     * Create error message with exception details
     */
    private ObjectNode createErrorMessage(String errorMessage, Exception exception) {
        ObjectNode message = JsonNodeFactory.instance.objectNode();
        String text = "🚨 **ERROR NOTIFICATION**\n\n" +
                "**Message:** " + errorMessage + "\n" +
                "**Exception:** " + exception.getClass().getSimpleName() + "\n" +
                "**Details:** " + exception.getMessage() + "\n" +
                "**Timestamp:** " + DateTimeFormatter.ISO_INSTANT.format(Instant.now());
        message.put("text", text);
        return message;
    }

    /**
     * Send HTTP request to Google Chat webhook
     */
    private void sendMessage(JsonNode message) throws Exception {
        String jsonBody = objectMapper.writeValueAsString(message);

        RequestBody body = RequestBody.create(
                MediaType.parse("application/json"), jsonBody);

        Request request = new Request.Builder()
                .url(webhookUrl)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String responseBody = response.body() != null ? response.body().string() : "No response body";
                log.error("Google Chat webhook failed - Status: {}, Response: {}", response.code(), responseBody);
                throw new RuntimeException("Google Chat webhook failed with status: " + response.code());
            }
        }
    }
}
