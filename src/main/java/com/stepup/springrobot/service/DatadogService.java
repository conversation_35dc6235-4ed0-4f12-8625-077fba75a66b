package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.dto.monitoring.ExternalApiCallDTO;
import com.stepup.springrobot.dto.monitoring.MonitoringLogDTO;
import com.stepup.springrobot.dto.monitoring.TraceContextDTO;
import com.stepup.springrobot.dto.monitoring.WebSocketEventDTO;
import com.stepup.springrobot.model.DatadogLogType;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class DatadogService {
    @Value("${datadog_endpoint}")
    private String endpoint;

    @Value("${datadog_api_key}")
    private String apiKey;

    @Value("${datadog_application_key}")
    private String applicationKey;

    @Value("${datadog_host}")
    private String hostName;

    @Value("${datadog_service}")
    private String serviceName;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private com.stepup.springrobot.config.MonitoringConfiguration monitoringConfig;

    @Autowired
    private OkHttpClient okHttpClient;

    @Async("taskExecutor")
    public void handleMonitoringLog(MonitoringLogDTO monitoringLog) {
        monitoringLog.setServiceName(serviceName);
        monitoringLog.setEnvironment(serviceName);
        sendLogToDatadog(monitoringLog);
    }

    @Async("taskExecutor")
    public void sendLogToDatadog(MonitoringLogDTO monitoringLog) {
        if (!monitoringConfig.isEnabled() || !monitoringConfig.getDatadog().isEnabled()) {
            return;
        }

        try {

            // Create structured log data
            ObjectNode data = JsonNodeFactory.instance.objectNode();
            data.put("ddsource", "java");
            data.put("ddtags", "env:prod,service:" + serviceName);
            data.put("hostname", hostName);
            data.put("service", serviceName);
            data.put("message", monitoringLog.getMessage());
            data.put("level", monitoringLog.getLevel());
            data.put("timestamp", monitoringLog.getTimestamp().toString());

            if (monitoringLog.getLogType() != null) {
                data.put("log_type", monitoringLog.getLogType().getFeature());
            }

            if (monitoringLog.getConversationId() != null) {
                data.put("conversation_id", monitoringLog.getConversationId());
            }

            if (monitoringLog.getStatusCode() != null) {
                data.put("status_code", monitoringLog.getStatusCode());
            }

            if (monitoringLog.getUserId() != null) {
                data.put("user_id", monitoringLog.getUserId());
            }

            if (monitoringLog.getRobotId() != null) {
                data.put("robot_id", monitoringLog.getRobotId());
            }

            if (monitoringLog.getSocketSessionId() != null) {
                data.put("socket_session_id", monitoringLog.getSocketSessionId());
            }

            if (monitoringLog.getStatus() != null) {
                data.put("status", monitoringLog.getStatus());
            }

            if (monitoringLog.getDurationMs() != null) {
                data.put("duration_ms", monitoringLog.getDurationMs());
            }

            if (monitoringLog.getEnvironment() != null) {
                data.put("environment", monitoringLog.getEnvironment());
            }

            if (monitoringLog.getVersion() != null) {
                data.put("version", monitoringLog.getVersion());
            }

            // WebSocket specific fields
            if (monitoringLog.getClientIp() != null) {
                data.put("client_ip", monitoringLog.getClientIp());
            }

            if (monitoringLog.getUserAgent() != null) {
                data.put("user_agent", monitoringLog.getUserAgent());
            }

            if (monitoringLog.getConnectionType() != null) {
                data.put("connection_type", monitoringLog.getConnectionType());
            }

            // Audio specific fields
            if (monitoringLog.getAudioSizeBytes() != null) {
                data.put("audio_size_bytes", monitoringLog.getAudioSizeBytes());
            }

            if (monitoringLog.getAudioFormat() != null) {
                data.put("audio_format", monitoringLog.getAudioFormat());
            }

            if (monitoringLog.getAudioDurationSeconds() != null) {
                data.put("audio_duration_seconds", monitoringLog.getAudioDurationSeconds());
            }

            // S3 specific fields
            if (monitoringLog.getS3Bucket() != null) {
                data.put("s3_bucket", monitoringLog.getS3Bucket());
            }

            if (monitoringLog.getS3Key() != null) {
                data.put("s3_key", monitoringLog.getS3Key());
            }

            if (monitoringLog.getUploadSizeBytes() != null) {
                data.put("upload_size_bytes", monitoringLog.getUploadSizeBytes());
            }

            // ASR specific fields
            if (monitoringLog.getAsrProvider() != null) {
                data.put("asr_provider", monitoringLog.getAsrProvider().toString());
            }

            if (monitoringLog.getConfidenceScore() != null) {
                data.put("confidence_score", monitoringLog.getConfidenceScore());
            }

            if (monitoringLog.getTranscribedText() != null) {
                data.put("transcribed_text", monitoringLog.getTranscribedText());
            }

            if (monitoringLog.getLanguage() != null) {
                data.put("language", monitoringLog.getLanguage());
            }

            if (monitoringLog.getSilenceThreshold() != null) {
                data.put("silence_threshold", monitoringLog.getSilenceThreshold());
            }

            if (monitoringLog.getModelVersion() != null) {
                data.put("model_version", monitoringLog.getModelVersion());
            }

            if (monitoringLog.getGrpcEndpoint() != null) {
                data.put("grpc_endpoint", monitoringLog.getGrpcEndpoint());
            }

            if (monitoringLog.getApiResponseCode() != null) {
                data.put("api_response_code", monitoringLog.getApiResponseCode());
            }

            // LLM specific fields
            if (monitoringLog.getLlmCallType() != null) {
                data.put("llm_call_type", monitoringLog.getLlmCallType());
            }

            if (monitoringLog.getLlmProvider() != null) {
                data.put("llm_provider", monitoringLog.getLlmProvider());
            }

            if (monitoringLog.getModelName() != null) {
                data.put("model_name", monitoringLog.getModelName());
            }

            if (monitoringLog.getPromptTokens() != null) {
                data.put("prompt_tokens", monitoringLog.getPromptTokens());
            }

            if (monitoringLog.getCompletionTokens() != null) {
                data.put("completion_tokens", monitoringLog.getCompletionTokens());
            }

            if (monitoringLog.getTotalTokens() != null) {
                data.put("total_tokens", monitoringLog.getTotalTokens());
            }

            if (monitoringLog.getLlmResponse() != null) {
                data.put("llm_response", monitoringLog.getLlmResponse());
            }

            if (monitoringLog.getActionType() != null) {
                data.put("action_type", monitoringLog.getActionType());
            }

            if (monitoringLog.getActionParameters() != null) {
                ObjectNode actionParamsNode = objectMapper.valueToTree(monitoringLog.getActionParameters());
                data.set("action_parameters", actionParamsNode);
            }

            if (monitoringLog.getActionResult() != null) {
                data.put("action_result", monitoringLog.getActionResult());
            }

            // TTS specific fields
            if (monitoringLog.getTtsProvider() != null) {
                data.put("tts_provider", monitoringLog.getTtsProvider());
            }

            if (monitoringLog.getVoiceName() != null) {
                data.put("voice_name", monitoringLog.getVoiceName());
            }

            if (monitoringLog.getTextLength() != null) {
                data.put("text_length", monitoringLog.getTextLength());
            }

            if (monitoringLog.getAudioOutputSizeBytes() != null) {
                data.put("audio_output_size_bytes", monitoringLog.getAudioOutputSizeBytes());
            }

            if (monitoringLog.getAudioOutputDurationSeconds() != null) {
                data.put("audio_output_duration_seconds", monitoringLog.getAudioOutputDurationSeconds());
            }

            // Animation specific fields
            if (monitoringLog.getAnimationType() != null) {
                data.put("animation_type", monitoringLog.getAnimationType());
            }

            if (monitoringLog.getAnimationDurationSeconds() != null) {
                data.put("animation_duration_seconds", monitoringLog.getAnimationDurationSeconds());
            }

            if (monitoringLog.getAnimationFileSizeBytes() != null) {
                data.put("animation_file_size_bytes", monitoringLog.getAnimationFileSizeBytes());
            }

            // Response specific fields
            if (monitoringLog.getResponseType() != null) {
                data.put("response_type", monitoringLog.getResponseType());
            }

            if (monitoringLog.getTotalResponseSizeBytes() != null) {
                data.put("total_response_size_bytes", monitoringLog.getTotalResponseSizeBytes());
            }

            // Conversation end specific fields
            if (monitoringLog.getTotalDurationMs() != null) {
                data.put("total_duration_ms", monitoringLog.getTotalDurationMs());
            }

            if (monitoringLog.getStagesCompleted() != null) {
                data.put("stages_completed", monitoringLog.getStagesCompleted());
            }

            if (monitoringLog.getTotalStages() != null) {
                data.put("total_stages", monitoringLog.getTotalStages());
            }

            if (monitoringLog.getSuccessRate() != null) {
                data.put("success_rate", monitoringLog.getSuccessRate());
            }

            if (monitoringLog.getConnectionDurationSeconds() != null) {
                data.put("connection_duration_seconds", monitoringLog.getConnectionDurationSeconds());
            }

            // Error specific fields
            if (monitoringLog.getErrorCode() != null) {
                data.put("error_code", monitoringLog.getErrorCode());
            }

            if (monitoringLog.getErrorMessage() != null) {
                data.put("error_message", monitoringLog.getErrorMessage());
            }

            if (monitoringLog.getErrorStackTrace() != null) {
                data.put("error_stack_trace", monitoringLog.getErrorStackTrace());
            }

            if (monitoringLog.getRequestData() != null) {
                data.set("request_data", monitoringLog.getRequestData());
            }

            if (monitoringLog.getResponseData() != null) {
                data.set("response_data", monitoringLog.getResponseData());
            }

            if (monitoringLog.getRetryCount() != null) {
                data.put("retry_count", monitoringLog.getRetryCount());
            }

            // HTTP specific fields
            if (monitoringLog.getHttpMethod() != null) {
                data.put("http_method", monitoringLog.getHttpMethod());
            }

            if (monitoringLog.getHttpUrl() != null) {
                data.put("http_url", monitoringLog.getHttpUrl());
            }

            if (monitoringLog.getHttpHeaders() != null) {
                ObjectNode headersNode = objectMapper.valueToTree(monitoringLog.getHttpHeaders());
                data.set("http_headers", headersNode);
            }

            if (monitoringLog.getHttpQueryParams() != null) {
                ObjectNode queryParamsNode = objectMapper.valueToTree(monitoringLog.getHttpQueryParams());
                data.set("http_query_params", queryParamsNode);
            }

            if (monitoringLog.getTraceContext() != null) {
                ObjectNode traceData = objectMapper.valueToTree(monitoringLog.getTraceContext());
                data.set("trace_context", traceData);
            }

            if (monitoringLog.getMethodName() != null) {
                data.put("method_name", monitoringLog.getMethodName());
            }

            if (monitoringLog.getClassName() != null) {
                data.put("class_name", monitoringLog.getClassName());
            }

            if (monitoringLog.getMetadata() != null) {
                ObjectNode metadataNode = objectMapper.valueToTree(monitoringLog.getMetadata());
                data.set("metadata", metadataNode);
            }

            Request request = new Request.Builder()
                    .url(endpoint)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("DD-API-KEY", apiKey)
                    .addHeader("DD-APPLICATION-KEY", applicationKey)
                    .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsString(data)))
                    .build();

            try (Response response = okHttpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    log.debug("Sent monitoring log to Datadog successfully: " + data.get("log_type"));
                } else {
                    log.warn("Failed to send log to Datadog: HTTP {}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("Error sending log to Datadog: {}", e.getMessage(), e);
        }
    }

    @Async("taskExecutor")
    public void logWebSocketEvent(WebSocketEventDTO event) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("websocket_session_id", event.getSessionId());
        metadata.put("websocket_endpoint", event.getEndpoint());
        metadata.put("websocket_event_type", event.getEventType());
        metadata.put("websocket_user_id", event.getUserId());
        metadata.put("websocket_robot_id", event.getRobotId());
        metadata.put("websocket_remote_address", event.getRemoteAddress());

        if (event.getMessageSize() != null) {
            metadata.put("websocket_message_size", event.getMessageSize());
        }

        if (event.getConnectionDurationMs() != null) {
            metadata.put("websocket_connection_duration_ms", event.getConnectionDurationMs());
        }

        DatadogLogType logType;
        String level = "INFO";
        String message = "WebSocket " + event.getEventType();

        switch (event.getEventType()) {
            case "CONNECT":
                logType = DatadogLogType.WEBSOCKET_CONNECTION_ESTABLISHED;
                message = "WebSocket connection established";
                break;
            case "DISCONNECT":
                logType = DatadogLogType.WEBSOCKET_CONNECTION_CLOSED;
                message = "WebSocket connection closed";
                break;
            case "MESSAGE_RECEIVED":
                logType = DatadogLogType.WEBSOCKET_MESSAGE_RECEIVED;
                message = "WebSocket message received";
                break;
            case "MESSAGE_SENT":
                logType = DatadogLogType.WEBSOCKET_MESSAGE_SENT;
                message = "WebSocket message sent";
                break;
            case "ERROR":
                logType = DatadogLogType.WEBSOCKET_ERROR;
                message = "WebSocket error: " + event.getErrorMessage();
                level = "ERROR";
                break;
            default:
                logType = DatadogLogType.WEBSOCKET_ERROR;
                break;
        }

        MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                .logType(logType)
                .message(message)
                .level(level)
                .timestamp(event.getTimestamp())
                .serviceName(serviceName)
                .metadata(metadata)
                .errorMessage(event.getErrorMessage())
                .build();

        sendLogToDatadog(monitoringLog);
    }

    @Async("taskExecutor")
    public void logExternalApiCall(ExternalApiCallDTO apiCall) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("external_api_name", apiCall.getApiName());
        metadata.put("external_api_url", apiCall.getUrl());
        metadata.put("external_api_method", apiCall.getMethod());
        metadata.put("external_api_duration_ms", apiCall.getDurationMs());
        metadata.put("external_api_status", apiCall.getResponseStatus());
        metadata.put("external_api_success", apiCall.getSuccess());

        if (apiCall.getTimeoutMs() != null) {
            metadata.put("external_api_timeout_ms", apiCall.getTimeoutMs());
        }

        if (apiCall.getRetryCount() != null) {
            metadata.put("external_api_retry_count", apiCall.getRetryCount());
        }

        DatadogLogType logType = apiCall.getSuccess()
                ? DatadogLogType.EXTERNAL_API_CALL_SUCCESS
                : DatadogLogType.EXTERNAL_API_CALL_ERROR;

        String level = apiCall.getSuccess() ? "INFO" : "ERROR";
        String message = String.format("External API call to %s: %s",
                apiCall.getApiName(), apiCall.getSuccess() ? "SUCCESS" : "FAILED");

        MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                .logType(logType)
                .message(message)
                .level(level)
                .timestamp(apiCall.getTimestamp())
                .serviceName(serviceName)
                .durationMs(apiCall.getDurationMs())
                .errorMessage(apiCall.getErrorMessage())
                .requestData(apiCall.getRequestBody())
                .responseData(apiCall.getResponseBody())
                .metadata(metadata)
                .build();

        sendLogToDatadog(monitoringLog);
    }

    @Async("taskExecutor")
    public void logServiceMethodStart(String className, String methodName, TraceContextDTO traceContext) {
        MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                .logType(DatadogLogType.SERVICE_METHOD_START)
                .message(String.format("Method %s.%s started", className, methodName))
                .level("DEBUG")
                .timestamp(Instant.now())
                .serviceName(serviceName)
                .className(className)
                .methodName(methodName)
                .traceContext(traceContext)
                .build();

        sendLogToDatadog(monitoringLog);
    }

    @Async("taskExecutor")
    public void logServiceMethodEnd(String className, String methodName, long durationMs, TraceContextDTO traceContext) {
        MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                .logType(DatadogLogType.SERVICE_METHOD_END)
                .message(String.format("Method %s.%s completed in %d ms", className, methodName, durationMs))
                .level("DEBUG")
                .timestamp(Instant.now())
                .serviceName(serviceName)
                .className(className)
                .methodName(methodName)
                .durationMs(durationMs)
                .traceContext(traceContext)
                .build();

        sendLogToDatadog(monitoringLog);
    }

    @Async("taskExecutor")
    public void logServiceMethodError(String className, String methodName, Exception error, TraceContextDTO traceContext) {
        MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                .logType(DatadogLogType.SERVICE_METHOD_ERROR)
                .message(String.format("Method %s.%s failed with error: %s", className, methodName, error.getMessage()))
                .level("ERROR")
                .timestamp(Instant.now())
                .serviceName(serviceName)
                .className(className)
                .methodName(methodName)
                .errorMessage(error.getMessage())
                .errorStackTrace(getStackTrace(error))
                .traceContext(traceContext)
                .build();

        sendLogToDatadog(monitoringLog);
    }

    private String getStackTrace(Exception e) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }
}
