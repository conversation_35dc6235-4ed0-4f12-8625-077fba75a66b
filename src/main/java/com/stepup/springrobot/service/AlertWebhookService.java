package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import okhttp3.*;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
@RequiredArgsConstructor
@Log4j2
public class AlertWebhookService {

    private static final String NOTIFY_WEBHOOK = "https://chat.googleapis.com/v1/spaces/AAQAAl2Scho/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=FRDrKS9VRX3zAQMrIwt4GjyIymiR_FVBokXC_2KqE_I";
    private static final String ERROR_WEBHOOK = "https://chat.googleapis.com/v1/spaces/AAQAAl2Scho/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=yWGygpiN-GSRRKsyF0YdaDpNSzb-t3XuYmvCeq0OKSk";

    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    private final OkHttpClient okHttpClient = new OkHttpClient();
    private final ObjectMapper objectMapper = new ObjectMapper();

    public void sendNotify(String message) {
        postToWebhook(NOTIFY_WEBHOOK, message);
    }

    public void sendError(String message) {
        postToWebhook(ERROR_WEBHOOK, message);
    }

    private void postToWebhook(String url, String text) {
        try {
            String bodyJson = objectMapper.writeValueAsString(new TextBody(text));
            RequestBody body = RequestBody.create(JSON, bodyJson);
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .build();
            try (Response response = okHttpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.warn("Gửi webhook thất bại. code={}, msg={}", response.code(), response.message());
                }
            }
        } catch (IOException e) {
            log.warn("Không thể gửi webhook: {}", e.getMessage());
        }
    }

    private static class TextBody {
        @SuppressWarnings("unused")
        public final String text;

        private TextBody(String text) {
            this.text = text;
        }

        @Override
        public String toString() {
            return text;
        }
    }
}


