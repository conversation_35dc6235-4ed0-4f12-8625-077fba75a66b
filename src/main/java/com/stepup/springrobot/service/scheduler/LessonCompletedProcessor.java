package com.stepup.springrobot.service.scheduler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.model.alarm.ScheduledTask;
import com.stepup.springrobot.service.ParentDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Processor for LESSON_COMPLETED type tasks
 * Sends notification when child has completed a lesson
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class LessonCompletedProcessor implements TaskProcessor {

    private final ParentDashboardService parentDashboardService;
    private final ObjectMapper objectMapper;

    @Override
    public boolean process(ScheduledTask task) {
        try {
            log.info("Processing LESSON_COMPLETED task: {}", task.getId());

            // Parse the payload JSON to get task-specific data
            LessonCompletedPayload payload = objectMapper.readValue(task.getPayload(), LessonCompletedPayload.class);

            // Call the parent dashboard service to send the notification
            parentDashboardService.sendLessonCompletedNotification(payload.getUserId(), payload.getLessonId());

            log.info("Successfully processed LESSON_COMPLETED task: {}", task.getId());
            return true;

        } catch (Exception e) {
            log.error("Error processing LESSON_COMPLETED task: {}", task.getId(), e);
            return false;
        }
    }

    @Override
    public String getTaskType() {
        return "LESSON_COMPLETED";
    }

    @Override
    public String getDescription() {
        return "Processes notifications when child has completed a lesson";
    }

    /**
     * Inner class to represent the payload structure for lesson completed notifications
     */
    public static class LessonCompletedPayload {
        private String userId;
        private String lessonId;
        private String conversationId;
        private Long studyTimeMinutes;

        // Getters and setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getLessonId() {
            return lessonId;
        }

        public void setLessonId(String lessonId) {
            this.lessonId = lessonId;
        }

        public String getConversationId() {
            return conversationId;
        }

        public void setConversationId(String conversationId) {
            this.conversationId = conversationId;
        }

        public Long getStudyTimeMinutes() {
            return studyTimeMinutes;
        }

        public void setStudyTimeMinutes(Long studyTimeMinutes) {
            this.studyTimeMinutes = studyTimeMinutes;
        }
    }
}
