package com.stepup.springrobot.service.scheduler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.model.alarm.ScheduledTask;
import com.stepup.springrobot.service.ParentDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Processor for CUSTOM_STUDY_REMINDER type tasks
 * Sends custom study reminder notifications
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CustomStudyReminderProcessor implements TaskProcessor {

    private final ParentDashboardService parentDashboardService;
    private final ObjectMapper objectMapper;

    @Override
    public boolean process(ScheduledTask task) {
        try {
            log.info("Processing CUSTOM_STUDY_REMINDER task: {}", task.getId());

            // Parse the payload JSON to get task-specific data
            CustomStudyReminderPayload payload = objectMapper.readValue(task.getPayload(), CustomStudyReminderPayload.class);

            // Call the parent dashboard service to send the notification
            parentDashboardService.sendCustomStudyReminder(
                    payload.getUserId(),
                    payload.getTitle(),
                    payload.getCustomMessage(),
                    payload.getLessonId()
            );

            log.info("Successfully processed CUSTOM_STUDY_REMINDER task: {}", task.getId());
            return true;

        } catch (Exception e) {
            log.error("Error processing CUSTOM_STUDY_REMINDER task: {}", task.getId(), e);
            return false;
        }
    }

    @Override
    public String getTaskType() {
        return "CUSTOM_STUDY_REMINDER";
    }

    @Override
    public String getDescription() {
        return "Processes custom study reminder notifications";
    }

    /**
     * Inner class to represent the payload structure for custom study reminder notifications
     */
    public static class CustomStudyReminderPayload {
        private String userId;
        private String title;
        private String customMessage;
        private String lessonId;
        private String reminderType;

        // Getters and setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getCustomMessage() {
            return customMessage;
        }

        public void setCustomMessage(String customMessage) {
            this.customMessage = customMessage;
        }

        public String getLessonId() {
            return lessonId;
        }

        public void setLessonId(String lessonId) {
            this.lessonId = lessonId;
        }

        public String getReminderType() {
            return reminderType;
        }

        public void setReminderType(String reminderType) {
            this.reminderType = reminderType;
        }
    }
}
