package com.stepup.springrobot.service.scheduler;

import com.stepup.springrobot.model.alarm.ScheduledTask;
import com.stepup.springrobot.model.alarm.TaskStatus;
import com.stepup.springrobot.repository.alarm.ScheduledTaskRepository;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Centralized scheduler service that processes all types of scheduled tasks
 * using the Strategy Pattern to handle different task types
 */
@Service
@Slf4j
public class CentralizedSchedulerService {

    private final ScheduledTaskRepository scheduledTaskRepository;
    private final Map<String, TaskProcessor> taskProcessorsByType;

    @Autowired
    public CentralizedSchedulerService(ScheduledTaskRepository scheduledTaskRepository, List<TaskProcessor> processors) {
        this.scheduledTaskRepository = scheduledTaskRepository;
        this.taskProcessorsByType = processors.stream()
                .collect(Collectors.toMap(TaskProcessor::getTaskType, p -> p));
    }

    /**
     * Process pending tasks every minute
     * Uses ShedLock to ensure only one instance runs in distributed environment
     */
    @Scheduled(cron = "0 * * * * *") // Run every minute
    @SchedulerLock(name = "processScheduledTasks", lockAtLeastFor = "55s", lockAtMostFor = "59s")
    public void processPendingTasks() {
        try {
            log.debug("Starting to process pending scheduled tasks");

            // Find all pending tasks that are due for execution
            List<ScheduledTask> pendingTasks = scheduledTaskRepository
                    .findByStatusAndScheduledTimeLessThanEqual(TaskStatus.PENDING, LocalDateTime.now());

            if (pendingTasks.isEmpty()) {
                log.debug("No pending tasks to process");
                return;
            }

            log.info("Processing {} pending tasks", pendingTasks.size());

            // Process tasks in batches to avoid overwhelming the system
            processBatch(pendingTasks);

        } catch (Exception e) {
            log.error("Error processing pending tasks", e);
        }
    }

    /**
     * Process a batch of tasks
     */
    private void processBatch(List<ScheduledTask> tasks) {
        for (ScheduledTask task : tasks) {
            try {
                processTask(task);
            } catch (Exception e) {
                log.error("Error processing task: {}", task.getId(), e);
                handleTaskError(task, e);
            }
        }
    }

    /**
     * Process a single task using the appropriate processor
     */
    private void processTask(ScheduledTask task) {
        log.debug("Processing task: {} of type: {}", task.getId(), task.getTaskType());

        // Mark task as processing
        task.setStatus(TaskStatus.PROCESSING);
        scheduledTaskRepository.save(task);

        // Find the appropriate processor for this task type
        TaskProcessor processor = taskProcessorsByType.get(task.getTaskType());

        if (processor == null) {
            log.error("No processor found for task type: {}", task.getTaskType());
            handleTaskError(task, new RuntimeException("No processor found for task type: " + task.getTaskType()));
            return;
        }

        // Process the task
        boolean success = processor.process(task);

        if (success) {
            // Mark task as completed
            task.setStatus(TaskStatus.DONE);
            task.setErrorMessage(null);
            log.info("Successfully processed task: {} of type: {}", task.getId(), task.getTaskType());
        } else {
            // Mark task as failed
            handleTaskError(task, new RuntimeException("Task processing returned false"));
        }

        scheduledTaskRepository.save(task);
    }

    /**
     * Handle task processing errors with retry logic
     */
    private void handleTaskError(ScheduledTask task, Exception e) {
        task.setRetryCount(task.getRetryCount() + 1);
        task.setErrorMessage(e.getMessage());

        if (task.getRetryCount() >= task.getMaxRetries()) {
            task.setStatus(TaskStatus.FAILED);
            log.error("Task {} failed after {} retries: {}", task.getId(), task.getRetryCount(), e.getMessage());
        } else {
            // Reschedule task for retry with exponential backoff
            task.setStatus(TaskStatus.PENDING);
            long delayMinutes = (long) Math.pow(2, task.getRetryCount()); // Exponential backoff: 2, 4, 8 minutes
            task.setScheduledTime(LocalDateTime.now().plusMinutes(delayMinutes));
            log.warn("Rescheduling task {} for retry {} in {} minutes", task.getId(), task.getRetryCount(), delayMinutes);
        }

        scheduledTaskRepository.save(task);
    }

    /**
     * Clean up old completed and failed tasks daily at 2 AM
     */
    @Scheduled(cron = "0 0 2 * * *") // Run at 2 AM daily
    @SchedulerLock(name = "cleanupOldScheduledTasks", lockAtLeastFor = "5m", lockAtMostFor = "10m")
    public void cleanupOldTasks() {
        try {
            log.info("Starting cleanup of old scheduled tasks");

            // Delete tasks older than 30 days
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30);
            int deletedCount = scheduledTaskRepository.deleteOldTasks(cutoffDate);

            log.info("Cleaned up {} old scheduled tasks", deletedCount);

        } catch (Exception e) {
            log.error("Error cleaning up old scheduled tasks", e);
        }
    }

    /**
     * Monitor task statistics every 5 minutes
     */
    @Scheduled(cron = "0 */5 * * * *") // Run every 5 minutes
    @SchedulerLock(name = "monitorScheduledTasks", lockAtLeastFor = "1m", lockAtMostFor = "4m")
    public void monitorTaskStats() {
        try {
            long pendingCount = scheduledTaskRepository.countByStatus(TaskStatus.PENDING);
            long processingCount = scheduledTaskRepository.countByStatus(TaskStatus.PROCESSING);
            long failedCount = scheduledTaskRepository.countByStatus(TaskStatus.FAILED);

            if (pendingCount > 0 || processingCount > 0 || failedCount > 0) {
                log.info("Scheduled Tasks Stats - Pending: {}, Processing: {}, Failed: {}",
                        pendingCount, processingCount, failedCount);
            }

        } catch (Exception e) {
            log.error("Error monitoring scheduled task statistics", e);
        }
    }

    /**
     * Get available task processors for debugging/monitoring
     */
    public Map<String, TaskProcessor> getAvailableProcessors() {
        return taskProcessorsByType;
    }

    /**
     * Clean up expired ShedLock entries every hour
     * This prevents accumulation of old lock records
     */
    @Scheduled(cron = "0 0 * * * *") // Run every hour
    @Transactional
    public void cleanupExpiredLocks() {
        try {
            log.info("Cleaning up expired ShedLock entries");

            // Execute native query to delete expired locks
            int deletedCount = scheduledTaskRepository.deleteExpiredLocks();

            if (deletedCount > 0) {
                log.info("Cleaned up {} expired ShedLock entries", deletedCount);
            } else {
                log.debug("No expired ShedLock entries to clean up");
            }

        } catch (Exception e) {
            log.error("Error cleaning up expired ShedLock entries", e);
        }
    }

    /**
     * Manual method to clean up expired locks immediately
     * Can be called from controller or admin interface
     */
    @Transactional
    public int cleanupExpiredLocksNow() {
        try {
            log.info("Manually cleaning up expired ShedLock entries");
            int deletedCount = scheduledTaskRepository.deleteExpiredLocks();
            log.info("Manually cleaned up {} expired ShedLock entries", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("Error manually cleaning up expired ShedLock entries", e);
            return 0;
        }
    }
} 