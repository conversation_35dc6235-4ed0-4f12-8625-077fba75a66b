package com.stepup.springrobot.service.scheduler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.model.alarm.ScheduledTask;
import com.stepup.springrobot.service.ParentDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Processor for STUDY_REMINDER type tasks
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class StudyReminderProcessor implements TaskProcessor {
    
    private final ParentDashboardService parentDashboardService;
    private final ObjectMapper objectMapper;
    
    @Override
    public boolean process(ScheduledTask task) {
        try {
            log.info("Processing STUDY_REMINDER task: {}", task.getId());
            
            // Parse the payload JSON to get task-specific data
            StudyReminderPayload payload = objectMapper.readValue(task.getPayload(), StudyReminderPayload.class);
            
            // Call the parent dashboard service to send the study time reminder
            parentDashboardService.sendStudyTimeReminder(payload.getUserId());
            boolean success = true; // ParentDashboardService methods don't return boolean, so we assume success
            
            if (success) {
                log.info("Successfully processed STUDY_REMINDER task: {}", task.getId());
            } else {
                log.warn("Failed to send study reminder for task: {}", task.getId());
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("Error processing STUDY_REMINDER task: {}", task.getId(), e);
            return false;
        }
    }
    
    @Override
    public String getTaskType() {
        return "STUDY_REMINDER";
    }
    
    @Override
    public String getDescription() {
        return "Processes study time reminders for users";
    }
    
    /**
     * Inner class to represent the payload structure for study reminders
     */
    public static class StudyReminderPayload {
        private String userId;
        private String alarmTime;
        private Long alarmScheduleId;
        
        // Getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        
        public String getAlarmTime() { return alarmTime; }
        public void setAlarmTime(String alarmTime) { this.alarmTime = alarmTime; }
        
        public Long getAlarmScheduleId() { return alarmScheduleId; }
        public void setAlarmScheduleId(Long alarmScheduleId) { this.alarmScheduleId = alarmScheduleId; }
    }
} 