package com.stepup.springrobot.service.scheduler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.model.alarm.ScheduledTask;
import com.stepup.springrobot.model.alarm.TaskStatus;
import com.stepup.springrobot.repository.alarm.ScheduledTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing scheduled tasks
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ScheduledTaskService {
    
    private final ScheduledTaskRepository scheduledTaskRepository;
    private final ObjectMapper objectMapper;
    
    /**
     * Create a new scheduled task
     * 
     * @param taskType the type of task
     * @param payload the task-specific data as JSON string
     * @param scheduledTime when the task should be executed
     * @param createdBy who created the task
     * @return the created task
     */
    public ScheduledTask createTask(String taskType, String payload, LocalDateTime scheduledTime, String createdBy) {
        try {
            ScheduledTask task = ScheduledTask.builder()
                .taskType(taskType)
                .payload(payload)
                .scheduledTime(scheduledTime)
                .status(TaskStatus.PENDING)
                .retryCount(0)
                .maxRetries(3)
                .createdBy(createdBy)
                .build();
            
            ScheduledTask savedTask = scheduledTaskRepository.save(task);
            log.info("Created scheduled task: {} of type: {} scheduled for: {}", 
                savedTask.getId(), taskType, scheduledTime);
            
            return savedTask;
            
        } catch (Exception e) {
            log.error("Error creating scheduled task of type: {} - {}", taskType, e.getMessage(), e);
            throw new RuntimeException("Failed to create scheduled task", e);
        }
    }
    
    /**
     * Create a new scheduled task with object payload (automatically converts to JSON)
     * 
     * @param taskType the type of task
     * @param payloadObject the task-specific data object
     * @param scheduledTime when the task should be executed
     * @param createdBy who created the task
     * @return the created task
     */
    public ScheduledTask createTask(String taskType, Object payloadObject, LocalDateTime scheduledTime, String createdBy) {
        try {
            String payload = objectMapper.writeValueAsString(payloadObject);
            return createTask(taskType, payload, scheduledTime, createdBy);
        } catch (Exception e) {
            log.error("Error serializing payload for task type: {}", taskType, e);
            throw new RuntimeException("Failed to serialize task payload", e);
        }
    }
    
    /**
     * Find a task by ID
     * 
     * @param taskId the task ID
     * @return the task if found
     */
    public Optional<ScheduledTask> findById(Long taskId) {
        return scheduledTaskRepository.findById(taskId);
    }
    
    /**
     * Find tasks by type and status
     * 
     * @param taskType the task type
     * @param status the task status
     * @return list of matching tasks
     */
    public List<ScheduledTask> findByTypeAndStatus(String taskType, TaskStatus status) {
        return scheduledTaskRepository.findByTaskTypeAndStatus(taskType, status);
    }
    
    /**
     * Update task status
     * 
     * @param taskId the task ID
     * @param status the new status
     * @param updatedBy who updated the task
     * @return the updated task
     */
    public ScheduledTask updateTaskStatus(Long taskId, TaskStatus status, String updatedBy) {
        Optional<ScheduledTask> taskOpt = scheduledTaskRepository.findById(taskId);
        if (taskOpt.isEmpty()) {
            throw new RuntimeException("Task not found: " + taskId);
        }
        
        ScheduledTask task = taskOpt.get();
        task.setStatus(status);
        task.setUpdatedBy(updatedBy);
        
        ScheduledTask updatedTask = scheduledTaskRepository.save(task);
        log.info("Updated task {} status to: {}", taskId, status);
        
        return updatedTask;
    }
    
    /**
     * Delete a task
     * 
     * @param taskId the task ID
     */
    public void deleteTask(Long taskId) {
        if (scheduledTaskRepository.existsById(taskId)) {
            scheduledTaskRepository.deleteById(taskId);
            log.info("Deleted scheduled task: {}", taskId);
        } else {
            log.warn("Attempted to delete non-existent task: {}", taskId);
        }
    }
    
    /**
     * Cancel all pending tasks of a specific type
     * 
     * @param taskType the task type to cancel
     * @param updatedBy who cancelled the tasks
     * @return number of cancelled tasks
     */
    public int cancelTasksByType(String taskType, String updatedBy) {
        List<ScheduledTask> pendingTasks = scheduledTaskRepository.findByTaskTypeAndStatus(taskType, TaskStatus.PENDING);
        
        for (ScheduledTask task : pendingTasks) {
            task.setStatus(TaskStatus.FAILED);
            task.setErrorMessage("Cancelled by user");
            task.setUpdatedBy(updatedBy);
            scheduledTaskRepository.save(task);
        }
        
        log.info("Cancelled {} pending tasks of type: {}", pendingTasks.size(), taskType);
        return pendingTasks.size();
    }
    
    /**
     * Get task statistics
     * 
     * @return map with task counts by status
     */
    public java.util.Map<TaskStatus, Long> getTaskStatistics() {
        java.util.Map<TaskStatus, Long> stats = new java.util.HashMap<>();
        for (TaskStatus status : TaskStatus.values()) {
            stats.put(status, scheduledTaskRepository.countByStatus(status));
        }
        return stats;
    }
    
    /**
     * Get task statistics by type
     * 
     * @param taskType the task type
     * @return map with task counts by status for the given type
     */
    public java.util.Map<TaskStatus, Long> getTaskStatisticsByType(String taskType) {
        java.util.Map<TaskStatus, Long> stats = new java.util.HashMap<>();
        for (TaskStatus status : TaskStatus.values()) {
            stats.put(status, scheduledTaskRepository.countByTaskTypeAndStatus(taskType, status));
        }
        return stats;
    }
} 