package com.stepup.springrobot.service.scheduler;

import com.stepup.springrobot.model.alarm.ScheduledTask;

/**
 * Strategy Pattern interface for processing different types of scheduled tasks
 */
public interface TaskProcessor {
    
    /**
     * Process a scheduled task
     * 
     * @param task the scheduled task to process
     * @return true if processing was successful, false otherwise
     */
    boolean process(ScheduledTask task);
    
    /**
     * Get the task type this processor handles
     * 
     * @return the task type string
     */
    String getTaskType();
    
    /**
     * Get a human-readable description of this processor
     * 
     * @return description of the processor
     */
    String getDescription();
} 