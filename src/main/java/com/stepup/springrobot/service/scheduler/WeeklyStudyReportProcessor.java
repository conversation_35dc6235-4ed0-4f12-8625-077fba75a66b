package com.stepup.springrobot.service.scheduler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.model.alarm.ScheduledTask;
import com.stepup.springrobot.service.ParentDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Processor for WEEKLY_STUDY_REPORT type tasks
 * Sends weekly study report notification
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WeeklyStudyReportProcessor implements TaskProcessor {

    private final ParentDashboardService parentDashboardService;
    private final ObjectMapper objectMapper;

    @Override
    public boolean process(ScheduledTask task) {
        try {
            log.info("Processing WEEKLY_STUDY_REPORT task: {}", task.getId());

            // Parse the payload JSON to get task-specific data
            WeeklyStudyReportPayload payload = objectMapper.readValue(task.getPayload(), WeeklyStudyReportPayload.class);

            // Call the parent dashboard service to send the notification
            parentDashboardService.sendWeeklyStudyReport(payload.getUserId());

            log.info("Successfully processed WEEKLY_STUDY_REPORT task: {}", task.getId());
            return true;

        } catch (Exception e) {
            log.error("Error processing WEEKLY_STUDY_REPORT task: {}", task.getId(), e);
            return false;
        }
    }

    @Override
    public String getTaskType() {
        return "WEEKLY_STUDY_REPORT";
    }

    @Override
    public String getDescription() {
        return "Processes weekly study report notifications";
    }

    /**
     * Inner class to represent the payload structure for weekly study report notifications
     */
    public static class WeeklyStudyReportPayload {
        private String userId;
        private String weekStartDate;
        private String weekEndDate;
        private Integer totalLessons;
        private Double totalStudyTime;

        // Getters and setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getWeekStartDate() {
            return weekStartDate;
        }

        public void setWeekStartDate(String weekStartDate) {
            this.weekStartDate = weekStartDate;
        }

        public String getWeekEndDate() {
            return weekEndDate;
        }

        public void setWeekEndDate(String weekEndDate) {
            this.weekEndDate = weekEndDate;
        }

        public Integer getTotalLessons() {
            return totalLessons;
        }

        public void setTotalLessons(Integer totalLessons) {
            this.totalLessons = totalLessons;
        }

        public Double getTotalStudyTime() {
            return totalStudyTime;
        }

        public void setTotalStudyTime(Double totalStudyTime) {
            this.totalStudyTime = totalStudyTime;
        }
    }
}
