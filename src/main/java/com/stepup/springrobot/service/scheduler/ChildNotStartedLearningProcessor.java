package com.stepup.springrobot.service.scheduler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.model.alarm.ScheduledTask;
import com.stepup.springrobot.service.ParentDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Processor for CHILD_NOT_STARTED_LEARNING type tasks
 * Sends notification when child hasn't started learning after scheduled study time
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ChildNotStartedLearningProcessor implements TaskProcessor {

    private final ParentDashboardService parentDashboardService;
    private final ObjectMapper objectMapper;

    @Override
    public boolean process(ScheduledTask task) {
        try {
            log.info("Processing CHILD_NOT_STARTED_LEARNING task: {}", task.getId());

            // Parse the payload JSON to get task-specific data
            ChildNotStartedLearningPayload payload = objectMapper.readValue(task.getPayload(), ChildNotStartedLearningPayload.class);

            // Call the parent dashboard service to send the notification
            parentDashboardService.sendChildNotStartedLearning(payload.getUserId(), payload.getLessonId());

            log.info("Successfully processed CHILD_NOT_STARTED_LEARNING task: {}", task.getId());
            return true;

        } catch (Exception e) {
            log.error("Error processing CHILD_NOT_STARTED_LEARNING task: {}", task.getId(), e);
            return false;
        }
    }

    @Override
    public String getTaskType() {
        return "CHILD_NOT_STARTED_LEARNING";
    }

    @Override
    public String getDescription() {
        return "Processes notifications when child hasn't started learning after scheduled study time";
    }

    /**
     * Inner class to represent the payload structure for child not started learning notifications
     */
    public static class ChildNotStartedLearningPayload {
        private String userId;
        private String lessonId;
        private String alarmTime;
        private Long alarmScheduleId;

        // Getters and setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getLessonId() {
            return lessonId;
        }

        public void setLessonId(String lessonId) {
            this.lessonId = lessonId;
        }

        public String getAlarmTime() {
            return alarmTime;
        }

        public void setAlarmTime(String alarmTime) {
            this.alarmTime = alarmTime;
        }

        public Long getAlarmScheduleId() {
            return alarmScheduleId;
        }

        public void setAlarmScheduleId(Long alarmScheduleId) {
            this.alarmScheduleId = alarmScheduleId;
        }
    }
}
