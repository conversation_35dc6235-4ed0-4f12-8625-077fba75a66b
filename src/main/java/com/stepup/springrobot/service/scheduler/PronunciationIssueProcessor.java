package com.stepup.springrobot.service.scheduler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.model.alarm.ScheduledTask;
import com.stepup.springrobot.service.ParentDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Processor for PRONUNCIATION_ISSUE type tasks
 * Sends notification about pronunciation issues
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PronunciationIssueProcessor implements TaskProcessor {

    private final ParentDashboardService parentDashboardService;
    private final ObjectMapper objectMapper;

    @Override
    public boolean process(ScheduledTask task) {
        try {
            log.info("Processing PRONUNCIATION_ISSUE task: {}", task.getId());

            // Parse the payload JSON to get task-specific data
            PronunciationIssuePayload payload = objectMapper.readValue(task.getPayload(), PronunciationIssuePayload.class);

            // Call the parent dashboard service to send the notification
            parentDashboardService.sendPronunciationIssueNotification(payload.getUserId(), payload.getLessonId());

            log.info("Successfully processed PRONUNCIATION_ISSUE task: {}", task.getId());
            return true;

        } catch (Exception e) {
            log.error("Error processing PRONUNCIATION_ISSUE task: {}", task.getId(), e);
            return false;
        }
    }

    @Override
    public String getTaskType() {
        return "PRONUNCIATION_ISSUE";
    }

    @Override
    public String getDescription() {
        return "Processes notifications about pronunciation issues";
    }

    /**
     * Inner class to represent the payload structure for pronunciation issue notifications
     */
    public static class PronunciationIssuePayload {
        private String userId;
        private String lessonId;
        private String conversationId;
        private Integer pronunciationScore;
        private String issueDetails;

        // Getters and setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getLessonId() {
            return lessonId;
        }

        public void setLessonId(String lessonId) {
            this.lessonId = lessonId;
        }

        public String getConversationId() {
            return conversationId;
        }

        public void setConversationId(String conversationId) {
            this.conversationId = conversationId;
        }

        public Integer getPronunciationScore() {
            return pronunciationScore;
        }

        public void setPronunciationScore(Integer pronunciationScore) {
            this.pronunciationScore = pronunciationScore;
        }

        public String getIssueDetails() {
            return issueDetails;
        }

        public void setIssueDetails(String issueDetails) {
            this.issueDetails = issueDetails;
        }
    }
}
