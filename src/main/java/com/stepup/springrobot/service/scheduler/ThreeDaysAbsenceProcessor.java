package com.stepup.springrobot.service.scheduler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.model.alarm.ScheduledTask;
import com.stepup.springrobot.service.ParentDashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Processor for THREE_DAYS_ABSENCE type tasks
 * Sends notification when child hasn't used app for 3 days
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ThreeDaysAbsenceProcessor implements TaskProcessor {

    private final ParentDashboardService parentDashboardService;
    private final ObjectMapper objectMapper;

    @Override
    public boolean process(ScheduledTask task) {
        try {
            log.info("Processing THREE_DAYS_ABSENCE task: {}", task.getId());

            // Parse the payload JSON to get task-specific data
            ThreeDaysAbsencePayload payload = objectMapper.readValue(task.getPayload(), ThreeDaysAbsencePayload.class);

            // Call the parent dashboard service to send the notification
            parentDashboardService.sendThreeDaysAbsenceNotification(payload.getUserId());

            log.info("Successfully processed THREE_DAYS_ABSENCE task: {}", task.getId());
            return true;

        } catch (Exception e) {
            log.error("Error processing THREE_DAYS_ABSENCE task: {}", task.getId(), e);
            return false;
        }
    }

    @Override
    public String getTaskType() {
        return "THREE_DAYS_ABSENCE";
    }

    @Override
    public String getDescription() {
        return "Processes notifications when child hasn't used app for 3 days";
    }

    /**
     * Inner class to represent the payload structure for three days absence notifications
     */
    public static class ThreeDaysAbsencePayload {
        private String userId;
        private String lastActivityDate;
        private Integer daysAbsent;

        // Getters and setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getLastActivityDate() {
            return lastActivityDate;
        }

        public void setLastActivityDate(String lastActivityDate) {
            this.lastActivityDate = lastActivityDate;
        }

        public Integer getDaysAbsent() {
            return daysAbsent;
        }

        public void setDaysAbsent(Integer daysAbsent) {
            this.daysAbsent = daysAbsent;
        }
    }
}
