package com.stepup.springrobot.service.auth;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.common.ValidationUtils;
import com.stepup.springrobot.config.HttpUtils;
import com.stepup.springrobot.config.LambdaExceptionUtil;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.auth.*;
import com.stepup.springrobot.dto.sms.DataSMSClientReqDTO;
import com.stepup.springrobot.exception.business.request.TooManyRequestException;
import com.stepup.springrobot.exception.business.user.*;
import com.stepup.springrobot.model.auth.UserIssueToken;
import com.stepup.springrobot.model.auth.UserRequestSystem;
import com.stepup.springrobot.model.auth.UserRequestType;
import com.stepup.springrobot.model.sms.SMSAction;
import com.stepup.springrobot.model.sms.SMSServiceProvider;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.model.user.UserRole;
import com.stepup.springrobot.repository.auth.TestPhonesRepository;
import com.stepup.springrobot.repository.auth.UserIssueTokenRepository;
import com.stepup.springrobot.repository.auth.UserRepository;
import com.stepup.springrobot.repository.auth.UserRequestSystemRepository;
import com.stepup.springrobot.repository.personal.DeviceIdUserRepository;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.security.RefreshTokenService;
import com.stepup.springrobot.service.CommonService;
import com.stepup.springrobot.service.SlackWarningSystemService;
import com.stepup.springrobot.service.UploadFileToS3;
import com.stepup.springrobot.service.speech.RedisRateLimiter;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;

import static com.stepup.springrobot.common.CodeDefine.*;

@Log4j2
@Service
public class UserService extends CommonService {
    @Value("${rate_limit_request_signup}")
    private int rateLimitRequestSignupMax;

    @Value("${sms_block_time}")
    private int SMS_BLOCK_TIME;

    @Value("${rate_limit_request_forgot_pw_max}")
    private int rateLimitRequestForgotPwMax;

    @Value("${rate_limit_request_signin}")
    private int RATE_LIMIT_REQUEST_SIGNIN_MAX;

    @Value("${rate_limit_request_signup}")
    private int RATE_LIMIT_REQUEST_SIGNUP_MAX;

    @Value("${rate_limit_request_sms_max}")
    private int RATE_LIMIT_REQUEST_SMS_MAX;

    @Value("${rate_limit_verify_sms_max}")
    private int RATE_LIMIT_VERIFY_SMS_MAX;

    @Value("${is_enable_sms_otp_method}")
    private boolean IS_ENABLE_SMS_OTP_METHOD;

    @Value("${is_enable_zalo_otp_method}")
    private boolean IS_ENABLE_ZALO_OTP_METHOD;

    @Value("${is_enable_zalo_otp_fns_method}")
    private boolean IS_ENABLE_ZALO_OTP_FNS_METHOD;

    @Value("${sms_suffix_message_sms}")
    private String SMS_SUFFIX_MESSAGE_SMS;

    @Value("${sms_firebase_request}")
    private int SMS_FIREBASE_REQUEST;

    @Value("${sms_brandname_request}")
    private int SMS_BRAND_NAME_REQUEST;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserRequestSystemRepository userRequestSystemRepository;

    @Autowired
    private RedisRateLimiter redisRateLimiter;

    @Autowired
    private OTPService otpService;

    @Autowired
    private SMSService smsService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private PasswordEncoder bcryptEncoder;

    @Autowired
    private DeviceIdUserRepository deviceIdUserRepository;

    @Autowired
    private TestPhonesRepository testPhonesRepository;

    @Autowired
    private JwtService jwtService;

    @Autowired
    private RefreshTokenService refreshTokenService;


    @Autowired
    private UserIssueTokenRepository userIssueTokenRepository;

    /**
     * this statement create a thread pool of twenty threads
     * here we are assigning send mail task using ScheduledExecutorService.submit();
     */
    private ScheduledExecutorService quickService = Executors.newScheduledThreadPool(20); // Creates a thread pool that reuses fixed number of threads(as specified by noOfThreads in this case).

    protected UserService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }


    /**
     * Tạo mới user thì mặc định:
     * BookBought ko có
     * Role mặc định là User
     */
    @Transactional(rollbackFor = Exception.class)
    public DataResponseDTO<?> createUserFromApp(UserCreateReqDTO userCreateRequestDTO, HttpServletRequest request) {
        String phone = userCreateRequestDTO.getPhone();
        String ipAddress = HttpUtils.getClientIpAddress(request);

        // từ app không cho phép tạo các tài khoản không phải là số
        var normalizePhone = ValidationUtils.validateAndNormalizePhone(phone);
        String key = SIGNUP_LIMIT_KEY + ":" + normalizePhone.toLowerCase() + ":" + LocalDateTime.now().getHour();
        boolean isAllowed = redisRateLimiter.isAllowed(key, RATE_LIMIT_REQUEST_SIGNUP_MAX, SIGNUP_BLOCK_TIME, TimeUnit.MINUTES);
        if (!isAllowed) {
            throw new TooManyRequestException("Bạn đã thử quá nhiều. Vui lòng thử lại sau 10 phút.");
        }

        User user = userRepository.findByPhone(normalizePhone);
        if (user != null) {
            throw new UserAlreadyExistException();
        }

        if (!smsService.checkVerifiedSMSByPhone(normalizePhone, SMSAction.SIGN_UP.name())) {
            throw new InvalidOTPException("Mã xác thực hết hạn, bạn không thể tạo tài khoản rồi 😢");
        }

        user = User.builder()
                .phone(normalizePhone)
                .password(bcryptEncoder.encode(userCreateRequestDTO.getPassword()))
                .ip(ipAddress)
                .deviceId(userCreateRequestDTO.getDeviceId())
                .appV(userCreateRequestDTO.getAppV())
                .os(userCreateRequestDTO.getOs())
                .deviceName(userCreateRequestDTO.getDeviceName())
                .roles(Set.of(UserRole.USER))
                .build();

        user = userRepository.save(user);


        //   quickService.submit(() -> updateSignUpHistory(finalUser, userCreateRequestDTO, SUCCESS, isOldAppUser));

        // update user từ device_id sang user_id
        // deviceIdUserRepository.updateUserIdAndRemoveDeviceId(user.getId(), user.getDeviceId());

        updateNotificationDeviceToken(userCreateRequestDTO.getDeviceId(), userCreateRequestDTO.getDeviceToken(), user);

        // thực hiện đăng nhập luôn
        SignUpResponseDTO signUpResponseDTO = SignUpResponseDTO.builder()
                .accessToken(jwtService.generateToken(user, userCreateRequestDTO.getDeviceId()))
                .refreshToken(refreshTokenService.handleCreateRefreshToken(user.getId(), userCreateRequestDTO.getDeviceId()).getToken())
                .username("Học viên")
                .phone(user.getPhone())
                .description("Chào mừng bạn đến với \n nơi học nói tiếng Anh theo <JadeGreen>cách của top 5% cao thủ</JadeGreen>")
                .build();

        // lưu lịch sử đăng nhập
        //   quickService.submit(() -> updateLoginSuccessfulHistory(finalUser, ipAddress, LOGIN_FROM_SIGNUP));

        // lưu lịch sử request
//            var userSendOTPReqDTO = UserSendOTPReqDTO.builder()
//                    .phone(userCreateRequestDTO.getPhone())
//                    .type(UserReqSystem.SIGN_UP_SUCCESS)
//                    .build();
//            saveAsyncUserSendOTPHistory(ipAddress, userSendOTPReqDTO);

        log.info("create user successful " + phone);

        return new DataResponseDTO<>(CodeDefine.OK, "Tài khoản được tạo thành công: " + phone, signUpResponseDTO);
    }

    public DataResponseDTO<?> getUserForPreSignUp(HttpServletRequest request, String phone) throws InvalidPhoneNumberException {
        String ipAddress = HttpUtils.getClientIpAddress(request);
        String normalizePhone = ValidationUtils.validateAndNormalizePhone(phone);
        UserRequestSystem userRequestSystem = UserRequestSystem.builder()
                .ip(ipAddress)
                .normalizePhone(normalizePhone)
                .phone(phone)
                .build();

        String key = GET_PRE_SIGNUP_INFO_KEY + ":" + ipAddress + ":" + LocalDateTime.now().getHour();
        boolean isAllowed = redisRateLimiter.isAllowed(key, rateLimitRequestSignupMax, SIGNUP_BLOCK_TIME, TimeUnit.MINUTES);
        if (!isAllowed) {
            userRequestSystem.setType(UserRequestType.TOO_MANY_REQUESTS);
            saveUserRequestSystem(userRequestSystem);
            return new DataResponseDTO<>(CodeDefine.TOO_MANY_REQUESTS, "Bạn gửi yêu cầu quá nhiều, 10 phút nữa thử lại nhé!");
        }

        User user = userRepository.findByPhone(normalizePhone);
        if (user != null) {
            userRequestSystem.setType(UserRequestType.PRE_SIGN_UP_USER_EXIST);
            saveUserRequestSystem(userRequestSystem);

            if (!user.getIsActive()) {
                throw new UserLockedException();
            }

            return new DataResponseDTO<>(HttpStatus.CONFLICT.value(), "Tài khoản đã tồn tại, bạn hãy đăng nhập luôn nha ^_^");
        }

        final String action = "SIGN_UP";
        CompletableFuture<List<String>> otpMethodsFuture = CompletableFuture.supplyAsync(LambdaExceptionUtil.rethrowSupplier(() -> getAvailableOTPMethods(action, ipAddress)), quickService);

        userRequestSystem.setType(UserRequestType.PRE_SIGN_UP);
        saveUserRequestSystem(userRequestSystem);

        UserPreSignUpResDTO userPreSignUpResDTO = UserPreSignUpResDTO.builder()
                .isBrandname(true)
                .phone(phone)
                .smsFirebaseReq(SMS_FIREBASE_REQUEST)
                .smsBrandnameReq(SMS_BRAND_NAME_REQUEST)
                .availableOTPMethods(otpMethodsFuture.join())
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Tài khoản không tìm thấy, bạn có thể tạo tài khoản này <3", userPreSignUpResDTO);
    }

    public DataResponseDTO<?> getUserForgotPwdInfo(HttpServletRequest request, String phone) throws InvalidPhoneNumberException {
        String ipAddress = HttpUtils.getClientIpAddress(request);
        String normalizePhone = ValidationUtils.validateAndNormalizePhone(phone);
        UserRequestSystem userRequestSystem = UserRequestSystem.builder()
                .ip(ipAddress)
                .normalizePhone(normalizePhone)
                .phone(phone)
                .build();

        String key = GET_FORGOT_INFO_KEY + ":" + ipAddress + ":" + LocalDateTime.now().getHour();
        boolean isAllowed = redisRateLimiter.isAllowed(key, rateLimitRequestForgotPwMax, FORGOT_PASS_BLOCK_TIME, TimeUnit.MINUTES);
        if (!isAllowed) {
            userRequestSystem.setType(UserRequestType.TOO_MANY_REQUESTS);
            saveUserRequestSystem(userRequestSystem);
            throw new TooManyRequestException("Bạn gửi yêu cầu quá nhiều, " + FORGOT_PASS_BLOCK_TIME + " phút nữa thử lại nhé!");
        }

        User user = userRepository.findByPhone(normalizePhone);
        if (user == null) {
            throw new UserNotFoundException();
        }

        if (Boolean.FALSE.equals(user.getIsActive())) {
            throw new UserLockedException();
        }

        final String action = "FORGOT_PASSWORD";
        CompletableFuture<List<String>> otpMethodsFuture = CompletableFuture.supplyAsync(LambdaExceptionUtil.rethrowSupplier(() -> getAvailableOTPMethods(action, ipAddress)), quickService);

        userRequestSystem.setType(UserRequestType.PRE_FORGOT_PW);
        saveUserRequestSystem(userRequestSystem);

        UserPreSignUpResDTO userPreSignUpResDTO = UserPreSignUpResDTO.builder()
                .isBrandname(true)
                .phone(phone)
                .smsFirebaseReq(SMS_FIREBASE_REQUEST)
                .smsBrandnameReq(SMS_BRAND_NAME_REQUEST)
                .availableOTPMethods(otpMethodsFuture.join())
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Thông tin tài khoản của bạn đã được tìm thấy!", userPreSignUpResDTO);
    }

    private List<String> getAvailableOTPMethods(String action, String ipAddress) {
        List<String> availableOTPMethods = new ArrayList<>();

        if (IS_ENABLE_SMS_OTP_METHOD) {
            availableOTPMethods.add(SMSServiceProvider.SMS_BRANDNAME.getType());
        }

        boolean isAvailableFNS = IS_ENABLE_ZALO_OTP_FNS_METHOD && otpService.isAvailableFNSQuota();
        boolean isAvailableZNS = IS_ENABLE_ZALO_OTP_METHOD && otpService.isAvailableZNSQuota();
        if (isAvailableRateLimitOTP(action, SMSServiceProvider.SMS_ZALO.getType(), ipAddress) && (isAvailableFNS || isAvailableZNS)) {
            availableOTPMethods.add(SMSServiceProvider.SMS_ZALO.getType());
        }

        return availableOTPMethods;
    }

    private boolean isAvailableRateLimitOTP(String action, String smsServiceProvider, String ipAddress) {
        String key = action + "_" + smsServiceProvider + SMS_LIMIT_KEY + ":" + ipAddress + ":" + LocalDateTime.now().getHour();
        return redisRateLimiter.isAllowedButNotIncreaseValue(key, RATE_LIMIT_REQUEST_SMS_MAX, SMS_BLOCK_TIME, TimeUnit.MINUTES);
    }

    /**
     * Hàm này dùng 2 chỗ:
     * * 1 là lưu lúc thực hiện tìm kiếm khi đăng ký tài khoản
     * * 2 là khi thực hiện tìm kiếm tài khoản khi quên mật khẩu*
     *
     * @param userRequestSystem
     */
    public void saveUserRequestSystem(UserRequestSystem userRequestSystem) {
        quickService.submit(() -> userRequestSystemRepository.save(userRequestSystem));
    }

    public DataResponseDTO<?> sendOtpSMSToUser(HttpServletRequest request, UserSendSMSReqDTO requestDTO) {
        String ipAddress = HttpUtils.getClientIpAddress(request);

        String key = SMSAction.valueOf(requestDTO.getAction()) + "_" + requestDTO.getSmsServiceProvider().getType() + SMS_LIMIT_KEY + ":" + ipAddress + ":" + LocalDateTime.now().getHour();

        boolean isAllowed = redisRateLimiter.isAllowed(key, RATE_LIMIT_REQUEST_SMS_MAX, SMS_BLOCK_TIME, TimeUnit.MINUTES);
        if (!isAllowed) {
            throw new TooManyRequestException("Bạn gửi yêu cầu quá nhiều," + SMS_BLOCK_TIME + " phút nữa thử lại nhé!");
        }

        String phone = ValidationUtils.validateAndNormalizePhone(requestDTO.getPhone());

        String activeCode = getSecureRandomKey();
        DataSMSClientReqDTO dataSMSClientReqDTO = DataSMSClientReqDTO.builder()
                .phone(phone)
                .content(activeCode + " " + SMS_SUFFIX_MESSAGE_SMS)
                .otp(activeCode)
                .action(SMSAction.valueOf(requestDTO.getAction()))
                .ip(ipAddress)
                .isBrandname(requestDTO.isBrandname())
                .smsServiceProvider(requestDTO.getSmsServiceProvider())
                .build();

        smsService.sendSMSToClient(dataSMSClientReqDTO);
        return new DataResponseDTO<>(CodeDefine.OK, "Đã gửi OTP thành công");
    }

    private String getSecureRandomKey() {
        return String.valueOf(ThreadLocalRandom.current().nextInt(100000, 999999));
    }

    public DataResponseDTO<?> verifyOTPSMS(HttpServletRequest request, UserVerifySMSReqDTO requestDTO) throws InvalidPhoneNumberException {
        String ipAddress = HttpUtils.getClientIpAddress(request);
        var phone = ValidationUtils.validateAndNormalizePhone(requestDTO.getPhone());

        String key = requestDTO.getAction() + SMS_VERIFY_LIMIT_KEY + ":" + ipAddress + ":" + LocalDateTime.now().getHour();
        boolean isAllowed = redisRateLimiter.isAllowed(key, RATE_LIMIT_VERIFY_SMS_MAX, SMS_BLOCK_TIME, TimeUnit.MINUTES);
        if (!isAllowed) {
            throw new TooManyRequestException("Bạn gửi yêu cầu quá nhiều, " + SMS_BLOCK_TIME + " phút nữa thử lại nhé!");
        }

        boolean verifyStatus = smsService.verifySMSByPhoneAndOTP(phone, requestDTO.getOtp(), requestDTO.getAction());
        quickService.submit(() -> saveVerifyOTPSMSRequest(phone, requestDTO));
        if (verifyStatus) {
            redissonClient.getMapCache(key).deleteAsync();
            return new DataResponseDTO<>(OK, "Đã xác thực thành công");
        } else {
            throw new InvalidOTPException("Mã xác thực sai hoặc hết thời hạn rồi 😢");
        }
    }

    private void saveVerifyOTPSMSRequest(String phone, UserVerifySMSReqDTO requestDTO) {
        requestDTO.setTime(LocalDateTime.now());
        RMapCache<String, String> otpRequestMapCache = redissonClient.getMapCache(REDIS_KEY_VERIFY_OTP_REQUEST);
        try {
            List<UserVerifySMSReqDTO> otpRequestList;
            if (otpRequestMapCache.containsKey(phone)) {
                otpRequestList = objectMapper.readValue(otpRequestMapCache.get(phone), new TypeReference<>() {
                });
                otpRequestList.add(requestDTO);
            } else {
                otpRequestList = new ArrayList<>();
                otpRequestList.add(requestDTO);
            }

            otpRequestMapCache.put(phone, objectMapper.writeValueAsString(otpRequestList), 3, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("Error when save otp request {}", e.getMessage());
            otpRequestMapCache.remove(phone);
        }
    }

    public DataResponseDTO<?> updateUserPasswordByOTP(HttpServletRequest request, UserRecoveryPWByOTPReqDTO requestDTO) {
        String ipAddress = HttpUtils.getClientIpAddress(request);
        String normalizePhone = ValidationUtils.validateAndNormalizePhone(requestDTO.getPhone());
        String key = UPDATE_PASSWORD_KEY + ":" + ipAddress + ":" + LocalDateTime.now().getHour();
        boolean isAllowed = redisRateLimiter.isAllowed(key, rateLimitRequestForgotPwMax, FORGOT_PASS_BLOCK_TIME, TimeUnit.MINUTES);
        if (!isAllowed) {
            throw new TooManyRequestException("Bạn gửi yêu cầu quá nhiều, " + FORGOT_PASS_BLOCK_TIME + " phút nữa thử lại nhé!");
        }

        User user = userRepository.findByPhone(normalizePhone);
        if (user == null) {
            throw new UserNotFoundException();
        }

        if (Boolean.FALSE.equals(user.getIsActive())) {
            throw new UserLockedException();
        }

        if (!smsService.checkVerifiedSMSByPhone(normalizePhone, requestDTO.getAction().getType())) {
            throw new InvalidOTPException("Mã xác thực hết hạn, bạn không thể tạo tài khoản rồi 😢");
        }

        user.setPassword(bcryptEncoder.encode(requestDTO.getPassword()));
        userRepository.save(user);
        UserRequestSystem userRequestSystem = UserRequestSystem.builder()
                .ip(ipAddress)
                .normalizePhone(normalizePhone)
                .phone(requestDTO.getPhone())
                .type(UserRequestType.FORGOT_PW_SUCCESS)
                .build();
        saveUserRequestSystem(userRequestSystem);

        updateNotificationDeviceToken(requestDTO.getDeviceId(), requestDTO.getDeviceToken(), user);

        return new DataResponseDTO<>(CodeDefine.OK, "Mật khẩu của bạn đã được cập nhật", LoginResDTO.builder()
                .accessToken(jwtService.generateToken(user, requestDTO.getDeviceId()))
                .refreshToken(refreshTokenService.createRefreshToken(user.getId(), requestDTO.getDeviceId()).getToken())
                .build());
    }

    public void updateNotificationDeviceToken(String deviceId, String deviceToken, User user) {
        UserIssueToken userIssueToken = userIssueTokenRepository.findByDeviceId(deviceId);
        // Check device hiện tại đã lưu token chưa, nếu có thì cập nhật lại token mới
        if (userIssueToken != null) {
            userIssueToken.setDeviceToken(deviceToken);
            userIssueTokenRepository.save(userIssueToken);
        }
        // Note: If userIssueToken is null, it means the device hasn't been registered yet
        // This will be handled by JwtService.saveUserIssueTokenAndCheckMaxDeviceUse during token generation
    }

    @Transactional
    public DataResponseDTO<?> logOut(HttpServletRequest request, LogoutReqDTO requestDTO) throws IOException {
        AccountInfoDTO accountInfoDTO = getAccountInfo(request, requestDTO.getDeviceId(), false);
        String jti = accountInfoDTO.getUserData().getJti();
        
        // Delete by token - more precise and avoids conflict
        UserIssueToken userIssueToken = userIssueTokenRepository.findByToken(jti);
        if (userIssueToken != null) {
            userIssueTokenRepository.delete(userIssueToken);
        }
        
        // Clear JWT from Redis cache
        RMapCache<String, String> userTokenMapCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_USERS_ISSUE_TOKEN);
        userTokenMapCache.remove(jti);

        return new DataResponseDTO<>(CodeDefine.OK, "Đăng xuất thành công");
    }

}
