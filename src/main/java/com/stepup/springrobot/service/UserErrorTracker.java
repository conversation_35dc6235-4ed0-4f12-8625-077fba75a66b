package com.stepup.springrobot.service;

import com.stepup.springrobot.dto.monitoring.MonitoringLogDTO;
import com.stepup.springrobot.dto.monitoring.UserErrorDTO;
import com.stepup.springrobot.model.DatadogLogType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserErrorTracker {

    @Autowired
    private DatadogService datadogService;

    @Autowired
    private TraceContextService traceContextService;

    @Autowired
    private UserJourneyTracker userJourneyTracker;

    // Store user errors by user ID
    private final Map<String, List<UserErrorDTO>> userErrors = new ConcurrentHashMap<>();

    // Store error patterns and frequencies
    private final Map<String, ErrorPattern> errorPatterns = new ConcurrentHashMap<>();

    // Store active error resolution attempts
    private final Map<String, ErrorResolutionContext> activeResolutions = new ConcurrentHashMap<>();

    /**
     * Track a user error
     */
    public String trackUserError(String userId, String sessionId, String conversationId, String robotId,
                                 Exception exception, String serviceName, String methodName,
                                 Map<String, Object> contextBeforeError) {
        try {
            String errorId = generateErrorId(userId, exception);

            UserErrorDTO errorDTO = UserErrorDTO.builder()
                    .errorId(errorId)
                    .userId(userId)
                    .sessionId(sessionId)
                    .conversationId(conversationId)
                    .robotId(robotId)
                    .errorType(classifyErrorType(exception))
                    .errorCategory(classifyErrorCategory(exception))
                    .errorSeverity(classifyErrorSeverity(exception))
                    .errorCode(extractErrorCode(exception))
                    .errorMessage(exception.getMessage())
                    .errorDescription(generateErrorDescription(exception))
                    .stackTrace(getStackTrace(exception))
                    .serviceName(serviceName)
                    .methodName(methodName)
                    .className(exception.getStackTrace().length > 0 ?
                            exception.getStackTrace()[0].getClassName() : "unknown")
                    .lineNumber(exception.getStackTrace().length > 0 ?
                            exception.getStackTrace()[0].getLineNumber() : null)
                    .occurrenceTime(Instant.now())
                    .isResolved(false)
                    .userImpact(assessUserImpact(exception, userId))
                    .businessImpact(assessBusinessImpact(exception, userId))
                    .contextBeforeError(contextBeforeError != null ? new HashMap<>(contextBeforeError) : new HashMap<>())
                    .systemState(captureSystemState())
                    .retryAttempts(0)
                    .maxRetryAttempts(getMaxRetryAttempts(exception))
                    .userNotified(false)
                    .tags(generateErrorTags(exception, userId, serviceName))
                    .metadata(new HashMap<>())
                    .build();

            // Store error
            userErrors.computeIfAbsent(userId, k -> new ArrayList<>()).add(errorDTO);

            // Update error patterns
            updateErrorPatterns(errorDTO);

            // Track in user journey if active
            trackErrorInUserJourney(userId, sessionId, errorDTO);

            // Send to Datadog
            sendErrorLogToDatadog(errorDTO);

            // Check if auto-resolution should be attempted
            attemptAutoResolution(errorDTO);

            log.error("User error tracked: userId={}, errorId={}, type={}, message={}",
                    userId, errorId, errorDTO.getErrorType(), exception.getMessage());

            return errorId;

        } catch (Exception e) {
            log.error("Error tracking user error for user: {}", userId, e);
            return null;
        }
    }

    /**
     * Track error resolution
     */
    public void trackErrorResolution(String errorId, String resolutionMethod, boolean success,
                                     String details, Map<String, Object> contextAfterError) {
        try {
            UserErrorDTO error = findErrorById(errorId);
            if (error == null) {
                log.warn("Error not found for resolution: {}", errorId);
                return;
            }

            Instant resolutionTime = Instant.now();
            long resolutionDuration = resolutionTime.toEpochMilli() - error.getOccurrenceTime().toEpochMilli();

            error.setIsResolved(success);
            error.setResolutionTime(resolutionTime);
            error.setResolutionDurationMs(resolutionDuration);
            error.setResolutionMethod(resolutionMethod);
            error.setContextAfterError(contextAfterError);

            // Update metadata
            error.getMetadata().put("resolution_details", details);
            error.getMetadata().put("resolution_success", success);

            // Remove from active resolutions
            activeResolutions.remove(errorId);

            // Send resolution log to Datadog
            sendResolutionLogToDatadog(error);

            log.info("Error resolution tracked: errorId={}, method={}, success={}, duration={}ms",
                    errorId, resolutionMethod, success, resolutionDuration);

        } catch (Exception e) {
            log.error("Error tracking resolution for errorId: {}", errorId, e);
        }
    }

    /**
     * Track retry attempt
     */
    public void trackRetryAttempt(String errorId, int attemptNumber, boolean success, String details) {
        try {
            UserErrorDTO error = findErrorById(errorId);
            if (error == null) return;

            error.setRetryAttempts(attemptNumber);

            Map<String, Object> retryData = new HashMap<>();
            retryData.put("attempt_number", attemptNumber);
            retryData.put("success", success);
            retryData.put("details", details);
            retryData.put("timestamp", Instant.now());

            error.getMetadata().put("retry_attempt_" + attemptNumber, retryData);

            if (success) {
                trackErrorResolution(errorId, "RETRY", true, details, null);
            }

        } catch (Exception e) {
            log.error("Error tracking retry attempt: {}", errorId, e);
        }
    }

    /**
     * Get user error history
     */
    public List<UserErrorDTO> getUserErrorHistory(String userId, int limit) {
        List<UserErrorDTO> errors = userErrors.get(userId);
        if (errors == null) return new ArrayList<>();

        return errors.stream()
                .sorted((a, b) -> b.getOccurrenceTime().compareTo(a.getOccurrenceTime()))
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * Get user error statistics
     */
    public UserErrorStatistics getUserErrorStatistics(String userId, int daysPeriod) {
        List<UserErrorDTO> errors = userErrors.get(userId);
        if (errors == null) return UserErrorStatistics.builder().build();

        Instant cutoff = Instant.now().minusSeconds(daysPeriod * 24 * 60 * 60);
        List<UserErrorDTO> recentErrors = errors.stream()
                .filter(error -> error.getOccurrenceTime().isAfter(cutoff))
                .collect(Collectors.toList());

        return UserErrorStatistics.builder()
                .userId(userId)
                .totalErrors(recentErrors.size())
                .resolvedErrors((int) recentErrors.stream().filter(e -> Boolean.TRUE.equals(e.getIsResolved())).count())
                .unresolvedErrors((int) recentErrors.stream().filter(e -> !Boolean.TRUE.equals(e.getIsResolved())).count())
                .criticalErrors((int) recentErrors.stream().filter(e -> e.getErrorSeverity() == UserErrorDTO.ErrorSeverity.CRITICAL).count())
                .averageResolutionTimeMs(calculateAverageResolutionTime(recentErrors))
                .mostCommonErrorType(findMostCommonErrorType(recentErrors))
                .errorTrends(calculateErrorTrends(recentErrors))
                .affectedFeatures(extractAffectedFeatures(recentErrors))
                .build();
    }

    /**
     * Get error patterns for analysis
     */
    public List<ErrorPattern> getErrorPatterns(int minOccurrences) {
        return errorPatterns.values().stream()
                .filter(pattern -> pattern.getOccurrenceCount() >= minOccurrences)
                .sorted((a, b) -> Integer.compare(b.getOccurrenceCount(), a.getOccurrenceCount()))
                .collect(Collectors.toList());
    }

    /**
     * Classify error type based on exception
     */
    private UserErrorDTO.ErrorType classifyErrorType(Exception exception) {
        String className = exception.getClass().getSimpleName().toLowerCase();
        String message = exception.getMessage() != null ? exception.getMessage().toLowerCase() : "";

        if (className.contains("timeout") || message.contains("timeout")) {
            return UserErrorDTO.ErrorType.TIMEOUT;
        } else if (className.contains("network") || className.contains("connection")) {
            return UserErrorDTO.ErrorType.NETWORK;
        } else if (className.contains("auth")) {
            return UserErrorDTO.ErrorType.AUTHENTICATION;
        } else if (className.contains("validation")) {
            return UserErrorDTO.ErrorType.VALIDATION;
        } else if (className.contains("business") || className.contains("rule")) {
            return UserErrorDTO.ErrorType.BUSINESS;
        } else {
            return UserErrorDTO.ErrorType.TECHNICAL;
        }
    }

    /**
     * Classify error category
     */
    private UserErrorDTO.ErrorCategory classifyErrorCategory(Exception exception) {
        String message = exception.getMessage() != null ? exception.getMessage().toLowerCase() : "";

        if (message.contains("critical") || message.contains("fatal")) {
            return UserErrorDTO.ErrorCategory.CRITICAL;
        } else if (message.contains("error") || exception instanceof RuntimeException) {
            return UserErrorDTO.ErrorCategory.HIGH;
        } else {
            return UserErrorDTO.ErrorCategory.MEDIUM;
        }
    }

    /**
     * Classify error severity
     */
    private UserErrorDTO.ErrorSeverity classifyErrorSeverity(Exception exception) {
        String message = exception.getMessage() != null ? exception.getMessage().toLowerCase() : "";

        if (message.contains("blocker") || message.contains("critical")) {
            return UserErrorDTO.ErrorSeverity.CRITICAL;
        } else if (message.contains("major")) {
            return UserErrorDTO.ErrorSeverity.MAJOR;
        } else if (message.contains("minor")) {
            return UserErrorDTO.ErrorSeverity.MINOR;
        } else {
            return UserErrorDTO.ErrorSeverity.MAJOR; // Default to major
        }
    }

    /**
     * Generate error ID
     */
    private String generateErrorId(String userId, Exception exception) {
        return "error_" + userId + "_" + System.currentTimeMillis() + "_" +
                Integer.toHexString(exception.getClass().getSimpleName().hashCode());
    }

    /**
     * Extract error code from exception
     */
    private String extractErrorCode(Exception exception) {
        // Try to extract error code from message or exception type
        String message = exception.getMessage();
        if (message != null && message.matches(".*\\b[A-Z]{3,}[0-9]{3,}\\b.*")) {
            return message.replaceAll(".*\\b([A-Z]{3,}[0-9]{3,})\\b.*", "$1");
        }
        return exception.getClass().getSimpleName().toUpperCase();
    }

    /**
     * Generate error description
     */
    private String generateErrorDescription(Exception exception) {
        StringBuilder description = new StringBuilder();
        description.append("Error Type: ").append(exception.getClass().getSimpleName()).append("\n");
        description.append("Message: ").append(exception.getMessage()).append("\n");

        if (exception.getCause() != null) {
            description.append("Root Cause: ").append(exception.getCause().getMessage()).append("\n");
        }

        return description.toString();
    }

    /**
     * Get stack trace as string
     */
    private String getStackTrace(Exception exception) {
        StringBuilder sb = new StringBuilder();
        for (StackTraceElement element : exception.getStackTrace()) {
            sb.append(element.toString()).append("\n");
            if (sb.length() > 2000) break; // Limit stack trace size
        }
        return sb.toString();
    }

    /**
     * Assess user impact
     */
    private UserErrorDTO.UserImpact assessUserImpact(Exception exception, String userId) {
        // This would typically involve business logic to assess impact
        return UserErrorDTO.UserImpact.builder()
                .blockedCompletely(isBlockingError(exception))
                .degradedExperience(true)
                .userFrustrationLevel(calculateFrustrationLevel(exception))
                .timeWastedMinutes(estimateTimeWasted(exception))
                .build();
    }

    /**
     * Assess business impact
     */
    private UserErrorDTO.BusinessImpact assessBusinessImpact(Exception exception, String userId) {
        return UserErrorDTO.BusinessImpact.builder()
                .affectedUsersCount(1)
                .slaBreach(isSLABreach(exception))
                .reputationDamage(isReputationDamaging(exception))
                .build();
    }

    // Helper methods for impact assessment
    private boolean isBlockingError(Exception exception) {
        return exception instanceof RuntimeException &&
                exception.getMessage() != null &&
                exception.getMessage().toLowerCase().contains("block");
    }

    private int calculateFrustrationLevel(Exception exception) {
        // Scale 1-10, higher for more critical errors
        if (exception instanceof RuntimeException) return 7;
        return 5;
    }

    private int estimateTimeWasted(Exception exception) {
        // Estimate in minutes
        if (isBlockingError(exception)) return 10;
        return 2;
    }

    private boolean isSLABreach(Exception exception) {
        // Check if this type of error causes SLA breach
        return exception instanceof RuntimeException;
    }

    private boolean isReputationDamaging(Exception exception) {
        // Check if visible to user
        return exception.getMessage() != null &&
                !exception.getMessage().toLowerCase().contains("internal");
    }

    private Map<String, Object> captureSystemState() {
        Map<String, Object> state = new HashMap<>();
        state.put("timestamp", Instant.now());
        state.put("memory_usage", Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory());
        state.put("free_memory", Runtime.getRuntime().freeMemory());
        state.put("active_threads", Thread.activeCount());
        return state;
    }

    private int getMaxRetryAttempts(Exception exception) {
        // Different retry strategies based on error type
        if (exception.getMessage() != null && exception.getMessage().contains("timeout")) {
            return 3;
        }
        return 1;
    }

    private List<String> generateErrorTags(Exception exception, String userId, String serviceName) {
        List<String> tags = new ArrayList<>();
        tags.add("user:" + userId);
        tags.add("service:" + serviceName);
        tags.add("error_type:" + exception.getClass().getSimpleName());
        return tags;
    }

    private UserErrorDTO findErrorById(String errorId) {
        return userErrors.values().stream()
                .flatMap(List::stream)
                .filter(error -> errorId.equals(error.getErrorId()))
                .findFirst()
                .orElse(null);
    }

    private void updateErrorPatterns(UserErrorDTO errorDTO) {
        String patternKey = errorDTO.getErrorType() + "_" + errorDTO.getServiceName();
        ErrorPattern pattern = errorPatterns.computeIfAbsent(patternKey, k -> new ErrorPattern(patternKey));
        pattern.addOccurrence(errorDTO);
    }

    private void trackErrorInUserJourney(String userId, String sessionId, UserErrorDTO errorDTO) {
        try {
            UserJourneyTracker.UserJourneyContext activeJourney =
                    userJourneyTracker.getActiveJourney(userId, sessionId);

            if (activeJourney != null) {
                userJourneyTracker.trackJourneyStage(
                        activeJourney.getJourneyId(),
                        com.stepup.springrobot.dto.monitoring.UserJourneyDTO.JourneyStage.ERROR_OCCURRED,
                        "ERROR",
                        null,
                        errorDTO,
                        errorDTO.getErrorMessage()
                );
            }
        } catch (Exception e) {
            log.error("Error tracking error in user journey: {}", e.getMessage());
        }
    }

    private void attemptAutoResolution(UserErrorDTO errorDTO) {
        // Implement auto-resolution logic based on error type
        if (shouldAttemptAutoResolution(errorDTO)) {
            ErrorResolutionContext context = new ErrorResolutionContext(errorDTO);
            activeResolutions.put(errorDTO.getErrorId(), context);

            // Trigger async resolution attempt
            attemptResolution(errorDTO);
        }
    }

    private boolean shouldAttemptAutoResolution(UserErrorDTO errorDTO) {
        return errorDTO.getErrorType() == UserErrorDTO.ErrorType.TIMEOUT ||
                errorDTO.getErrorType() == UserErrorDTO.ErrorType.NETWORK;
    }

    @Async
    protected void attemptResolution(UserErrorDTO errorDTO) {
        // Implement specific resolution strategies
        log.info("Attempting auto-resolution for error: {}", errorDTO.getErrorId());
    }

    @Async
    protected void sendErrorLogToDatadog(UserErrorDTO errorDTO) {
        try {
            MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                    .logType(DatadogLogType.USER_ERROR_TRACKING)
                    .message(String.format("User Error: %s - %s - %s",
                            errorDTO.getUserId(),
                            errorDTO.getErrorType().getTypeName(),
                            errorDTO.getErrorMessage()))
                    .level("ERROR")
                    .timestamp(errorDTO.getOccurrenceTime())
                    .serviceName("user-error-tracker")
                    .traceContext(traceContextService.getTraceContext())
                    .errorMessage(errorDTO.getErrorMessage())
                    .metadata(createErrorMetadata(errorDTO))
                    .build();

            datadogService.sendLogToDatadog(monitoringLog);

        } catch (Exception e) {
            log.error("Error sending error log to Datadog: {}", e.getMessage(), e);
        }
    }

    @Async
    protected void sendResolutionLogToDatadog(UserErrorDTO errorDTO) {
        try {
            MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                    .logType(DatadogLogType.USER_ERROR_RESOLUTION)
                    .message(String.format("Error Resolution: %s - %s - %s",
                            errorDTO.getErrorId(),
                            errorDTO.getResolutionMethod(),
                            errorDTO.getIsResolved() ? "SUCCESS" : "FAILED"))
                    .level("INFO")
                    .timestamp(errorDTO.getResolutionTime())
                    .serviceName("user-error-tracker")
                    .traceContext(traceContextService.getTraceContext())
                    .durationMs(errorDTO.getResolutionDurationMs())
                    .metadata(createResolutionMetadata(errorDTO))
                    .build();

            datadogService.sendLogToDatadog(monitoringLog);

        } catch (Exception e) {
            log.error("Error sending resolution log to Datadog: {}", e.getMessage(), e);
        }
    }

    private Map<String, Object> createErrorMetadata(UserErrorDTO errorDTO) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("error_id", errorDTO.getErrorId());
        metadata.put("user_id", errorDTO.getUserId());
        metadata.put("session_id", errorDTO.getSessionId());
        metadata.put("error_type", errorDTO.getErrorType().getTypeName());
        metadata.put("error_category", errorDTO.getErrorCategory().getCategoryName());
        metadata.put("error_severity", errorDTO.getErrorSeverity().getSeverityName());
        metadata.put("service_name", errorDTO.getServiceName());
        metadata.put("method_name", errorDTO.getMethodName());
        metadata.put("retry_attempts", errorDTO.getRetryAttempts());
        metadata.put("tags", errorDTO.getTags());
        return metadata;
    }

    private Map<String, Object> createResolutionMetadata(UserErrorDTO errorDTO) {
        Map<String, Object> metadata = createErrorMetadata(errorDTO);
        metadata.put("resolution_method", errorDTO.getResolutionMethod());
        metadata.put("resolution_duration_ms", errorDTO.getResolutionDurationMs());
        metadata.put("resolution_success", errorDTO.getIsResolved());
        return metadata;
    }

    // Helper methods for statistics
    private long calculateAverageResolutionTime(List<UserErrorDTO> errors) {
        return (long) errors.stream()
                .filter(e -> Boolean.TRUE.equals(e.getIsResolved()) && e.getResolutionDurationMs() != null)
                .mapToLong(UserErrorDTO::getResolutionDurationMs)
                .average()
                .orElse(0.0);
    }

    private String findMostCommonErrorType(List<UserErrorDTO> errors) {
        return errors.stream()
                .collect(Collectors.groupingBy(UserErrorDTO::getErrorType, Collectors.counting()))
                .entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(entry -> entry.getKey().getTypeName())
                .orElse("unknown");
    }

    private Map<String, Integer> calculateErrorTrends(List<UserErrorDTO> errors) {
        // Calculate daily error counts for trend analysis
        return errors.stream()
                .collect(Collectors.groupingBy(
                        error -> error.getOccurrenceTime().toString().substring(0, 10), // Date part
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
    }

    private List<String> extractAffectedFeatures(List<UserErrorDTO> errors) {
        return errors.stream()
                .map(UserErrorDTO::getServiceName)
                .distinct()
                .collect(Collectors.toList());
    }

    // Inner classes for data structures
    @lombok.Data
    @lombok.Builder
    public static class UserErrorStatistics {
        private String userId;
        private int totalErrors;
        private int resolvedErrors;
        private int unresolvedErrors;
        private int criticalErrors;
        private long averageResolutionTimeMs;
        private String mostCommonErrorType;
        private Map<String, Integer> errorTrends;
        private List<String> affectedFeatures;
    }

    @lombok.Data
    public static class ErrorPattern {
        private String patternKey;
        private int occurrenceCount;
        private Instant firstOccurrence;
        private Instant lastOccurrence;
        private List<UserErrorDTO> samples;

        public ErrorPattern(String patternKey) {
            this.patternKey = patternKey;
            this.occurrenceCount = 0;
            this.samples = new ArrayList<>();
        }

        public void addOccurrence(UserErrorDTO error) {
            this.occurrenceCount++;
            if (this.firstOccurrence == null) {
                this.firstOccurrence = error.getOccurrenceTime();
            }
            this.lastOccurrence = error.getOccurrenceTime();

            // Keep only recent samples
            this.samples.add(error);
            if (this.samples.size() > 10) {
                this.samples.remove(0);
            }
        }
    }

    @lombok.Data
    public static class ErrorResolutionContext {
        private UserErrorDTO error;
        private Instant resolutionStartTime;
        private List<String> resolutionAttempts;

        public ErrorResolutionContext(UserErrorDTO error) {
            this.error = error;
            this.resolutionStartTime = Instant.now();
            this.resolutionAttempts = new ArrayList<>();
        }
    }
}
