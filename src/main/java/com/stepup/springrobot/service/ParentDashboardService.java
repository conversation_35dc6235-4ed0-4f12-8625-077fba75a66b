package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.dto.notification.PushNotificationReqDTO;
import com.stepup.springrobot.model.notification.ParentNotificationType;
import com.stepup.springrobot.repository.chat.RobotUserConversationRepository;
import com.stepup.springrobot.security.JwtService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Service for sending parent dashboard notifications
 * Handles various types of notifications related to child's learning activities
 */
@Service
@Slf4j
public class ParentDashboardService extends CommonService {

    private final NotificationService notificationService;
    private final RobotUserConversationRepository robotUserConversationRepository;

    @Autowired
    protected ParentDashboardService(ObjectMapper objectMapper,
                                     UploadFileToS3 uploadFileToS3,
                                     JwtService jwtService,
                                     SlackWarningSystemService slackWarningSystemService,
                                     NotificationService notificationService,
                                     RobotUserConversationRepository robotUserConversationRepository) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
        this.notificationService = notificationService;
        this.robotUserConversationRepository = robotUserConversationRepository;
    }

    /**
     * Create data map with notification type information for frontend
     * @param notificationType Type of notification for frontend logic
     * @return Map containing notification type data
     */
    private Map<String, String> createNotificationData(ParentNotificationType notificationType) {
        Map<String, String> data = new HashMap<>();
        data.put("notification_type", notificationType.getType());
        return data;
    }

    /**
     * Send notification when it's time for child's study session
     * This is called 15 minutes after the alarm schedule time if child hasn't started learning
     *
     * @param userId   User ID of the parent
     * @param lessonId
     */
    public void sendChildNotStartedLearning(String userId, String lessonId) {
        try {
            String title = "🚨 Hôm nay Pika chưa thấy bạn nhỏ vào học!";
            String message = "Con vẫn chưa vào học cùng Pika. Bố mẹ giúp Pika nhắc bé hoàn thành bài để không bỏ lỡ hành trình khám phá tiếng Anh nhé! 🌍✨";
            Map<String, String> data = createNotificationData(ParentNotificationType.CHILD_NOT_STARTED_LEARNING);

            if (lessonId != null && !lessonId.trim().isEmpty()) {
                data.put("lesson_id", lessonId);
            }

            PushNotificationReqDTO notification = PushNotificationReqDTO.builder()
                    .userId(userId)
                    .title(title)
                    .message(message)
                    .data(data)
                    .build();

            notificationService.sendNotificationToUserId(notification);
        } catch (Exception e) {
            log.error("Error sending study time reminder notification to user: {} for child: {}", userId, e);
        }
    }

    /**
     * Send notification when child hasn't started learning today
     * @param userId User ID of the parent
     */
    public void sendStudyTimeReminder(String userId) {
        try {
            String title = "📚 Đã đến giờ học của con!";
            String message = "Pika đang chờ con để cùng khám phá bài học hôm nay. Bố mẹ nhắc bé giúp Pika nha! 🚀";
            Map<String, String> data = createNotificationData(ParentNotificationType.STUDY_TIME_REMINDER);

            PushNotificationReqDTO notification = PushNotificationReqDTO.builder()
                    .userId(userId)
                    .title(title)
                    .message(message)
                    .data(data)
                    .build();

            notificationService.sendNotificationToUserId(notification);
            log.info("Sent child not started learning notification to user: {}", userId);
        } catch (Exception e) {
            log.error("Error sending child not started learning notification to user: {}", userId, e);
        }
    }

    /**
     * Send notification when child has completed a lesson
     * Only sends if total study time today > 30 minutes
     * @param userId User ID of the parent
     * @param lessonId ID of the lesson to include in notification data
     */
    public void sendLessonCompletedNotification(String userId, String lessonId) {
        try {
            // Check if total study time today > 30 minutes
            Double totalStudyMinutes = robotUserConversationRepository.getTotalStudyTimeMinutesToday(userId);
            if (totalStudyMinutes == null || totalStudyMinutes <= 30.0) {
                log.info("Not sending lesson completed notification to user: {} - total study time: {} minutes (< 30 minutes)", userId, totalStudyMinutes);
                return;
            }

            String title = "🌟 Bé vừa hoàn thành buổi học cùng Pika!";
            String message = "Pika và bạn nhỏ vừa kết thúc buổi học trọn vẹn hôm nay. Bố mẹ có muốn xem hôm nay con đã nói chuyện gì với Pika không ạ? 💙";
            Map<String, String> data = createNotificationData(ParentNotificationType.LESSON_COMPLETED);

            // Add lesson ID to data if provided
            if (lessonId != null && !lessonId.trim().isEmpty()) {
                data.put("lesson_id", lessonId);
            }

            PushNotificationReqDTO notification = PushNotificationReqDTO.builder()
                    .userId(userId)
                    .title(title)
                    .message(message)
                    .data(data)
                    .build();

            notificationService.sendNotificationToUserId(notification);
            log.info("Sent lesson completed notification to user: {} - total study time: {} minutes", userId, totalStudyMinutes);
        } catch (Exception e) {
            log.error("Error sending lesson completed notification to user: {}", userId, e);
        }
    }

    /**
     * Schedule lesson completed notification 5 minutes after conversation ends
     * This should be called when a conversation/lesson ends
     * @param userId User ID of the parent
     */
    public void scheduleLessonCompletedNotification(String userId) {
        // Use a simple timer to schedule notification after 5 minutes
        // In production, you might want to use Spring's @Scheduled or a proper job scheduler
        Thread scheduledNotification = new Thread(() -> {
            try {
                Thread.sleep(5 * 60 * 1000); // Wait 5 minutes
                sendLessonCompletedNotification(userId, null);
            } catch (InterruptedException e) {
                log.error("Lesson completed notification scheduling interrupted for user: {}", userId, e);
                Thread.currentThread().interrupt();
            }
        });
        scheduledNotification.setDaemon(true);
        scheduledNotification.start();

        log.info("Scheduled lesson completed notification for user: {} in 5 minutes", userId);
    }

    /**
     * Send notification when lesson is incomplete
     * @param userId User ID of the parent
     */
    public void sendIncompleteLessonNotification(String userId) {
        try {
            String title = "⚡ Bài học hôm nay vẫn đang dang dở!";
            String message = "Có vẻ con đã tạm dừng giữa chừng. Bố mẹ nhắc bé quay lại hoàn thành bài học nhé – Pika vẫn đang chờ bé! 😄";
            Map<String, String> data = createNotificationData(ParentNotificationType.INCOMPLETE_LESSON);

            PushNotificationReqDTO notification = PushNotificationReqDTO.builder()
                    .userId(userId)
                    .title(title)
                    .message(message)
                    .data(data)
                    .build();

            notificationService.sendNotificationToUserId(notification);
            log.info("Sent incomplete lesson notification to user: {}", userId);
        } catch (Exception e) {
            log.error("Error sending incomplete lesson notification to user: {}", userId, e);
        }
    }

    /**
     * Send notification about pronunciation issues
     * @param userId User ID of the parent
     * @param lessonId ID of the lesson to include in notification data
     */
    public void sendPronunciationIssueNotification(String userId, String lessonId) {
        try {
            String title = "🎧 Hôm nay phát âm của con hơi... vấp váp xíu!";
            String message = "Phát âm của con hôm nay chưa rõ lắm. Bố mẹ cùng Pika cổ vũ bé \"nói lại cho chuẩn\" nha! 💬✨";
            Map<String, String> data = createNotificationData(ParentNotificationType.PRONUNCIATION_ISSUE);

            // Add lesson ID to data if provided
            if (lessonId != null && !lessonId.trim().isEmpty()) {
                data.put("lesson_id", lessonId);
            }

            PushNotificationReqDTO notification = PushNotificationReqDTO.builder()
                    .userId(userId)
                    .title(title)
                    .message(message)
                    .data(data)
                    .build();

            notificationService.sendNotificationToUserId(notification);
            log.info("Sent pronunciation issue notification to user: {}", userId);
        } catch (Exception e) {
            log.error("Error sending pronunciation issue notification to user: {}", userId, e);
        }
    }

    /**
     * Send notification when child hasn't used app for 3 days
     * @param userId User ID of the parent
     */
    public void sendThreeDaysAbsenceNotification(String userId) {
        try {
            String title = "📭 Pika chưa gặp bạn nhỏ suốt 3 ngày rồi đó…";
            String message = "Pika lo con đang gặp chút trở ngại! Bố mẹ hãy cổ vũ bé quay lại cùng Pika nhé! 💙";
            Map<String, String> data = createNotificationData(ParentNotificationType.THREE_DAYS_ABSENCE);

            PushNotificationReqDTO notification = PushNotificationReqDTO.builder()
                    .userId(userId)
                    .title(title)
                    .message(message)
                    .data(data)
                    .build();

            notificationService.sendNotificationToUserId(notification);
            log.info("Sent three days absence notification to user: {}", userId);
        } catch (Exception e) {
            log.error("Error sending three days absence notification to user: {}", userId, e);
        }
    }

    /**
     * Send weekly study report notification
     * @param userId User ID of the parent
     */
    public void sendWeeklyStudyReport(String userId) {
        try {
            String title = "📊 Pika gửi báo cáo học tập tuần của bé!";
            String message = "Bố mẹ có muốn xem hành trình học của con –  trong tuần này đã học gì, tiến bộ ra sao không? Pika đã chuẩn bị bản tóm tắt đáng yêu rồi đó! 📝✨";
            Map<String, String> data = createNotificationData(ParentNotificationType.WEEKLY_STUDY_REPORT);

            PushNotificationReqDTO notification = PushNotificationReqDTO.builder()
                    .userId(userId)
                    .title(title)
                    .message(message)
                    .data(data)
                    .build();

            notificationService.sendNotificationToUserId(notification);
            log.info("Sent weekly study report notification to user: {}", userId);
        } catch (Exception e) {
            log.error("Error sending weekly study report notification to user: {}", userId, e);
        }
    }

    /**
     * Send notification for general study reminders
     * @param userId User ID of the parent  
     * @param customMessage Custom message for the notification
     * @param lessonId ID of the lesson to include in notification data
     */
    public void sendCustomStudyReminder(String userId, String title, String customMessage, String lessonId) {
        try {
            title = title == null ? "Nhắc nhở học tập" : title;
            Map<String, String> data = createNotificationData(ParentNotificationType.CUSTOM_STUDY_REMINDER);

            // Add lesson ID to data if provided
            if (lessonId != null && !lessonId.trim().isEmpty()) {
                data.put("lesson_id", lessonId);
            }

            PushNotificationReqDTO notification = PushNotificationReqDTO.builder()
                    .userId(userId)
                    .title(title)
                    .message(customMessage)
                    .data(data)
                    .build();

            notificationService.sendNotificationToUserId(notification);
            log.info("Sent custom study reminder notification to user: {} with lesson: {}", userId, lessonId);
        } catch (Exception e) {
            log.error("Error sending custom study reminder notification to user: {}", userId, e);
        }
    }
} 