package com.stepup.springrobot.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.dto.monitoring.ConversationFlowDTO;
import com.stepup.springrobot.dto.monitoring.MonitoringLogDTO;
import com.stepup.springrobot.dto.monitoring.TraceContextDTO;
import com.stepup.springrobot.model.DatadogLogType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class ConversationFlowTracker {

    @Autowired
    private DatadogService datadogService;

    @Autowired
    private TraceContextService traceContextService;

    @Autowired
    private ObjectMapper objectMapper;

    // Store active conversation flows
    private final Map<String, ConversationFlowContext> activeConversations = new ConcurrentHashMap<>();

    public void startConversationFlow(String conversationId, String sessionId, String userId, String robotId) {
        ConversationFlowContext context = ConversationFlowContext.builder()
                .conversationId(conversationId)
                .sessionId(sessionId)
                .userId(userId)
                .robotId(robotId)
                .startTime(Instant.now())
                .traceContext(traceContextService.getTraceContext())
                .stageTimings(new ConcurrentHashMap<>())
                .build();

        activeConversations.put(conversationId, context);

        logConversationStage(conversationId, ConversationFlowDTO.ConversationFlowStage.WEBSOCKET_CONNECTION,
                null, null, "SUCCESS", null);
    }

    public void trackAudioReceived(String conversationId, int audioSizeBytes, String audioFormat) {
        ConversationFlowContext context = activeConversations.get(conversationId);
        if (context == null) return;

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("audio_size_bytes", audioSizeBytes);
        metadata.put("audio_format", audioFormat);

        logConversationStage(conversationId, ConversationFlowDTO.ConversationFlowStage.AUDIO_RECEIVED,
                null, null, "SUCCESS", metadata);
    }

    public void trackAudioUploadStart(String conversationId, String fileName) {
        startStage(conversationId, ConversationFlowDTO.ConversationFlowStage.AUDIO_UPLOAD_S3);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("file_name", fileName);

        logConversationStage(conversationId, ConversationFlowDTO.ConversationFlowStage.AUDIO_UPLOAD_S3,
                null, null, "STARTED", metadata);
    }

    public void trackAudioUploadEnd(String conversationId, String audioUrl, long durationMs) {
        endStage(conversationId, ConversationFlowDTO.ConversationFlowStage.AUDIO_UPLOAD_S3);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("audio_url", audioUrl);
        metadata.put("duration_ms", durationMs);

        logConversationStage(conversationId, ConversationFlowDTO.ConversationFlowStage.AUDIO_UPLOAD_S3,
                null, null, "SUCCESS", metadata);
    }

    public void trackASRStart(String conversationId, String asrProvider, String serviceEndpoint) {
        ConversationFlowDTO.ConversationFlowStage stage;
        switch (asrProvider.toUpperCase()) {
            case "GOOGLE":
                stage = ConversationFlowDTO.ConversationFlowStage.ASR_GOOGLE_CALL;
                break;
            case "GRPC":
                stage = ConversationFlowDTO.ConversationFlowStage.ASR_GRPC_CALL;
                break;
            default:
                stage = ConversationFlowDTO.ConversationFlowStage.ASR_INTERNAL_CALL;
                break;
        }

        startStage(conversationId, stage);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("asr_provider", asrProvider);
        metadata.put("service_endpoint", serviceEndpoint);

        logConversationStage(conversationId, stage, null, null, "STARTED", metadata);
    }

    public void trackASREnd(String conversationId, String asrProvider, String transcript,
                            Double confidence, String language, long durationMs, boolean success, String errorMessage) {
        ConversationFlowDTO.ConversationFlowStage stage;
        switch (asrProvider.toUpperCase()) {
            case "GOOGLE":
                stage = ConversationFlowDTO.ConversationFlowStage.ASR_GOOGLE_CALL;
                break;
            case "GRPC":
                stage = ConversationFlowDTO.ConversationFlowStage.ASR_GRPC_CALL;
                break;
            default:
                stage = ConversationFlowDTO.ConversationFlowStage.ASR_INTERNAL_CALL;
                break;
        }

        endStage(conversationId, stage);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("asr_provider", asrProvider);
        metadata.put("transcript_text", transcript);
        metadata.put("confidence_score", confidence);
        metadata.put("language_detected", language);
        metadata.put("duration_ms", durationMs);

        JsonNode outputData = null;
        try {
            outputData = objectMapper.createObjectNode()
                    .put("transcript", transcript)
                    .put("confidence", confidence != null ? confidence : 0.0)
                    .put("language", language);
        } catch (Exception e) {
            log.error("Error creating ASR output data", e);
        }

        logConversationStage(conversationId, stage, null, outputData,
                success ? "SUCCESS" : "ERROR", metadata, errorMessage);
    }

    public void trackLLMStart(String conversationId, String llmCallType, String userMessage, String audioUrl) {
        ConversationFlowDTO.ConversationFlowStage stage = "ACTION".equals(llmCallType)
                ? ConversationFlowDTO.ConversationFlowStage.LLM_ACTION_CALL
                : ConversationFlowDTO.ConversationFlowStage.LLM_CONVERSATION_CALL;

        startStage(conversationId, stage);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("llm_call_type", llmCallType);
        metadata.put("user_message", userMessage);
        metadata.put("audio_url", audioUrl);

        JsonNode inputData = null;
        try {
            inputData = objectMapper.createObjectNode()
                    .put("message", userMessage)
                    .put("audioUrl", audioUrl)
                    .put("callType", llmCallType);
        } catch (Exception e) {
            log.error("Error creating LLM input data", e);
        }

        logConversationStage(conversationId, stage, inputData, null, "STARTED", metadata);
    }

    public void trackLLMEnd(String conversationId, String llmCallType, JsonNode response,
                            Integer promptTokens, Integer completionTokens, long durationMs,
                            boolean success, String errorMessage) {
        ConversationFlowDTO.ConversationFlowStage stage = "ACTION".equals(llmCallType)
                ? ConversationFlowDTO.ConversationFlowStage.LLM_ACTION_CALL
                : ConversationFlowDTO.ConversationFlowStage.LLM_CONVERSATION_CALL;

        endStage(conversationId, stage);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("llm_call_type", llmCallType);
        metadata.put("prompt_tokens", promptTokens);
        metadata.put("completion_tokens", completionTokens);
        metadata.put("total_tokens", promptTokens != null && completionTokens != null ? promptTokens + completionTokens : null);
        metadata.put("duration_ms", durationMs);

        logConversationStage(conversationId, stage, null, response,
                success ? "SUCCESS" : "ERROR", metadata, errorMessage);
    }

    public void trackTTSStart(String conversationId, String text, String voice, Double speed, Integer volume) {
        startStage(conversationId, ConversationFlowDTO.ConversationFlowStage.TTS_CONVERSION);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("text_to_convert", text);
        metadata.put("tts_voice", voice);
        metadata.put("tts_speed", speed);
        metadata.put("tts_volume", volume);

        JsonNode inputData = null;
        try {
            inputData = objectMapper.createObjectNode()
                    .put("text", text)
                    .put("voice", voice)
                    .put("speed", speed != null ? speed : 1.0)
                    .put("volume", volume != null ? volume : 100);
        } catch (Exception e) {
            log.error("Error creating TTS input data", e);
        }

        logConversationStage(conversationId, ConversationFlowDTO.ConversationFlowStage.TTS_CONVERSION,
                inputData, null, "STARTED", metadata);
    }

    public void trackTTSEnd(String conversationId, String audioUrl, long durationMs, boolean success, String errorMessage) {
        endStage(conversationId, ConversationFlowDTO.ConversationFlowStage.TTS_CONVERSION);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("generated_audio_url", audioUrl);
        metadata.put("duration_ms", durationMs);

        JsonNode outputData = null;
        try {
            outputData = objectMapper.createObjectNode()
                    .put("audioUrl", audioUrl);
        } catch (Exception e) {
            log.error("Error creating TTS output data", e);
        }

        logConversationStage(conversationId, ConversationFlowDTO.ConversationFlowStage.TTS_CONVERSION,
                null, outputData, success ? "SUCCESS" : "ERROR", metadata, errorMessage);
    }

    public void trackAnimationStart(String conversationId, String emotionType) {
        startStage(conversationId, ConversationFlowDTO.ConversationFlowStage.ANIMATION_GENERATION);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("emotion_type", emotionType);

        logConversationStage(conversationId, ConversationFlowDTO.ConversationFlowStage.ANIMATION_GENERATION,
                null, null, "STARTED", metadata);
    }

    public void trackAnimationEnd(String conversationId, String emotionType, List<String> servoActions,
                                  JsonNode animationData, long durationMs, boolean success, String errorMessage) {
        endStage(conversationId, ConversationFlowDTO.ConversationFlowStage.ANIMATION_GENERATION);

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("emotion_type", emotionType);
        metadata.put("servo_actions", servoActions);
        metadata.put("duration_ms", durationMs);

        logConversationStage(conversationId, ConversationFlowDTO.ConversationFlowStage.ANIMATION_GENERATION,
                null, animationData, success ? "SUCCESS" : "ERROR", metadata, errorMessage);
    }

    public void trackResponseSent(String conversationId, JsonNode responseData, int responseSize) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("response_size_bytes", responseSize);

        logConversationStage(conversationId, ConversationFlowDTO.ConversationFlowStage.RESPONSE_SENT,
                null, responseData, "SUCCESS", metadata);
    }

    public void endConversationFlow(String conversationId, boolean success, String errorMessage) {
        ConversationFlowContext context = activeConversations.get(conversationId);
        if (context == null) return;

        long totalDuration = Instant.now().toEpochMilli() - context.getStartTime().toEpochMilli();

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("total_duration_ms", totalDuration);
        metadata.put("stage_count", context.getStageTimings().size());

        logConversationStage(conversationId, ConversationFlowDTO.ConversationFlowStage.CONVERSATION_END,
                null, null, success ? "SUCCESS" : "ERROR", metadata, errorMessage);

        // Clean up
        activeConversations.remove(conversationId);
    }

    private void startStage(String conversationId, ConversationFlowDTO.ConversationFlowStage stage) {
        ConversationFlowContext context = activeConversations.get(conversationId);
        if (context != null) {
            context.getStageTimings().put(stage, Instant.now());
        }
    }

    private void endStage(String conversationId, ConversationFlowDTO.ConversationFlowStage stage) {
        ConversationFlowContext context = activeConversations.get(conversationId);
        if (context != null && context.getStageTimings().containsKey(stage)) {
            // Remove the stage timing as it's completed
            // The duration will be calculated in logConversationStage
            context.getStageTimings().remove(stage);
        }
    }

    private void logConversationStage(String conversationId, ConversationFlowDTO.ConversationFlowStage stage,
                                      JsonNode inputData, JsonNode outputData, String status,
                                      Map<String, Object> metadata) {
        logConversationStage(conversationId, stage, inputData, outputData, status, metadata, null);
    }

    private void logConversationStage(String conversationId, ConversationFlowDTO.ConversationFlowStage stage,
                                      JsonNode inputData, JsonNode outputData, String status,
                                      Map<String, Object> metadata, String errorMessage) {
        try {
            ConversationFlowContext context = activeConversations.get(conversationId);
            if (context == null) return;

            Instant now = Instant.now();
            Instant stageStart = context.getStageTimings().get(stage);
            Long durationMs = stageStart != null ? now.toEpochMilli() - stageStart.toEpochMilli() : null;

            ConversationFlowDTO flowDTO = ConversationFlowDTO.builder()
                    .conversationId(conversationId)
                    .sessionId(context.getSessionId())
                    .userId(context.getUserId())
                    .robotId(context.getRobotId())
                    .flowStage(stage)
                    .stageName(stage.getStageName())
                    .startTime(stageStart != null ? stageStart : now)
                    .endTime(now)
                    .durationMs(durationMs)
                    .status(status)
                    .inputData(inputData)
                    .outputData(outputData)
                    .errorMessage(errorMessage)
                    .metadata(metadata != null ? metadata : new HashMap<>())
                    .build();

            // Convert to MonitoringLogDTO for Datadog
            MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                    .logType(DatadogLogType.CONVERSATION_SERVER_RESPONSE) // We can add more specific types
                    .message(String.format("Conversation stage: %s - %s", stage.getStageName(), status))
                    .level("ERROR".equals(status) ? "ERROR" : "INFO")
                    .timestamp(now)
                    .serviceName("spring-robot")
                    .traceContext(context.getTraceContext())
                    .durationMs(durationMs)
                    .errorMessage(errorMessage)
                    .requestData(inputData)
                    .responseData(outputData)
                    .metadata(metadata != null ? new HashMap<>(metadata) : new HashMap<>())
                    .build();

            // Add conversation flow specific metadata
            if (monitoringLog.getMetadata() == null) {
                monitoringLog.setMetadata(new HashMap<>());
            }
            monitoringLog.getMetadata().put("conversation_flow_stage", stage.getStageName());
            monitoringLog.getMetadata().put("conversation_id", conversationId);
            monitoringLog.getMetadata().put("session_id", context.getSessionId());

            // Add flowDTO data to metadata for detailed tracking
            try {
                monitoringLog.getMetadata().put("flow_stage_data", objectMapper.writeValueAsString(flowDTO));
            } catch (Exception e) {
                log.error("Error serializing flowDTO: {}", e.getMessage());
            }

            datadogService.sendLogToDatadog(monitoringLog);

        } catch (Exception e) {
            log.error("Error logging conversation stage: {}", e.getMessage(), e);
        }
    }

    @lombok.Data
    @lombok.Builder
    private static class ConversationFlowContext {
        private String conversationId;
        private String sessionId;
        private String userId;
        private String robotId;
        private Instant startTime;
        private TraceContextDTO traceContext;
        private Map<ConversationFlowDTO.ConversationFlowStage, Instant> stageTimings;
    }
}
