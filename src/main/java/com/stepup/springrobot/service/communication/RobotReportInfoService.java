package com.stepup.springrobot.service.communication;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.communication.RobotErrorLogReqDTO;
import com.stepup.springrobot.model.robot.RobotReportInfo;
import com.stepup.springrobot.repository.robot.RobotReportInfoRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class RobotReportInfoService {
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RobotReportInfoRepository robotReportInfoRepository;

    public DataResponseDTO<?> saveRobotErrorLog(RobotErrorLogReqDTO reqDTO) {
        RobotReportInfo robotReportInfo = RobotReportInfo.builder()
                .robotId(reqDTO.getRobotId())
                .infoType(reqDTO.getInfoType())
                .screen(reqDTO.getScreen())
                .firmwareVersion(reqDTO.getFirmwareVersion())
                .msgDetail(reqDTO.getMsgDetail())
                .build();

        robotReportInfoRepository.save(robotReportInfo);
        return new DataResponseDTO<>(CodeDefine.OK, "Lưu error log robot thành công");
    }

    public Page<RobotReportInfo> getErrorLogs(String robotId, java.util.Date start, java.util.Date end, Pageable pageable) {
        boolean noRobot = (robotId == null || robotId.isBlank());
        if (noRobot && start == null && end == null) {
            return robotReportInfoRepository.findAllByOrderByCreatedAtDesc(pageable);
        }
        if (noRobot) {
            if (start != null && end != null) {
                return robotReportInfoRepository.findByCreatedAtBetweenOrderByCreatedAtDesc(start, end, pageable);
            } else if (start != null) {
                return robotReportInfoRepository.findByCreatedAtGreaterThanEqualOrderByCreatedAtDesc(start, pageable);
            } else {
                return robotReportInfoRepository.findByCreatedAtLessThanEqualOrderByCreatedAtDesc(end, pageable);
            }
        } else {
            if (start == null && end == null) {
                return robotReportInfoRepository.findByRobotIdOrderByCreatedAtDesc(robotId, pageable);
            } else if (start != null && end != null) {
                return robotReportInfoRepository.findByRobotIdAndCreatedAtBetweenOrderByCreatedAtDesc(robotId, start, end, pageable);
            } else if (start != null) {
                return robotReportInfoRepository.findByRobotIdAndCreatedAtGreaterThanEqualOrderByCreatedAtDesc(robotId, start, pageable);
            } else {
                return robotReportInfoRepository.findByRobotIdAndCreatedAtLessThanEqualOrderByCreatedAtDesc(robotId, end, pageable);
            }
        }
    }
}
