package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.UserDataDTO;
import com.stepup.springrobot.dto.alarm.*;
import com.stepup.springrobot.dto.notification.PushNotificationReqDTO;
import com.stepup.springrobot.exception.business.request.InvalidFormatException;
import com.stepup.springrobot.exception.business.user.UserNotFoundException;
import com.stepup.springrobot.model.InternalTextToSpeechVoice;
import com.stepup.springrobot.model.alarm.*;
import com.stepup.springrobot.model.communication.CommunicationRequestStatusType;
import com.stepup.springrobot.model.robot.RobotUser;
import com.stepup.springrobot.model.user.Profile;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.repository.alarm.AlarmActivityRepository;
import com.stepup.springrobot.repository.alarm.AlarmScheduleExecutionRepository;
import com.stepup.springrobot.repository.alarm.AlarmScheduleRepository;
import com.stepup.springrobot.repository.auth.ProfileRepository;
import com.stepup.springrobot.repository.auth.UserRepository;
import com.stepup.springrobot.repository.chat.RobotUserConversationRepository;
import com.stepup.springrobot.repository.robot.RobotUserRepository;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.communication.ConnectService;
import com.stepup.springrobot.service.scheduler.ChildNotStartedLearningProcessor;
import com.stepup.springrobot.service.scheduler.ScheduledTaskService;
import com.stepup.springrobot.service.scheduler.StudyReminderProcessor;
import com.stepup.springrobot.service.scheduler.TaskTypes;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ResponseStatusException;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AlarmService extends CommonService {
    private final AlarmScheduleRepository alarmScheduleRepository;

    private final AlarmActivityRepository alarmActivityRepository;

    private final RedissonClient redissonClient;

    private final RecognizeService recognizeService;

    private final RobotUserRepository robotUserRepository;

    private final AlarmScheduleExecutionRepository alarmScheduleExecutionRepository;

    private final ProfileRepository profileRepository;

    private final NotificationService notificationService;

    private final ConnectService connectService;

    private final RobotUserConversationRepository robotUserConversationRepository;

    private final ParentDashboardService parentDashboardService;

    private final UserRepository userRepository;

    private final ScheduledExecutorService quickService = Executors.newScheduledThreadPool(20); // Creates a thread pool that reuses fixed number of threads(as specified by noOfThreads in this case).
    private final ScheduledTaskService scheduledTaskService;

    @Autowired
    protected AlarmService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService,
                           SlackWarningSystemService slackWarningSystemService, AlarmScheduleRepository alarmScheduleRepository,
                           AlarmActivityRepository alarmActivityRepository, RedissonClient redissonClient,
                           RecognizeService recognizeService, RobotUserRepository robotUserRepository, ConnectService connectService, AlarmScheduleExecutionRepository alarmScheduleExecutionRepository,
                           ProfileRepository profileRepository,
                           NotificationService notificationService,
                           RobotUserConversationRepository robotUserConversationRepository,
                           ParentDashboardService parentDashboardService, UserRepository userRepository,
                           ScheduledTaskService scheduledTaskService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
        this.alarmScheduleRepository = alarmScheduleRepository;
        this.alarmActivityRepository = alarmActivityRepository;
        this.redissonClient = redissonClient;
        this.recognizeService = recognizeService;
        this.robotUserRepository = robotUserRepository;
        this.connectService = connectService;
        this.alarmScheduleExecutionRepository = alarmScheduleExecutionRepository;
        this.notificationService = notificationService;
        this.profileRepository = profileRepository;
        this.robotUserConversationRepository = robotUserConversationRepository;
        this.parentDashboardService = parentDashboardService;
        this.userRepository = userRepository;
        this.scheduledTaskService = scheduledTaskService;
    }

    /**
     * Get all alarm schedules for the authenticated user
     *
     * @param request HTTP request containing user authentication
     * @return DataResponseDTO containing list of AlarmScheduleResDTO
     */
    public DataResponseDTO<?> getAllSchedules(HttpServletRequest request) {
        UserDataDTO userDataDTO = getUserDataFromRequest(request);
        List<AlarmSchedule> schedules = alarmScheduleRepository.findByUserId(userDataDTO.getUserId());

        // order by updatedAt desc
        schedules.sort(Comparator.comparing(AlarmSchedule::getUpdatedAt).reversed());

        List<AlarmScheduleResDTO> scheduleDTOs = schedules.stream()
                .map(this::convertToResDTO)
                .collect(Collectors.toList());
        return new DataResponseDTO<>(HttpStatus.OK.value(), "Lấy danh sách lịch báo thức thành công", scheduleDTOs);
    }

    private UserDataDTO getUserDataFromRequest(HttpServletRequest request) {
        try {
            return getDataUserHeader(request);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Create a new alarm schedule
     *
     * @param alarmScheduleReqDTO DTO containing alarm schedule data
     * @param request HTTP request containing user authentication
     * @return DataResponseDTO containing created AlarmScheduleResDTO
     */
    public DataResponseDTO<?> createAlarmSchedule(AlarmScheduleReqDTO alarmScheduleReqDTO, HttpServletRequest request) throws JsonProcessingException {
        UserDataDTO userDataDTO = getUserDataFromRequest(request);

        AlarmSchedule alarmSchedule = new AlarmSchedule();
        if (alarmScheduleReqDTO.getTime() != null) {
            if (alarmScheduleReqDTO.getTime().getHour() > 12 || alarmScheduleReqDTO.getTime().getHour() < 1) {
                throw new InvalidFormatException("Invalid time format");
            }

            if (alarmScheduleReqDTO.getDayPart() == AlarmTimeType.PM) {
                alarmSchedule.setTimeHour(alarmScheduleReqDTO.getTime().getHour() + 12);
            } else {
                alarmSchedule.setTimeHour(alarmScheduleReqDTO.getTime().getHour());
            }

            alarmSchedule.setTimeMinute(alarmScheduleReqDTO.getTime().getMinute());
        } else {
            throw new InvalidFormatException("Time is required");
        }

        alarmSchedule.setUserId(userDataDTO.getUserId());
        alarmSchedule.setIsActive(true);

        alarmScheduleReqDTO.getRepeatDays().forEach(day -> {
            if (day == null) throw new InvalidFormatException("Repeat day is required");
        });

        // Check duplicate
        AlarmScheduleConflictDTO conflict = getConflictSchedules(userDataDTO.getUserId(), alarmScheduleReqDTO.getRepeatDays(), alarmSchedule.getTimeHour(), alarmSchedule.getTimeMinute(), null);
        if (conflict != null) {
            return new DataResponseDTO<>(HttpStatus.CONFLICT.value(), "Trùng lịch báo thức, bạn check lại nhé!", conflict);
        }

        List<AlarmDateType> repeatDays = alarmScheduleReqDTO.getRepeatDays();
        if (CollectionUtils.isEmpty(repeatDays)) {
            repeatDays = List.of(AlarmDateType.ONE_TIME);
        }

        alarmSchedule.setDaysActive(objectMapper.writeValueAsString(repeatDays));
        alarmSchedule.setActivityId(alarmScheduleReqDTO.getActivityId());

        AlarmSchedule savedSchedule = alarmScheduleRepository.save(alarmSchedule);
        updateCachedAlarmSchedulesByTime(savedSchedule);
        log.info("Created alarm schedule with ID {} for user {}", savedSchedule.getId(), userDataDTO.getUserId());

        return new DataResponseDTO<>(HttpStatus.OK.value(), "Tạo lịch báo thức thành công", convertToResDTO(savedSchedule));
    }

    /**
     * Update an existing alarm schedule
     *
     * @param id Schedule ID to update
     * @param alarmScheduleReqDTO DTO containing updated alarm schedule data
     * @param request HTTP request containing user authentication
     * @return DataResponseDTO containing updated AlarmScheduleResDTO
     */
    public DataResponseDTO<?> updateAlarmSchedule(Long id, AlarmScheduleReqDTO alarmScheduleReqDTO, HttpServletRequest request) throws JsonProcessingException {
        UserDataDTO userDataDTO = getUserDataFromRequest(request);

        AlarmSchedule existingSchedule = alarmScheduleRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Alarm schedule not found"));

        if (!existingSchedule.getUserId().equals(userDataDTO.getUserId())) {
            throw new ResponseStatusException(HttpStatus.FORBIDDEN, "You don't have permission to update this schedule");
        }

        // Update fields from DTO
        if (alarmScheduleReqDTO.getTime() != null) {
            if (alarmScheduleReqDTO.getTime().getHour() > 12 || alarmScheduleReqDTO.getTime().getHour() < 1) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid time format");
            }

            if (alarmScheduleReqDTO.getDayPart() == AlarmTimeType.PM) {
                existingSchedule.setTimeHour(alarmScheduleReqDTO.getTime().getHour() + 12);
            } else {
                existingSchedule.setTimeHour(alarmScheduleReqDTO.getTime().getHour());
            }

            existingSchedule.setTimeMinute(alarmScheduleReqDTO.getTime().getMinute());
        }

        // Check duplicate
        AlarmScheduleConflictDTO conflict = getConflictSchedules(userDataDTO.getUserId(), alarmScheduleReqDTO.getRepeatDays(), existingSchedule.getTimeHour(), existingSchedule.getTimeMinute(), existingSchedule.getId());
        if (conflict != null) {
            return new DataResponseDTO<>(HttpStatus.CONFLICT.value(), "Trùng lịch báo thức, bạn check lại nhé!", conflict);
        }

        List<AlarmDateType> repeatDays = alarmScheduleReqDTO.getRepeatDays();
        if (CollectionUtils.isEmpty(repeatDays)) {
            repeatDays = List.of(AlarmDateType.ONE_TIME);
        }

        existingSchedule.setDaysActive(objectMapper.writeValueAsString(repeatDays));
        existingSchedule.setActivityId(alarmScheduleReqDTO.getActivityId());
        existingSchedule.setUpdatedAt(new Date());

        AlarmSchedule updatedSchedule = alarmScheduleRepository.save(existingSchedule);
        updateCachedAlarmSchedulesByTime(updatedSchedule);
        log.info("Updated alarm schedule with ID {} for user {}", updatedSchedule.getId(), userDataDTO.getUserId());

        return new DataResponseDTO<>(HttpStatus.OK.value(), "Cập nhật lịch báo thức thành công", convertToResDTO(updatedSchedule));
    }

    /**
     * Delete an alarm schedule
     *
     * @param reqDTO Schedule ID to delete
     * @param request HTTP request containing user authentication
     * @return DataResponseDTO with success message
     */
    public DataResponseDTO<?> deleteAlarmSchedule(AlarmScheduleDeleteReqDTO reqDTO, HttpServletRequest request) {
        UserDataDTO userDataDTO = getUserDataFromRequest(request);
        List<AlarmSchedule> alarmSchedules = alarmScheduleRepository.findByIdInAndUserId(reqDTO.getIds(), userDataDTO.getUserId());
        if (!CollectionUtils.isEmpty(alarmSchedules)) {
            alarmSchedules.forEach(this::updateCachedAlarmSchedulesByTime);
        }

        alarmScheduleRepository.deleteAll(alarmSchedules);
        log.info("Deleted alarm schedule with ID {} for user {}", reqDTO.getIds(), userDataDTO.getUserId());

        return new DataResponseDTO<>(HttpStatus.OK.value(), "Xóa lịch báo thức thành công");
    }

    /**
     * Toggle the active status of an alarm schedule
     *
     * @param id Schedule ID to toggle
     * @param request HTTP request containing user authentication
     * @return DataResponseDTO containing updated AlarmScheduleResDTO
     */
    public DataResponseDTO<?> toggleAlarmScheduleStatus(Long id, HttpServletRequest request) {
        UserDataDTO userDataDTO = getUserDataFromRequest(request);

        AlarmSchedule existingSchedule = alarmScheduleRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Alarm schedule not found"));

        if (!existingSchedule.getUserId().equals(userDataDTO.getUserId())) {
            throw new ResponseStatusException(HttpStatus.FORBIDDEN, "You don't have permission to toggle this schedule");
        }

        existingSchedule.setIsActive(!existingSchedule.getIsActive());
        existingSchedule.setUpdatedAt(new Date());

        AlarmSchedule updatedSchedule = alarmScheduleRepository.save(existingSchedule);
        updateCachedAlarmSchedulesByTime(updatedSchedule);
        log.info("Toggled alarm schedule status with ID {} for user {} to {}",
                updatedSchedule.getId(), userDataDTO.getUserId(), updatedSchedule.getIsActive());

        return new DataResponseDTO<>(HttpStatus.OK.value(), "Cập nhật trạng thái báo thức thành công", convertToResDTO(updatedSchedule));
    }

    private AlarmScheduleConflictDTO getConflictSchedules(String userId, List<AlarmDateType> repeatDays, int hour, int minute, Long scheduleId) throws JsonProcessingException {
        // Use the native query to get conflicting schedules with exact time match
        List<AlarmSchedule> conflictSchedules = alarmScheduleRepository.getConflictSchedules(
                userId,
                hour,
                minute);

        // Check for day conflicts among the time-conflicting schedules
        for (AlarmSchedule schedule : conflictSchedules) {
            if (schedule.getId().equals(scheduleId)) {
                continue;
            }

            List<AlarmDateType> alarmDays = objectMapper.readValue(schedule.getDaysActive(), new TypeReference<>() {
            });

            // Check if days conflict
            if (isDaysConflict(repeatDays, alarmDays)) {
                AlarmScheduleResDTO scheduleResDTO = convertToResDTO(schedule);
                AlarmActivity alarmActivity = getAlarmActivityType(schedule.getActivityId());
                String description = "Đã có báo thức <b>" + alarmActivity.getName() + "</b> lúc <b>" + scheduleResDTO.getTime() + " " + scheduleResDTO.getDayPart();
                if (scheduleResDTO.getRepeatDaysDescription().equals(AlarmDateType.ONE_TIME.getDescription())) {
                    description = description + " " + AlarmDateType.ONE_TIME.getDescription() + "</b>";
                } else if (scheduleResDTO.getRepeatDaysDescription().equals(AlarmDateType.EVERYDAY.getDescription())) {
                    description = description + " " + AlarmDateType.EVERYDAY.getDescription() + "</b>";
                } else {
                    List<AlarmDateType> daysActive = objectMapper.readValue(schedule.getDaysActive(), new TypeReference<>() {
                    });
                    description = description + " Thứ " + daysActive.stream().map(type -> type.getDescription().replace("T", "")).collect(Collectors.joining(", ")) + "</b>";
                }

                return AlarmScheduleConflictDTO.builder()
                        .title("Trùng báo thức")
                        .description(description)
                        .build();

            }
        }

        return null;
    }


    /**
     * Get all alarm activities
     *
     * @param request HTTP request containing user authentication
     * @return DataResponseDTO containing list of AlarmActivityDTO
     */
    public DataResponseDTO<?> getAllActivities(HttpServletRequest request) {
        getUserDataFromRequest(request); // Validate user authentication

        List<AlarmActivity> activities = alarmActivityRepository.findAllOrderById();
        List<AlarmActivityDTO> activityDTOs = activities.stream()
                .map(activity -> AlarmActivityDTO.builder()
                        .id(activity.getId())
                        .name(activity.getName())
                        .type(activity.getType())
                        .build())
                .collect(Collectors.toList());

        List<AlarmFrequencyDTO> frequencies = Arrays.stream(AlarmDateType.values())
                .filter(frequency -> frequency != AlarmDateType.EVERYDAY)
                .map(frequency -> AlarmFrequencyDTO.builder()
                        .label(frequency.getLabel())
                        .value(frequency.getType())
                        .build())
                .collect(Collectors.toList());

        AlarmActivityResDTO activityResDTO = AlarmActivityResDTO.builder()
                .activities(activityDTOs)
                .days(frequencies)
                .build();

        return new DataResponseDTO<>(HttpStatus.OK.value(), "Get all activities", activityResDTO);
    }


    /**
     * Convert AlarmSchedule entity to AlarmScheduleResDTO
     * Includes activity name from the AlarmActivity entity
     *
     * @param entity AlarmSchedule entity to convert
     * @return AlarmScheduleResDTO
     */
    private AlarmScheduleResDTO convertToResDTO(AlarmSchedule entity) {
        if (entity == null) {
            return null;
        }

        AlarmScheduleResDTO dto = new AlarmScheduleResDTO();
        dto.setId(entity.getId());

        // Convert hour and minute to LocalTime
        if (entity.getTimeHour() != null && entity.getTimeMinute() != null) {
            Integer hour = entity.getTimeHour() >= 12 ? entity.getTimeHour() - 12 : entity.getTimeHour();
            Integer minute = entity.getTimeMinute();

            String hourStr = hour < 10 ? "0" + hour : hour.toString();
            String minuteStr = minute < 10 ? "0" + minute : minute.toString();

            dto.setTime(hourStr + ":" + minuteStr);
            dto.setDayPart(entity.getTimeHour() >= 12 ? "PM" : "AM");
        }

        dto.setIsActive(entity.getIsActive());

        List<AlarmDateType> alarmDateTypes = null;
        try {
            alarmDateTypes = objectMapper.readValue(entity.getDaysActive(), new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        List<String> daysActive = new ArrayList<>();
        for (AlarmDateType alarmDateType : alarmDateTypes) {
            if (AlarmDateType.ONE_TIME == alarmDateType) {
                if (!isDateToday(entity.getUpdatedAt())) {
                    dto.setIsActive(false);
                    entity.setIsActive(false);
                    alarmScheduleRepository.save(entity);
                }
            }

            daysActive.add(alarmDateType.getType());
        }

        if (daysActive.size() == 1 && daysActive.get(0).equals(AlarmDateType.ONE_TIME.getType())) {
            daysActive = new ArrayList<>();
        }

        dto.setRepeatDays(daysActive);
        if (daysActive.size() == 7) {
            dto.setRepeatDaysDescription(AlarmDateType.EVERYDAY.getDescription());
        } else if (daysActive.isEmpty()) {
            dto.setRepeatDaysDescription(AlarmDateType.ONE_TIME.getDescription());
        } else {
            dto.setRepeatDaysDescription(daysActive.stream().collect(Collectors.joining(", ")));
        }

        // Get activity name if activity ID is present
        if (entity.getActivityId() != null) {
            AlarmActivity activity = getAlarmActivityType(entity.getActivityId());
            dto.setActivityName(activity.getName());
            dto.setActivityId(activity.getId());
        }

        return dto;
    }

    private boolean isDateToday(Date date) {
        // compare current date with entity creation date in format "yyyy-MM-dd"
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        Instant instant = date.toInstant();
        LocalDate localDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();
        String dateString = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return today.equals(dateString);
    }

    /**
     * Check if there's any day conflict between two sets of days
     *
     * @param days1 List of days from request
     * @param days2 Comma-separated string of days from database
     * @return true if there's at least one day in common
     */
    private boolean isDaysConflict(List<AlarmDateType> days1, List<AlarmDateType> days2) {
        for (AlarmDateType day1 : days1) {
            for (AlarmDateType day2 : days2) {
                if (day1 == day2) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Convert List<String> of days to a comma-separated String
     *
     * @param daysList List of days
     * @return Comma-separated String of days
     */
    private String convertListToString(List<String> daysList) {
        if (daysList == null || daysList.isEmpty()) {
            return "";
        }
        return String.join(",", daysList);
    }

    /**
     * Convert comma-separated String of days to a List<String>
     *
     * @param daysString Comma-separated String of days
     * @return List of days
     */
    private List<String> convertStringToList(String daysString) {
        if (daysString == null || daysString.isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.asList(daysString.split(","));
    }

    /**
     * Scheduled task to check current time schedules and prepare future schedules
     * This task runs every minute to:
     * 1. Check and process current time schedules
     * 2. Prepare schedules for next 5 minutes
     */
    @Scheduled(cron = "0 * * * * *") // Run at second 0 of every minute
    public void checkAndCacheAlarmSchedules() {
        try {
            LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
            int currentHour = now.getHour();
            int currentMinute = now.getMinute();
            // Convert to Vietnamese format (2-7 for Monday-Saturday, 8 for Sunday)
            AlarmDateType currentDayType = AlarmDateType.convertFromWeekDay(now.getDayOfWeek());

            log.info("Checking alarm schedules, hour: {}, minute: {}, day: {}", currentHour, currentMinute, currentDayType);
            quickService.submit(this::saveCachedAlarmSchedulesInAdvance);
            quickService.submit(() -> handleNonResponseAlarmSchedule(now));

            List<AlarmSchedule> todaySchedules = getCachedAlarmSchedulesByTime(currentHour, currentMinute, currentDayType);
            log.info("Today's alarm schedules, hour: {}, minute: {}, day: {}", currentHour, currentMinute, todaySchedules);
            // get String in current time to format HH:mm dd/MM/yyyy
            String currentTime = now.format(CodeDefine.ALARM_SCHEDULE_PATTERN);
            todaySchedules.forEach(schedule -> getAlarmDataForSchedule(schedule, currentTime));

        } catch (Exception e) {
            log.error("Error checking alarm schedules: ", e);
        }
    }

    /**
     * Manually trigger an alarm for a specific schedule ID
     *
     * @param id schedule id
     * @param request HTTP request for user validation
     * @return DataResponseDTO with trigger status
     */
    public DataResponseDTO<?> triggerAlarm(Long id, HttpServletRequest request) {
        UserDataDTO userDataDTO = getUserDataFromRequest(request);

        AlarmSchedule schedule = alarmScheduleRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Alarm schedule not found"));

        if (!schedule.getUserId().equals(userDataDTO.getUserId())) {
            throw new ResponseStatusException(HttpStatus.FORBIDDEN, "You don't have permission to trigger this schedule");
        }

        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        String currentTime = now.format(CodeDefine.ALARM_SCHEDULE_PATTERN);

        getAlarmDataForSchedule(schedule, currentTime);

        return new DataResponseDTO<>(HttpStatus.OK.value(), "Trigger alarm successfully");
    }

    private void getAlarmDataForSchedule(AlarmSchedule alarmSchedule, String currentTime) {
        // Schedule study time reminder and child-not-started checks immediately at alarm time
        LocalDateTime alarmDateTime = LocalDateTime.now(ZoneId.systemDefault());
        parentDashboardService.sendStudyTimeReminder(alarmSchedule.getUserId());
        scheduleChildNotStartedLearningCheck(alarmSchedule.getUserId(), alarmDateTime);

        String username = getUserNameByUserId(alarmSchedule.getUserId());
        AlarmActivity activity = getAlarmActivityType(alarmSchedule.getActivityId());
        String remindSentence = activity.getRemindVoice().replace("<username>", username);
        String remindAudio = recognizeService.convertTextToSpeech(remindSentence, InternalTextToSpeechVoice.getDefaultVoice(), "robot", "mp3", 1.0, null);
        String responseSentence = activity.getResponseVoice();
        String responseAudio = recognizeService.convertTextToSpeech(responseSentence, InternalTextToSpeechVoice.getDefaultVoice(), "robot", "mp3", 1.0, null);
        String message = activity.getType().getMessage();
        List<RobotUser> robotUsers = robotUserRepository.findByUserIdAndIsActiveTrue(alarmSchedule.getUserId());
        robotUsers.forEach(robotUser -> {
            try {
                connectService.handleAlarmRequest(robotUser.getRobotId(), robotUser.getUserId(), message, remindAudio, responseAudio, alarmSchedule.getId(), activity.getType(), currentTime);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });
    }

    private void scheduleChildNotStartedLearningCheck(String userId, LocalDateTime alarmDateTime) {
        LocalDateTime scheduleTime = alarmDateTime.plusMinutes(15);

        ChildNotStartedLearningProcessor.ChildNotStartedLearningPayload payload = new ChildNotStartedLearningProcessor.ChildNotStartedLearningPayload();
        payload.setUserId(userId);
        payload.setLessonId(null);
        payload.setAlarmTime(alarmDateTime.toString());
        payload.setAlarmScheduleId(null);

        scheduledTaskService.createTask(TaskTypes.CHILD_NOT_STARTED_LEARNING, payload, scheduleTime, "system");
        log.info("Scheduled child-not-started-learning check (immediate) for user {} at {}", userId, scheduleTime);
    }

    /**
     * Check and send default study time reminders at 20:15 for users without alarm schedules
     * This method is called by the scheduled task
     * Now uses centralized database-driven scheduler instead of in-memory tasks
     */
    @Scheduled(cron = "0 15 20 * * *") // Run at 20:15 (8:15 PM) every day
    public void checkDefaultStudyTimeReminders() {
        try {
            log.info("Checking default study time reminders at 20:15");

            // Record the time when default study time starts (20:15) and send reminder immediately
            LocalDateTime defaultStudyTime = LocalDateTime.now(ZoneId.systemDefault());
            LocalDateTime reminderTime = defaultStudyTime; // immediate at 20:15

            // Create reminders for all users without study schedules
            List<String> allUserIds = getAllActiveUserIds();
            int reminderCount = 0;

            for (String userId : allUserIds) {
                // Check if user has alarm schedules for today
                if (!hasStudyAlarmScheduleToday(userId)) {
                    // Create scheduled task using the new generalized system
                    StudyReminderProcessor.StudyReminderPayload payload = new StudyReminderProcessor.StudyReminderPayload();
                    payload.setUserId(userId);
                    payload.setAlarmTime(defaultStudyTime.toString());
                    payload.setAlarmScheduleId(null);

                    scheduledTaskService.createTask(TaskTypes.STUDY_REMINDER, payload, reminderTime, "system");
                    reminderCount++;
                    log.debug("Scheduled default study reminder for user: {} at {}", userId, reminderTime);
                }
            }

            log.info("Scheduled {} default study time reminders immediately at 20:15", reminderCount);

        } catch (Exception e) {
            log.error("Error checking default study time reminders", e);
        }
    }

    /**
     * Get all active user IDs from robot_user table
     */
    private List<String> getAllActiveUserIds() {
        return robotUserRepository.findAll().stream()
                .map(RobotUser::getUserId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * Check if user has any study-related alarm schedule for today (STUDY or HOMEWORK types)
     */
    private boolean hasStudyAlarmScheduleToday(String userId) {
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        AlarmDateType currentDayType = AlarmDateType.convertFromWeekDay(now.getDayOfWeek());

        List<AlarmSchedule> userSchedules = alarmScheduleRepository.findByUserId(userId);

        return userSchedules.stream().anyMatch(schedule -> {
            if (!schedule.getIsActive()) {
                return false;
            }

            // Check if this is a study-related alarm
            AlarmActivity activity = getAlarmActivityType(schedule.getActivityId());
            if (activity == null ||
                    (activity.getType() != AlarmActivityType.STUDY &&
                            activity.getType() != AlarmActivityType.HOMEWORK)) {
                return false;
            }

            try {
                List<AlarmDateType> daysActive = objectMapper.readValue(schedule.getDaysActive(), new TypeReference<>() {
                });
                return daysActive.contains(currentDayType) ||
                        (daysActive.contains(AlarmDateType.ONE_TIME) && isDateToday(schedule.getUpdatedAt()));
            } catch (JsonProcessingException e) {
                log.error("Error parsing days active for schedule ID: {}", schedule.getId(), e);
                return false;
            }
        });
    }

    private String getUserNameByUserId(String userId) {
        Profile profiles = profileRepository.getCurrentProfileByUserId(userId);
        if (profiles != null) {
            return profiles.getName();
        } else {
            return "Cậu";
        }
    }

    private AlarmActivity getAlarmActivityType(Long id) {
        RMapCache<String, String> alarmActivityMapcache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_ALARM_ACTIVITY);
        String key = "activity_id_" + id;
        if (alarmActivityMapcache.containsKey(key)) {
            try {
                return objectMapper.readValue(alarmActivityMapcache.get(key), new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("Error getting cached alarm activity: {}", e.getMessage(), e);
            }
        }

        AlarmActivity alarmActivity = alarmActivityRepository.findById(id).orElse(null);
        if (alarmActivity != null) {
            try {
                alarmActivityMapcache.put(key, objectMapper.writeValueAsString(alarmActivity), CodeDefine.TTL_KEY_FIX_CONTENT, TimeUnit.DAYS);
            } catch (Exception e) {
                log.error("Error caching alarm activity: {}", e.getMessage(), e);
            }
        } else {
            log.error("Alarm activity not found for id: {}", id);
        }

        return alarmActivity;
    }

    private void saveCachedAlarmSchedulesInAdvance() {
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        LocalDateTime futureTime = now.plusMinutes(5);
        int currentHour = futureTime.getHour();
        int currentMinute = futureTime.getMinute();
        AlarmDateType currentDayType = AlarmDateType.convertFromWeekDay(now.getDayOfWeek());
        saveCachedAlarmSchedulesByTime(currentHour, currentMinute, currentDayType);
    }

    private void saveCachedAlarmSchedulesByTime(int hour, int minute, AlarmDateType weekDay) {
        RMapCache<String, String> scheduleCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_ALARM_SCHEDULES);
        String timeKey = getScheduleKey(hour, minute, weekDay);
        List<AlarmSchedule> schedules = getAlarmSchedulesByTime(hour, minute, weekDay);
        if (CollectionUtils.isEmpty(schedules)) {
            schedules = new ArrayList<>();
        }

        try {
            scheduleCache.put(timeKey, objectMapper.writeValueAsString(schedules), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("Error save Alarm schedule into redis: {}", e.getMessage(), e);
            scheduleCache.remove(timeKey);
        }
    }

    private String getScheduleKey(int hour, int minute, AlarmDateType weekDay) {
        return String.format("%s_%02d:%02d", weekDay, hour, minute);
    }

    private void updateCachedAlarmSchedulesByTime(AlarmSchedule alarmSchedule) {
        List<AlarmDateType> daysActive = null;
        try {
            daysActive = objectMapper.readValue(alarmSchedule.getDaysActive(), new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Error getting cached alarm schedules: {}", e.getMessage(), e);
        }

        if (!CollectionUtils.isEmpty(daysActive)) {
            RMapCache<String, String> scheduleCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_ALARM_SCHEDULES);
            for (AlarmDateType weekDay : daysActive) {
                String timeKey = getScheduleKey(alarmSchedule.getTimeHour(), alarmSchedule.getTimeMinute(), weekDay);
                scheduleCache.remove(timeKey);
            }
        }
    }

    private List<AlarmSchedule> getAlarmSchedulesByTime(int hour, int minute, AlarmDateType weekDay) {
        // Get active schedules for current time
        List<AlarmSchedule> activeSchedules = alarmScheduleRepository.findActiveSchedulesByTime(hour,
                minute);

        // Filter schedules that are active for today
        return activeSchedules.stream()
                .filter(schedule -> {
                    List<AlarmDateType> daysActive = null;
                    try {
                        daysActive = objectMapper.readValue(schedule.getDaysActive(), new TypeReference<>() {
                        });
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }

                    if (daysActive.contains(weekDay)) {
                        return true;
                    }

                    return daysActive.contains(AlarmDateType.ONE_TIME) && isDateToday(schedule.getUpdatedAt());
                })
                .collect(Collectors.toList());
    }

    private List<AlarmSchedule> getCachedAlarmSchedulesByTime(int hour, int minute, AlarmDateType weekDay) {
        RMapCache<String, String> scheduleCache = redissonClient.getMapCache(CodeDefine.REDIS_KEY_ALARM_SCHEDULES);
        String timeKey = "getScheduleKey(hour, minute, weekDay)";
        if (scheduleCache.containsKey(timeKey)) {
            try {
                return objectMapper.readValue(scheduleCache.get(timeKey), new TypeReference<>() {
                });
            } catch (Exception e) {
                log.error("Error getting cached alarm schedules: {}", e.getMessage(), e);
                return new ArrayList<>();
            }
        } else {
            return getAlarmSchedulesByTime(hour, minute, weekDay);
        }
    }

    private void handleNonResponseAlarmSchedule(LocalDateTime now) {
        LocalDateTime pastTime = now.minusMinutes(5);
        String currentTime = pastTime.format(CodeDefine.ALARM_SCHEDULE_PATTERN);
        List<AlarmScheduleExecution> scheduleExecutions = alarmScheduleExecutionRepository.findByTimeSchedule(currentTime);
        try {
            log.info("Check pending alarm schedules, time: {}, result: {}", currentTime, objectMapper.writeValueAsString(scheduleExecutions));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        scheduleExecutions.removeIf(scheduleExecution -> scheduleExecution.getStatus() != CommunicationRequestStatusType.PENDING);
        scheduleExecutions.forEach(scheduleExecution -> notificationService.sendNotificationToUserId(PushNotificationReqDTO.builder()
                .userId(scheduleExecution.getUserId())
                .title("Báo thức")
                .message("Pika không thể phát báo thức vì đang tắt hoặc mất kết nối")
                .build()));
    }

    public void sendAlarmManual(AlarmManualDTO alarmManualDTO) {
        User user = userRepository.findByPhone(alarmManualDTO.getUserPhone());
        if (user == null) {
            throw new UserNotFoundException();
        }

        RobotUser robotUser = robotUserRepository.findByUserIdAndIsActiveTrue(user.getId()).get(0);
        try {
            LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
            String currentTime = now.format(CodeDefine.ALARM_SCHEDULE_PATTERN);

            String username = getUserNameByUserId(user.getId());

            String remindSentence = alarmManualDTO.getRemindSentence().replace("<username>", username);
            String remindAudio = alarmManualDTO.getRemindVoice() != null && !alarmManualDTO.getRemindVoice().isBlank()
                    ? alarmManualDTO.getRemindVoice()
                    : recognizeService.convertTextToSpeech(remindSentence, InternalTextToSpeechVoice.getDefaultVoice(), "robot", "mp3", 1.0, null);

            String responseAudio = alarmManualDTO.getResponseVoice();
            String sound = alarmManualDTO.getSound();

            connectService.handleAlarmManualRequest(
                    robotUser.getRobotId(),
                    robotUser.getUserId(),
                    "send manual",
                    sound,
                    remindAudio,
                    responseAudio,
                    currentTime
            );
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }
}
