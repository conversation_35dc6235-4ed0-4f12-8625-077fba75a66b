package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.UserDataDTO;
import com.stepup.springrobot.dto.game.*;
import com.stepup.springrobot.dto.speech.SpeechBotResponseDTO;
import com.stepup.springrobot.exception.business.audio.AudioErrorException;
import com.stepup.springrobot.exception.business.content.ContentNotFoundException;
import com.stepup.springrobot.exception.business.user.AnonymousAccessException;
import com.stepup.springrobot.model.game.*;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.repository.auth.UserRepository;
import com.stepup.springrobot.repository.game.*;
import com.stepup.springrobot.repository.robot.RobotUserRepository;
import com.stepup.springrobot.security.JwtService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Log4j2
@Service
public class GameService extends CommonService {
    @Autowired
    private GameEchoTowerWordRepository gameEchoTowerWordRepository;

    @Autowired
    private GameEchoTowerStateRepository gameEchoTowerStateRepository;

    @Autowired
    private GameEchoTowerHistoryRepository gameEchoTowerHistoryRepository;

    @Autowired
    private GameEchoTowerFloorTurnPlayRepository gameEchoTowerFloorTurnPlayRepository;

    @Autowired
    private GameWordRaceWordRepository gameWordRaceWordRepository;

    @Autowired
    private GameWordRaceHistoryRepository gameWordRaceHistoryRepository;

    @Autowired
    private GameWordRaceStateRepository gameWordRaceStateRepository;

    @Autowired
    private GameAlphaBlastWordRepository gameAlphaBlastWordRepository;

    @Autowired
    private GameAlphaBlastStateRepository gameAlphaBlastStateRepository;

    @Autowired
    private GameAlphaBlastHistoryRepository gameAlphaBlastHistoryRepository;

    @Autowired
    private GameWordRaceTurnPlayHistoryRepository gameWordRaceTurnPlayHistoryRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RobotUserRepository robotUserRepository;

    @Autowired
    private GameItemRepository gameItemRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SharedService sharedService;

    protected GameService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public DataResponseDTO<?> getGamesList(HttpServletRequest request) {
        String robotId = request.getHeader("robot-id");
        if (robotId == null) {
            throw new AnonymousAccessException();
        }

        // Mock data - in a real application, this might come from a database

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách game thành công", Arrays.asList(
                new GameInfoDTO("ECHO_TOWER", "Echo tower"),
                new GameInfoDTO("ALPHA_BLAST", "Alpha blast"),
                new GameInfoDTO("WORD_RACE", "Word race")
        ));
    }

    public DataResponseDTO<?> initGameEchoTowerData(HttpServletRequest request) {
        String robotId = request.getHeader("robot-id");
        if (robotId == null) {
            throw new AnonymousAccessException();
        }

        GameEchoTowerFloorTurnPlay prevFloorTurnPlay = gameEchoTowerFloorTurnPlayRepository.findFirstByProfileIdOrderByIdDesc(robotId);
        Long currentFloorId = 1L;
        List<GameEchoTowerWord> echoTowerWords = getEchoTowerWordsByFloorId(currentFloorId);
        GameEchoTowerFloorTurnPlay floorTurnPlay = gameEchoTowerFloorTurnPlayRepository.save(GameEchoTowerFloorTurnPlay.builder()
                .profileId(robotId)
                .turnPlay(prevFloorTurnPlay == null ? 1 : prevFloorTurnPlay.getTurnPlay() + 1)
                .playerHealth(6)
                .currentMonsterHealth(echoTowerWords.size())
                .score(0L)
                .build());


        EchoTowerDataResDTO towerDataResDTO = EchoTowerDataResDTO.builder()
                .floor(currentFloorId)
                .score(0L)
                .player(EchoTowerPlayerDTO.builder()
                        .currentHealth(floorTurnPlay.getPlayerHealth())
                        .build())
                .monster(EchoTowerPlayerDTO.builder()
                        .currentHealth(echoTowerWords.size())
                        .build())
                .words(echoTowerWords.stream()
                        .map(word -> EchoTowerWordDTO.builder()
                                .wordId(word.getId())
                                .word(word.getWord())
                                .build())
                        .collect(Collectors.toList()))
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu game echo tower thành công", towerDataResDTO);
    }

    public DataResponseDTO<?> nextGameEchoTowerData(HttpServletRequest request) {
        String robotId = request.getHeader("robot-id");
        if (robotId == null) {
            throw new AnonymousAccessException();
        }

        GameEchoTowerFloorTurnPlay floorTurnPlay = gameEchoTowerFloorTurnPlayRepository.findFirstByProfileIdOrderByIdDesc(robotId);
        if (floorTurnPlay.getCurrentMonsterHealth() > 0) {
            return new DataResponseDTO<>(CodeDefine.OK, "Bạn chưa vượt qua cửa trước!");
        }

        Long currentFloorId = floorTurnPlay.getCurrentFloorId() + 1;

        List<GameEchoTowerWord> echoTowerWords = getEchoTowerWordsByFloorId(currentFloorId);
        floorTurnPlay.setCurrentMonsterHealth(echoTowerWords.size());

        gameEchoTowerFloorTurnPlayRepository.save(floorTurnPlay);

        EchoTowerDataResDTO towerDataResDTO = EchoTowerDataResDTO.builder()
                .floor(currentFloorId)
                .score(floorTurnPlay.getScore())
                .player(EchoTowerPlayerDTO.builder()
                        .currentHealth(floorTurnPlay.getPlayerHealth())
                        .build())
                .monster(EchoTowerPlayerDTO.builder()
                        .currentHealth(echoTowerWords.size())
                        .build())
                .words(echoTowerWords.stream()
                        .map(word -> EchoTowerWordDTO.builder()
                                .wordId(word.getId())
                                .word(word.getWord())
                                .build())
                        .collect(Collectors.toList()))
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu cửa echo tower tiếp theo thành công", towerDataResDTO);
    }

    private List<GameEchoTowerWord> getEchoTowerWordsByFloorId(Long floorId) {
        return gameEchoTowerWordRepository.findByFloorIdOrderByOrderAsc(floorId);
    }

    public DataResponseDTO<?> checkEchoTowerPronounceScore(HttpServletRequest request, MultipartFile multipartFile, Long wordId) throws JsonProcessingException {
        String robotId = request.getHeader("robot-id");
        if (robotId == null) {
            throw new AnonymousAccessException();
        }

        GameEchoTowerFloorTurnPlay floorTurnPlay = gameEchoTowerFloorTurnPlayRepository.findFirstByProfileIdOrderByIdDesc(robotId);
        if (floorTurnPlay.getPlayerHealth() <= 0) {
            return new DataResponseDTO<>(CodeDefine.OK, "Bạn đã hết mạng, chơi lại từ đầu nhé!");
        }

        if (floorTurnPlay.getCurrentMonsterHealth() <= 0) {
            return new DataResponseDTO<>(CodeDefine.OK, "Chúc mừng bạn đã vượt qua tầng này!");
        }

        GameEchoTowerWord gameEchoTowerWord = gameEchoTowerWordRepository.findById(wordId).orElse(null);
        if (gameEchoTowerWord == null) {
            throw new ContentNotFoundException("word", wordId);
        }

        File objectFile = convertMultiPartFileToFile(multipartFile, robotId, true);
        //tmp
        if (objectFile == null) {
            throw new AudioErrorException();
        }

        Object result = sharedService.callCheckSpeechSingleByOkHttp(objectFile, gameEchoTowerWord.getWord());
        SpeechBotResponseDTO speechBotResponseDTO = objectMapper.convertValue(result, new TypeReference<>() {
        });

        List<PronounceScoreResultDTO> resultDTOS = objectMapper.convertValue(speechBotResponseDTO.getResult(), new TypeReference<>() {
        });

        double pronounceScore = speechBotResponseDTO.getTotalScore();
        Integer plusScore = 0;
        if (pronounceScore < 0.6) {
            floorTurnPlay.setPlayerHealth(floorTurnPlay.getPlayerHealth() - 1);
        } else if (pronounceScore < 0.85) {
            floorTurnPlay.setCurrentMonsterHealth(Math.max(floorTurnPlay.getCurrentMonsterHealth() - 1, 0));
            plusScore = 5;
        } else {
            floorTurnPlay.setCurrentMonsterHealth(Math.max(floorTurnPlay.getCurrentMonsterHealth() - 2, 0));
            plusScore = 8;
        }

        floorTurnPlay.setScore(floorTurnPlay.getScore() + plusScore);
        floorTurnPlay.setCurrentFloorId(gameEchoTowerWord.getFloorId());
        gameEchoTowerFloorTurnPlayRepository.save(floorTurnPlay);
        gameEchoTowerHistoryRepository.save(GameEchoTowerHistory.builder()
                .profileId(robotId)
                .wordId(wordId)
                .data(objectMapper.writeValueAsString(resultDTOS))
                .plusScore(plusScore)
                .turnPlay(floorTurnPlay.getTurnPlay())
                .build());

        // Update profile state
        GameEchoTowerState gameEchoTowerState = gameEchoTowerStateRepository.findByProfileId(robotId);
        if (gameEchoTowerState == null) {
            gameEchoTowerState = GameEchoTowerState.builder()
                    .profileId(robotId)
                    .floorId(gameEchoTowerWord.getFloorId())
                    .score(floorTurnPlay.getScore())
                    .build();
        } else {
            gameEchoTowerState.setFloorId(gameEchoTowerWord.getFloorId());
            gameEchoTowerState.setScore(floorTurnPlay.getScore());
        }

        gameEchoTowerStateRepository.save(gameEchoTowerState);

        EchoTowerCheckSpeechResDTO speechResDTO = EchoTowerCheckSpeechResDTO.builder()
                .player(EchoTowerPlayerDTO.builder()
                        .currentHealth(floorTurnPlay.getPlayerHealth())
                        .build())
                .monster(EchoTowerPlayerDTO.builder()
                        .currentHealth(floorTurnPlay.getCurrentMonsterHealth())
                        .build())
                .score(floorTurnPlay.getScore())
                .pronounceResult(resultDTOS)
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu chấm điểm game echo tower thành công", speechResDTO);
    }

    public DataResponseDTO<?> getEchoTowerSummary(HttpServletRequest request) {
        String robotId = request.getHeader("robot-id");
        if (robotId == null) {
            throw new AnonymousAccessException();
        }

        GameEchoTowerState gameEchoTowerState = gameEchoTowerStateRepository.findByProfileId(robotId);

        EchoTowerPlayerSummaryDTO playerSummaryDTO = EchoTowerPlayerSummaryDTO.builder()
                .floor(gameEchoTowerState.getFloorId())
                .rank(1109)
                .monster(gameEchoTowerState.getFloorId())
                .score(gameEchoTowerState.getScore())
                .build();

        List<String> topRanks = new ArrayList<>();
        topRanks.add("1st: F100: 9999");
        topRanks.add("2nd: F100: 9999");
        topRanks.add("3rd: F100: 9999");
        topRanks.add("4th: F100: 9999");
        topRanks.add("5th: F100: 9999");

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu tổng kết echo tower thành công", GameSummaryResDTO.builder()
                .player(playerSummaryDTO)
                .topRanks(topRanks)
                .build());
    }

    public DataResponseDTO<?> initWordRace(HttpServletRequest request) {
        String robotId = request.getHeader("robot-id");
        if (robotId == null) {
            throw new AnonymousAccessException();
        }

        List<GameWordRaceWord> words = gameWordRaceWordRepository.findAll();
        List<GameWordRaceWord> normalWords = words.stream().filter(word -> !word.getIsPowerUp()).collect(Collectors.toList());
        List<GameWordRaceWord> powerUpWords = words.stream().filter(GameWordRaceWord::getIsPowerUp).collect(Collectors.toList());

        // Get random 40 words from normal words
        List<GameWordRaceWord> randomNormalWords = getListRandomElements(normalWords, 40);
        // Get random 10 words from power up words
        List<GameWordRaceWord> randomPowerUpWords = getListRandomElements(powerUpWords, 10);

        Random random = new Random();
        // Create first opponent have progress with length of 10 and is_correct randomly, response_time randomly from 1 to 5
        WordRaceOpponentDTO firstOpponent = WordRaceOpponentDTO.builder()
                .progress(IntStream.range(0, 10)
                        .mapToObj(i -> WordRaceOpponentProgressDTO.builder()
                                .isCorrect(random.nextBoolean())
                                .responseTime((double) (random.nextInt(500) + 150) / 100)
                                .build())
                        .collect(Collectors.toList()))
                .build();

        // Create second opponent have progress with length of 10 and is_correct randomly
        WordRaceOpponentDTO secondOpponent = WordRaceOpponentDTO.builder()
                .progress(IntStream.range(0, 10)
                        .mapToObj(i -> WordRaceOpponentProgressDTO.builder()
                                .isCorrect(random.nextBoolean())
                                .responseTime((double) (random.nextInt(500) + 150) / 100)
                                .build())
                        .collect(Collectors.toList()))
                .build();

        WordRaceDataResDTO wordRaceDataResDTO = WordRaceDataResDTO.builder()
                .words(randomNormalWords.stream()
                        .map(word -> {
                            List<String> elements = Arrays.asList(word.getPartOne(), word.getPartTwo(), word.getPartThree());
                            Collections.shuffle(elements);
                            return WordRaceWordDTO.builder()
                                    .word(word.getWord())
                                    .elements(elements)
                                    .build();
                        }).collect(Collectors.toList()))
                .powerUpWords(randomPowerUpWords.stream()
                        .map(word -> {
                            List<String> elements = Arrays.asList(word.getPartOne(), word.getPartTwo(), word.getPartThree());
                            Collections.shuffle(elements);
                            return WordRaceWordDTO.builder()
                                    .word(word.getWord())
                                    .elements(elements)
                                    .build();
                        }).collect(Collectors.toList())
                )
                .targetDistance(10)
                .firstOpponent(firstOpponent)
                .secondOpponent(secondOpponent)
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu game word race thành công", wordRaceDataResDTO);
    }

    private <T> List<T> getListRandomElements(List<T> elements, int count) {
        Random random = new Random();
        // get unique random words
        Set<Integer> indexes = new HashSet<>();
        while (indexes.size() < count) {
            indexes.add(random.nextInt(elements.size()));
        }

        return indexes.stream().map(elements::get).collect(Collectors.toList());
    }

    public void saveWordRaceTurnPlayHistory(List<WordRaceHistoryReqDTO> progress, Integer turnPlay, String robotId) {
        List<GameWordRaceTurnPlayHistory> histories = progress.stream()
                .map(wordRaceHistoryReqDTO -> GameWordRaceTurnPlayHistory.builder()
                        .profileId(robotId)
                        .word(wordRaceHistoryReqDTO.getWord())
                        .isCorrect(wordRaceHistoryReqDTO.getIsCorrect())
                        .responseTime(wordRaceHistoryReqDTO.getResponseTime())
                        .turnPlay(turnPlay)
                        .build())
                .collect(Collectors.toList());
        gameWordRaceTurnPlayHistoryRepository.saveAll(histories);
    }

    public DataResponseDTO<?> getWordRaceSummary(HttpServletRequest request, WordRaceSummaryReqDTO wordRaceSummaryReqDTO) {
        String robotId = request.getHeader("robot-id");
        if (robotId == null) {
            throw new AnonymousAccessException();
        }

        GameWordRaceState gameWordRaceState = gameWordRaceStateRepository.findByProfileId(robotId);
        if (gameWordRaceState == null) {
            gameWordRaceState = GameWordRaceState.builder()
                    .profileId(robotId)
                    .score(wordRaceSummaryReqDTO.getScore())
                    .wordCount(wordRaceSummaryReqDTO.getWordCount())
                    .build();
        } else {
            gameWordRaceState.setScore(wordRaceSummaryReqDTO.getScore());
            gameWordRaceState.setWordCount(wordRaceSummaryReqDTO.getWordCount());
        }

        gameWordRaceStateRepository.save(gameWordRaceState);

        GameWordRaceHistory latestHistory = gameWordRaceHistoryRepository.findFirstByProfileIdOrderByIdDesc(robotId);
        Integer turnPlay = latestHistory == null || latestHistory.getTurnPlay() == null
                ? 1
                : latestHistory.getTurnPlay() + 1;

        gameWordRaceHistoryRepository.save(GameWordRaceHistory.builder()
                .profileId(robotId)
                .score(wordRaceSummaryReqDTO.getScore())
                .wordCount(wordRaceSummaryReqDTO.getWordCount())
                .turnPlay(turnPlay)
                .build());

        List<String> topRanks = new ArrayList<>();
        topRanks.add("1st: F100: 9999");
        topRanks.add("2nd: F100: 9999");
        topRanks.add("3rd: F100: 9999");
        topRanks.add("4th: F100: 9999");
        topRanks.add("5th: F100: 9999");

        saveWordRaceTurnPlayHistory(wordRaceSummaryReqDTO.getProgress(), turnPlay, robotId);
        GameSummaryResDTO wordRaceSummaryResDTO = GameSummaryResDTO.builder()
                .player(WordRacePlayerSummaryDTO.builder()
                        .score(gameWordRaceState.getScore())
                        .wordCount(gameWordRaceState.getWordCount())
                        .distance(gameWordRaceState.getWordCount())
                        .rank(1000)
                        .build())
                .topRanks(topRanks)
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu tổng kết game word race thành công", wordRaceSummaryResDTO);
    }

    public DataResponseDTO<?> initAlphaBlast(HttpServletRequest request) {
        String robotId = request.getHeader("robot-id");
        if (robotId == null) {
            throw new AnonymousAccessException();
        }

        List<GameAlphaBlastWord> words = gameAlphaBlastWordRepository.findAll();
        words = getListRandomElements(words, 40);

        AlphaBlastDataResDTO alphaBlastDataResDTO = AlphaBlastDataResDTO.builder()
                .words(words.stream()
                        .map(word -> AlphaBlastWordDTO.builder()
                                .word(word.getWord())
                                .build())
                        .collect(Collectors.toList()))
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu game alpha blast thành công", alphaBlastDataResDTO);
    }

    public DataResponseDTO<?> getAlphaBlastSummary(HttpServletRequest request, AlphaBlastSummaryReqDTO alphaBlastSummaryReqDTO) {
        String robotId = request.getHeader("robot-id");
        if (robotId == null) {
            throw new AnonymousAccessException();
        }

        GameAlphaBlastState gameAlphaBlastState = gameAlphaBlastStateRepository.findByProfileId(robotId);
        if (gameAlphaBlastState == null) {
            gameAlphaBlastState = GameAlphaBlastState.builder()
                    .profileId(robotId)
                    .score(alphaBlastSummaryReqDTO.getScore())
                    .wordCount(alphaBlastSummaryReqDTO.getWordCount())
                    .characterCount(alphaBlastSummaryReqDTO.getCharacterCount())
                    .build();
        } else {
            gameAlphaBlastState.setScore(alphaBlastSummaryReqDTO.getScore());
            gameAlphaBlastState.setWordCount(alphaBlastSummaryReqDTO.getWordCount());
            gameAlphaBlastState.setCharacterCount(alphaBlastSummaryReqDTO.getCharacterCount());
        }

        gameAlphaBlastStateRepository.save(gameAlphaBlastState);

        gameAlphaBlastHistoryRepository.save(GameAlphaBlastHistory.builder()
                .profileId(robotId)
                .score(alphaBlastSummaryReqDTO.getScore())
                .wordCount(alphaBlastSummaryReqDTO.getWordCount())
                .characterCount(alphaBlastSummaryReqDTO.getCharacterCount())
                .build());

        List<String> topRanks = new ArrayList<>();
        topRanks.add("1st: F100: 9999");
        topRanks.add("2nd: F100: 9999");
        topRanks.add("3rd: F100: 9999");
        topRanks.add("4th: F100: 9999");
        topRanks.add("5th: F100: 9999");

        GameSummaryResDTO gameSummaryResDTO = GameSummaryResDTO.builder()
                .player(AlphaBlastPlayerSummaryDTO.builder()
                        .score(gameAlphaBlastState.getScore())
                        .wordCount(gameAlphaBlastState.getWordCount())
                        .characterCount(gameAlphaBlastState.getCharacterCount())
                        .rank(1000)
                        .build())
                .topRanks(topRanks)
                .build();

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu tổng kết game alpha blast thành công", gameSummaryResDTO);
    }

    // User balance methods
    public DataResponseDTO<?> getUserBalance(HttpServletRequest request) {
        try {
            UserDataDTO userDataDTO = getDataUserHeader(request);
            User user = userRepository.findById(userDataDTO.getUserId()).orElse(null);
            if (user == null) {
                throw new AnonymousAccessException();
            }

            UserBalanceDTO balanceDTO = UserBalanceDTO.builder()
                    .redeemableBalance(user.getRedeemableBalance())
                    .build();

            return new DataResponseDTO<>(CodeDefine.OK, "Lấy số dư đổi thưởng thành công", balanceDTO);
        } catch (IOException e) {
            throw new AnonymousAccessException();
        }
    }

    public DataResponseDTO<?> updateUserBalance(HttpServletRequest request, UserBalanceDTO requestDTO) {
        try {
            UserDataDTO userDataDTO = getDataUserHeader(request);
            User user = userRepository.findById(userDataDTO.getUserId()).orElse(null);
            if (user == null) {
                throw new AnonymousAccessException();
            }

            user.setRedeemableBalance(requestDTO.getRedeemableBalance());
            userRepository.save(user);

            UserBalanceDTO balanceDTO = UserBalanceDTO.builder()
                    .redeemableBalance(user.getRedeemableBalance())
                    .build();

            return new DataResponseDTO<>(CodeDefine.OK, "Cập nhật số dư đổi thưởng thành công", balanceDTO);
        } catch (IOException e) {
            throw new AnonymousAccessException();
        }
    }

    // User items methods
    public DataResponseDTO<?> getUserItems(HttpServletRequest request) {
        try {
            UserDataDTO userDataDTO = getDataUserHeader(request);
            User user = userRepository.findById(userDataDTO.getUserId()).orElse(null);
            if (user == null) {
                throw new AnonymousAccessException();
            }

            List<GameItem> gameItems = gameItemRepository.findByUserIdAndIsActiveTrue(userDataDTO.getUserId());
            List<String> itemIds = gameItems.stream()
                    .map(GameItem::getItemId)
                    .collect(Collectors.toList());

            UserItemsDTO itemsDTO = UserItemsDTO.builder()
                    .ownedItemIds(itemIds)
                    .build();

            return new DataResponseDTO<>(CodeDefine.OK, "Lấy danh sách vật phẩm sở hữu thành công", itemsDTO);
        } catch (IOException e) {
            throw new AnonymousAccessException();
        }
    }

    public DataResponseDTO<?> updateUserItems(HttpServletRequest request, UserItemsDTO requestDTO) {
        try {
            UserDataDTO userDataDTO = getDataUserHeader(request);
            User user = userRepository.findById(userDataDTO.getUserId()).orElse(null);
            if (user == null) {
                throw new AnonymousAccessException();
            }

            String userId = userDataDTO.getUserId();

            // Get current items
            List<GameItem> currentItems = gameItemRepository.findByUserId(userId);
            List<String> currentItemIds = currentItems.stream()
                    .map(GameItem::getItemId)
                    .collect(Collectors.toList());

            // Get new item IDs
            List<String> newItemIds = requestDTO.getOwnedItemIds();

            // Remove items that are no longer in the list
            currentItems.stream()
                    .filter(item -> !newItemIds.contains(item.getItemId()))
                    .forEach(item -> gameItemRepository.delete(item));

            // Add new items
            newItemIds.stream()
                    .filter(itemId -> !currentItemIds.contains(itemId))
                    .forEach(itemId -> {
                        GameItem newItem = GameItem.builder()
                                .userId(userId)
                                .itemId(itemId)
                                .itemName("Item " + itemId) // Default name, can be enhanced later
                                .itemType("GENERAL") // Default type, can be enhanced later
                                .isActive(true)
                                .build();
                        gameItemRepository.save(newItem);
                    });

            // Get updated items for response
            List<GameItem> updatedItems = gameItemRepository.findByUserIdAndIsActiveTrue(userId);
            List<String> updatedItemIds = updatedItems.stream()
                    .map(GameItem::getItemId)
                    .collect(Collectors.toList());

            UserItemsDTO itemsDTO = UserItemsDTO.builder()
                    .ownedItemIds(updatedItemIds)
                    .build();

            return new DataResponseDTO<>(CodeDefine.OK, "Cập nhật danh sách vật phẩm sở hữu thành công", itemsDTO);
        } catch (IOException e) {
            throw new AnonymousAccessException();
        }
    }
} 