package com.stepup.springrobot.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.chat.BotInfoDTO;
import com.stepup.springrobot.dto.chat.BotListResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/bot-view")
@RequiredArgsConstructor
@Slf4j
public class BotViewController {

    private final OkHttpClient httpClient = new OkHttpClient();
    private final ObjectMapper objectMapper;

    @GetMapping("")
    public String getBotIndex() {
        return "bot/index";
    }

    @GetMapping("/list")
    public String getBotListView(@RequestParam(required = false) String search, Model model) {
        try {
            String url = "https://ai-tools-api.hacknao.edu.vn:19404/robot-ai-lesson/api/v1/database/listBot";
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("Failed to fetch bot list. Response code: {}", response.code());
                    model.addAttribute("error", "Không thể lấy danh sách bot từ external API");
                    return "bot/list";
                }

                String responseBody = response.body().string();
                BotListResDTO botListResponse = objectMapper.readValue(responseBody, BotListResDTO.class);
                
                List<BotInfoDTO> bots = botListResponse.getResult();
                if (bots == null) {
                    bots = new ArrayList<>();
                }
                
                // Filter bots by name if search parameter is provided
                if (search != null && !search.trim().isEmpty()) {
                    String searchLower = search.toLowerCase().trim();
                    bots = bots.stream()
                            .filter(bot -> bot.getName() != null && 
                                    bot.getName().toLowerCase().contains(searchLower))
                            .collect(Collectors.toList());
                }
                
                model.addAttribute("bots", bots);
                model.addAttribute("search", search);
                model.addAttribute("totalBots", bots.size());
                
                return "bot/list";
            }
        } catch (IOException e) {
            log.error("Error fetching bot list", e);
            model.addAttribute("error", "Lỗi khi lấy danh sách bot: " + e.getMessage());
            return "bot/list";
        } catch (Exception e) {
            log.error("Unexpected error while fetching bot list", e);
            model.addAttribute("error", "Lỗi không xác định: " + e.getMessage());
            return "bot/list";
        }
    }
}
