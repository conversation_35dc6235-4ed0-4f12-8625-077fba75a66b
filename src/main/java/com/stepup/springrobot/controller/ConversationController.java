package com.stepup.springrobot.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.chat.*;
import com.stepup.springrobot.model.chat.ConversationSource;
import com.stepup.springrobot.service.AIRobotConversationService;
import com.stepup.springrobot.service.OpenAIService;
import com.stepup.springrobot.service.TestExecutionService;
import com.stepup.springrobot.service.TestLessonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.log4j.Log4j2;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/conversations")
@Tag(name = "Conversations", description = "APIs to manage AI robot conversations")
@Log4j2
public class ConversationController extends BaseController {
    @Autowired
    private AIRobotConversationService aiRobotConversationService;

    @Autowired
    private TestLessonService testLessonService;

    @Autowired
    private OpenAIService openAIService;

    @Autowired
    private TestExecutionService testExecutionService;

    private final OkHttpClient httpClient = new OkHttpClient();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostMapping("/init")
    @Operation(summary = "Initialize conversation without WebSocket", description = "Creates a conversation and returns initial bot message and configs")
    public ResponseEntity<DataResponseDTO<RobotConversationMsgResDTO>> initConversation(HttpServletRequest request, @RequestBody RobotInitConversationReqDTO req) {
        String userId = req.getUserId();
        String robotId = req.getRobotId();
        Long botId = req.getBotId();
        String sessionId = req.getSessionId();
        String asrType = req.getAsrType();
        String source = req.getSource();
        Boolean isWebMvp = req.getIsWebMvp();

        if (StringUtils.isAnyBlank(userId, robotId)) {
            return ResponseEntity.badRequest().body(new DataResponseDTO<>(400, "Thiếu thông tin userId hoặc robotId", null));
        }

        if (botId == null) {
            return ResponseEntity.badRequest().body(new DataResponseDTO<>(400, "Thiếu thông tin botId", null));
        }

        String initText = "Hello";
        String socketSessionId = sessionId != null ? sessionId : "rest-api-" + System.currentTimeMillis();
        String ip = getClientIpAddress(request);
        ConversationSource conversationSource = source != null ? ConversationSource.from(source) : ConversationSource.TEST;

        RobotConversationMsgResDTO data = aiRobotConversationService.sendCustomUserSentence(
                userId, robotId, initText, socketSessionId, ip, botId, new StringBuilder(), null, null, isWebMvp, conversationSource);

        return success(new DataResponseDTO<>(CodeDefine.OK, "Thành công", data));
    }

    @PostMapping("/llm")
    @Operation(summary = "Send transcript directly to LLM", description = "Send transcript to LLM service, skipping audio chunk handling")
    public ResponseEntity<DataResponseDTO<RobotConversationMsgResDTO>> sendTranscriptToLLM(HttpServletRequest request, @RequestBody RobotSendTranscriptReqDTO req) {
        String userId = req.getUserId();
        String robotId = req.getRobotId();
        Long botId = req.getBotId();
        String sessionId = req.getSessionId();
        String source = req.getSource();
        Boolean isWebMvp = req.getIsWebMvp();
        String transcript = req.getTranscript();

        if (StringUtils.isAnyBlank(userId, robotId, transcript)) {
            return ResponseEntity.badRequest().body(new DataResponseDTO<>(400, "Thiếu thông tin userId, robotId hoặc transcript", null));
        }

        if (botId == null) {
            return ResponseEntity.badRequest().body(new DataResponseDTO<>(400, "Thiếu thông tin botId", null));
        }

        String socketSessionId = sessionId != null ? sessionId : "rest-api-" + System.currentTimeMillis();
        String ip = getClientIpAddress(request);
        ConversationSource conversationSource = source != null ? ConversationSource.from(source) : ConversationSource.TEST;

        RobotConversationMsgResDTO data = aiRobotConversationService.sendCustomUserSentence(
                userId, robotId, transcript, socketSessionId, ip, botId, new StringBuilder(), null, null, isWebMvp, conversationSource);

        return success(new DataResponseDTO<>(CodeDefine.OK, "Thành công", data));
    }

    @PostMapping("/test-lesson")
    @Operation(summary = "Test lesson workflow", description = "Generate test conversation flows to cover all intents in the lesson")
    public ResponseEntity<DataResponseDTO<TestLessonResDTO>> testLesson(@RequestBody TestLessonReqDTO req) {
        Long botId = req.getBotId();

        if (botId == null) {
            return ResponseEntity.badRequest().body(new DataResponseDTO<>(400, "Thiếu thông tin botId", null));
        }

        try {
            TestLessonResDTO result = testLessonService.testLessonWorkflow(botId);
            return success(new DataResponseDTO<>(CodeDefine.OK, "Thành công", result));
        } catch (Exception e) {
            log.error("Error testing lesson for bot ID: {}", botId, e);
            return ResponseEntity.status(500).body(new DataResponseDTO<>(500, "Lỗi khi test lesson: " + e.getMessage(), null));
        }
    }

    @PostMapping("/generate-user-response")
    @Operation(summary = "Generate user response using OpenAI", description = "Generate simulated user response based on conversation history and target intent")
    public ResponseEntity<DataResponseDTO<GenerateUserResponseResDTO>> generateUserResponse(@RequestBody GenerateUserResponseReqDTO req) {
        if (req.getTargetIntent() == null || req.getTargetIntent().trim().isEmpty()) {
            return ResponseEntity.badRequest().body(new DataResponseDTO<>(400, "Thiếu thông tin targetIntent", null));
        }

        try {
            GenerateUserResponseResDTO result = openAIService.generateUserResponse(req);
            return success(new DataResponseDTO<>(CodeDefine.OK, "Thành công", result));
        } catch (Exception e) {
            log.error("Error generating user response", e);
            return ResponseEntity.status(500).body(new DataResponseDTO<>(500, "Lỗi khi generate user response: " + e.getMessage(), null));
        }
    }

    @PostMapping("/execute-full-test")
    @Operation(summary = "Execute full test lesson workflow", description = "Run complete automated test of lesson workflow with conversation simulation")
    public ResponseEntity<DataResponseDTO<FullTestLessonResDTO>> executeFullTestLesson(@RequestBody FullTestLessonReqDTO req) {
        if (req.getBotId() == null) {
            return ResponseEntity.badRequest().body(new DataResponseDTO<>(400, "Thiếu thông tin botId", null));
        }

        if (req.getUserId() == null || req.getUserId().trim().isEmpty()) {
            return ResponseEntity.badRequest().body(new DataResponseDTO<>(400, "Thiếu thông tin userId", null));
        }

        try {
            log.info("Starting full test execution for bot ID: {} by user: {}", req.getBotId(), req.getUserId());
            FullTestLessonResDTO result = testLessonService.executeFullTestLesson(req);
            log.info("Full test execution completed. Success rate: {}%",
                    result.getSummary().getSuccessRatePercentage());
            return success(new DataResponseDTO<>(CodeDefine.OK, "Thành công", result));
        } catch (Exception e) {
            log.error("Error executing full test lesson for bot ID: {}", req.getBotId(), e);
            return ResponseEntity.status(500).body(new DataResponseDTO<>(500, "Lỗi khi execute full test lesson: " + e.getMessage(), null));
        }
    }

    @PostMapping("/execute-single-random-test")
    @Operation(summary = "Execute single random test with random intents", description = "Run single test execution with randomly selected intents from available workflows")
    public ResponseEntity<DataResponseDTO<SingleRandomTestResDTO>> executeSingleRandomTest(@RequestBody SingleRandomTestReqDTO req) {
        if (req.getBotId() == null) {
            return ResponseEntity.badRequest().body(new DataResponseDTO<>(400, "Thiếu thông tin botId", null));
        }

        if (req.getUserId() == null || req.getUserId().trim().isEmpty()) {
            return ResponseEntity.badRequest().body(new DataResponseDTO<>(400, "Thiếu thông tin userId", null));
        }

        try {
            log.info("Starting single random test execution for bot ID: {} by user: {}", req.getBotId(), req.getUserId());
            SingleRandomTestResDTO result = testLessonService.executeSingleRandomTest(req);
            log.info("Single random test execution completed. Status: {}, Intents tested: {}",
                    result.getFinalStatus(), result.getRandomIntentsSelected().size());
            return success(new DataResponseDTO<>(CodeDefine.OK, "Thành công", result));
        } catch (Exception e) {
            log.error("Error executing single random test for bot ID: {}", req.getBotId(), e);
            return ResponseEntity.status(500).body(new DataResponseDTO<>(500, "Lỗi khi execute single random test: " + e.getMessage(), null));
        }
    }

    @PostMapping("/execute-single-random-test-async")
    @Operation(summary = "Execute single random test async", description = "Trigger async single random test execution with webhook notification")
    public ResponseEntity<DataResponseDTO<String>> executeSingleRandomTestAsync(@RequestParam Long botId) {
        if (botId == null) {
            return ResponseEntity.badRequest().body(new DataResponseDTO<>(400, "Thiếu thông tin botId", null));
        }

        try {
            log.info("Triggering async single random test execution for bot ID: {}", botId);
            testExecutionService.executeSingleRandomTestAsync(botId);
            return success(new DataResponseDTO<>(CodeDefine.OK, "Đã trigger single random test, kết quả sẽ được gửi qua webhook", "OK"));
        } catch (Exception e) {
            log.error("Error triggering async single random test for bot ID: {}", botId, e);
            return ResponseEntity.status(500).body(new DataResponseDTO<>(500, "Lỗi khi trigger async single random test: " + e.getMessage(), null));
        }
    }

    @GetMapping("/bots")
    @Operation(summary = "Get bot list from external API", description = "Fetch list of available bots from external API")
    public ResponseEntity<DataResponseDTO<BotListResDTO>> getBotList() {
        try {
            String url = "https://ai-tools-api.hacknao.edu.vn:19404/robot-ai-lesson/api/v1/database/listBot";
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("Failed to fetch bot list. Response code: {}", response.code());
                    return ResponseEntity.status(response.code()).body(new DataResponseDTO<>(response.code(), "Không thể lấy danh sách bot từ external API", null));
                }

                String responseBody = response.body().string();
                BotListResDTO botList = objectMapper.readValue(responseBody, BotListResDTO.class);

                return success(new DataResponseDTO<>(CodeDefine.OK, "Thành công", botList));
            }
        } catch (IOException e) {
            log.error("Error fetching bot list", e);
            return ResponseEntity.status(500).body(new DataResponseDTO<>(500, "Lỗi khi lấy danh sách bot: " + e.getMessage(), null));
        } catch (Exception e) {
            log.error("Unexpected error while fetching bot list", e);
            return ResponseEntity.status(500).body(new DataResponseDTO<>(500, "Lỗi không xác định: " + e.getMessage(), null));
        }
    }

    @GetMapping("/workflow/{botId}")
    @Operation(summary = "Get workflow by bot ID", description = "Fetch workflow data for a specific bot from external API")
    public ResponseEntity<DataResponseDTO<WorkflowResDTO>> getWorkflowByBotId(@PathVariable Long botId) {
        try {
            String url = "https://ai-tools-api.hacknao.edu.vn:19403/robot-ai-workflow/api/v1/database/getDataBot?bot_id=" + botId;
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("Failed to fetch workflow for botId: {}. Response code: {}", botId, response.code());
                    return ResponseEntity.status(response.code()).body(new DataResponseDTO<>(response.code(), "Không thể lấy workflow cho bot ID: " + botId, null));
                }

                String responseBody = response.body().string();
                WorkflowResDTO workflow = objectMapper.readValue(responseBody, WorkflowResDTO.class);

                return success(new DataResponseDTO<>(CodeDefine.OK, "Thành công", workflow));
            }
        } catch (IOException e) {
            log.error("Error fetching workflow for botId: {}", botId, e);
            return ResponseEntity.status(500).body(new DataResponseDTO<>(500, "Lỗi khi lấy workflow cho bot ID " + botId + ": " + e.getMessage(), null));
        } catch (Exception e) {
            log.error("Unexpected error while fetching workflow for botId: {}", botId, e);
            return ResponseEntity.status(500).body(new DataResponseDTO<>(500, "Lỗi không xác định: " + e.getMessage(), null));
        }
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        return request.getRemoteAddr();
    }
}
