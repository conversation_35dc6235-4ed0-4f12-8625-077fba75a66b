package com.stepup.springrobot.controller.robot;

import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.controller.BaseController;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.robot_manufacture.CreateRobotManufactureReqDTO;
import com.stepup.springrobot.dto.robot_manufacture.RobotManufactureDTO;
import com.stepup.springrobot.model.robot.RobotManufacture;
import com.stepup.springrobot.service.robot.RobotManufactureService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@CrossOrigin("*")
@RequestMapping("**/api/v1/robot-manufacture")
@RequiredArgsConstructor
@Tag(name = "Robot Manufacture", description = "API for managing robot manufacturing process")
public class RobotManufactureController extends BaseController {

    private final RobotManufactureService robotManufactureService;

    // Create a new robot manufacture entry
    @Operation(summary = "Create a new robot manufacture entry", description = "Creates a new robot manufacture entry in the system")
    @PostMapping
    public ResponseEntity<?> createRobotManufacture(@Valid @RequestBody CreateRobotManufactureReqDTO request) {
        try {
            RobotManufactureDTO result = robotManufactureService.createRobotManufacture(request);

            return success(new DataResponseDTO<>(CodeDefine.OK, "Robot manufacture created successfully", result));
        } catch (Exception e) {
            log.error("Error creating robot manufacture: {}", e.getMessage(), e);
            DataResponseDTO<Object> errorResponse = new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Failed to create robot manufacture: " + e.getMessage());
            return success(errorResponse);
        }
    }

    // Get robot manufacture by ID
    @Operation(summary = "Get robot manufacture by ID", description = "Retrieves a robot manufacture entry by its ID")
    @GetMapping("/{id}")
    public ResponseEntity<?> getRobotManufactureById(
            @Parameter(description = "Robot manufacture ID") @PathVariable String id) {
        try {
            RobotManufactureDTO result = robotManufactureService.getRobotManufactureById(id);

            if (result == null) {
                return success(new DataResponseDTO<>(CodeDefine.FILE_NOT_FOUND, "Robot manufacture not found"));
            }

            return success(new DataResponseDTO<>(CodeDefine.OK, "Robot manufacture retrieved successfully", result));
        } catch (Exception e) {
            log.error("Error getting robot manufacture by ID: {}", e.getMessage(), e);
            DataResponseDTO<Object> errorResponse = new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Failed to get robot manufacture: " + e.getMessage());
            return success(errorResponse);
        }
    }

    // Get robot manufacture by device serial number
    @Operation(summary = "Get robot manufacture by device serial number", description = "Retrieves a robot manufacture entry by device serial number")
    @GetMapping("/device-serial/{deviceSerialNumber}")
    public ResponseEntity<?> getRobotManufactureByDeviceSerial(
            @Parameter(description = "Device serial number") @PathVariable String deviceSerialNumber) {
        try {
            RobotManufactureDTO result = robotManufactureService.getRobotManufactureByDeviceSerial(deviceSerialNumber);

            if (result == null) {
                return success(new DataResponseDTO<>(CodeDefine.FILE_NOT_FOUND, "Robot manufacture not found with device serial: " + deviceSerialNumber));
            }

            return success(new DataResponseDTO<>(CodeDefine.OK, "Robot manufacture retrieved successfully", result));
        } catch (Exception e) {
            log.error("Error getting robot manufacture by device serial: {}", e.getMessage(), e);
            DataResponseDTO<Object> errorResponse = new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Failed to get robot manufacture: " + e.getMessage());
            return success(errorResponse);
        }
    }

    // Get robot manufactures by batch number
    @Operation(summary = "Get robot manufactures by batch number", description = "Retrieves all robot manufacture entries for a specific batch")
    @GetMapping("/batch/{batchNumber}")
    public ResponseEntity<?> getRobotManufacturesByBatchNumber(
            @Parameter(description = "Batch number") @PathVariable String batchNumber) {
        try {
            List<RobotManufactureDTO> result = robotManufactureService.getRobotManufacturesByBatchNumber(batchNumber);

            return success(new DataResponseDTO<>(CodeDefine.OK, "Robot manufactures retrieved successfully", result));
        } catch (Exception e) {
            log.error("Error getting robot manufactures by batch number: {}", e.getMessage(), e);
            DataResponseDTO<Object> errorResponse = new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Failed to get robot manufactures: " + e.getMessage());
            return success(errorResponse);
        }
    }

    // Get robot manufactures by status
    @Operation(summary = "Get robot manufactures by status", description = "Retrieves all robot manufacture entries with a specific status")
    @GetMapping("/status/{status}")
    public ResponseEntity<?> getRobotManufacturesByStatus(
            @Parameter(description = "Manufacturing status") @PathVariable RobotManufacture.ManufacturingStatus status) {
        try {
            List<RobotManufactureDTO> result = robotManufactureService.getRobotManufacturesByStatus(status);

            return success(new DataResponseDTO<>(CodeDefine.OK, "Robot manufactures retrieved successfully", result));
        } catch (Exception e) {
            log.error("Error getting robot manufactures by status: {}", e.getMessage(), e);
            DataResponseDTO<Object> errorResponse = new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Failed to get robot manufactures: " + e.getMessage());
            return success(errorResponse);
        }
    }

    // Get all robot manufactures
    @Operation(summary = "Get all robot manufactures", description = "Retrieves all robot manufacture entries")
    @GetMapping
    public ResponseEntity<?> getAllRobotManufactures() {
        try {
            List<RobotManufactureDTO> result = robotManufactureService.getAllRobotManufactures();

            return success(new DataResponseDTO<>(CodeDefine.OK, "All robot manufactures retrieved successfully", result));
        } catch (Exception e) {
            log.error("Error getting all robot manufactures: {}", e.getMessage(), e);
            DataResponseDTO<Object> errorResponse = new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Failed to get robot manufactures: " + e.getMessage());
            return success(errorResponse);
        }
    }

    // Delete robot manufacture
    @Operation(summary = "Delete robot manufacture", description = "Deletes a robot manufacture entry")
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteRobotManufacture(
            @Parameter(description = "Robot manufacture ID") @PathVariable String id) {
        try {
            boolean isDeleted = robotManufactureService.deleteRobotManufacture(id);

            if (!isDeleted) {
                return success(new DataResponseDTO<>(CodeDefine.FILE_NOT_FOUND, "Robot manufacture not found"));
            }

            return success(new DataResponseDTO<>(CodeDefine.OK, "Robot manufacture deleted successfully"));
        } catch (Exception e) {
            log.error("Error deleting robot manufacture: {}", e.getMessage(), e);
            DataResponseDTO<Object> errorResponse = new DataResponseDTO<>(CodeDefine.ERR_COMMON, "Failed to delete robot manufacture: " + e.getMessage());
            return success(errorResponse);
        }
    }
} 