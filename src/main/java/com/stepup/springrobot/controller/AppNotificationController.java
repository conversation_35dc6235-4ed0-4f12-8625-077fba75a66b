package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.notification.*;
import com.stepup.springrobot.model.user.User;
import com.stepup.springrobot.repository.auth.UserRepository;
import com.stepup.springrobot.service.ParentDashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@CrossOrigin("*")
@RequestMapping("robot-user/api/v1/notifications")
@Tag(name = "App Notifications", description = "API for sending parent dashboard notifications")
@Slf4j
public class AppNotificationController extends BaseController {

    private final ParentDashboardService parentDashboardService;
    private final UserRepository userRepository;

    @Autowired
    public AppNotificationController(ParentDashboardService parentDashboardService, UserRepository userRepository) {
        this.parentDashboardService = parentDashboardService;
        this.userRepository = userRepository;
    }

    /**
     * Get user ID by phone number
     * @param phone Phone number to lookup
     * @return User ID if found
     * @throws RuntimeException if user not found
     */
    private String getUserIdByPhone(String phone) {
        User user = userRepository.findByPhone(phone);
        if (user == null) {
            throw new RuntimeException("User not found with phone: " + phone);
        }
        return user.getId();
    }

    /**
     * Send notification when child hasn't started learning today
     */
    @PostMapping("/child-not-started-learning")
    @Operation(summary = "Gửi thông báo khi quá 15p giờ học nhưng chưa học")
    public ResponseEntity<?> sendChildNotStartedLearning(@Valid @RequestBody StudyTimeReminderReqDTO requestDTO) {
        try {
            String userId = getUserIdByPhone(requestDTO.getTargetPhone());
            parentDashboardService.sendChildNotStartedLearning(userId, requestDTO.getLessonId());
            return success();
        } catch (Exception e) {
            log.error("Error sending study time reminder notification", e);
            return serverError(null);
        }
    }

    /**
     * Send study time reminder notification
     */
    @PostMapping("/study-time-reminder")
    @Operation(summary = "Gửi thông báo đã đến giờ học")
    public ResponseEntity<?> sendStudyTimeReminder(@Valid @RequestBody NotificationReqDTO requestDTO) {
        try {
            String userId = getUserIdByPhone(requestDTO.getTargetPhone());
            parentDashboardService.sendStudyTimeReminder(userId);
            return success();
        } catch (Exception e) {
            log.error("Error sending child not started learning notification", e);
            return serverError(null);
        }
    }

    /**
     * Send lesson completed notification
     */
    @PostMapping("/lesson-completed")
    @Operation(summary = "Gửi thông báo hoàn thành bài học", description = "Gửi thông báo cho phụ huynh khi con hoàn thành bài học với lesson ID")
    public ResponseEntity<?> sendLessonCompletedNotification(@Valid @RequestBody LessonCompletedReqDTO requestDTO) {
        try {
            String userId = getUserIdByPhone(requestDTO.getTargetPhone());
            parentDashboardService.sendLessonCompletedNotification(userId, requestDTO.getLessonId());
            return success();
        } catch (Exception e) {
            log.error("Error sending lesson completed notification", e);
            return serverError(null);
        }
    }

    /**
     * Send incomplete lesson notification
     */
    @PostMapping("/incomplete-lesson")
    @Operation(summary = "Gửi thông báo bài học chưa hoàn thành", description = "Gửi thông báo cho phụ huynh khi bài học còn dang dở")
    public ResponseEntity<?> sendIncompleteLessonNotification(@Valid @RequestBody NotificationReqDTO requestDTO) {
        try {
            String userId = getUserIdByPhone(requestDTO.getTargetPhone());
            parentDashboardService.sendIncompleteLessonNotification(userId);
            return success();
        } catch (Exception e) {
            log.error("Error sending incomplete lesson notification", e);
            return serverError(null);
        }
    }

    /**
     * Send pronunciation issue notification
     */
    @PostMapping("/pronunciation-issue")
    @Operation(summary = "Gửi thông báo phát âm cần cải thiện", description = "Gửi thông báo cho phụ huynh khi phát âm của con cần cải thiện với lesson ID")
    public ResponseEntity<?> sendPronunciationIssueNotification(@Valid @RequestBody PronunciationIssueReqDTO requestDTO) {
        try {
            String userId = getUserIdByPhone(requestDTO.getTargetPhone());
            parentDashboardService.sendPronunciationIssueNotification(userId, requestDTO.getLessonId());
            return success();
        } catch (Exception e) {
            log.error("Error sending pronunciation issue notification", e);
            return serverError(null);
        }
    }

    /**
     * Send three days absence notification
     */
    @PostMapping("/three-days-absence")
    @Operation(summary = "Gửi thông báo 3 ngày không học", description = "Gửi thông báo cho phụ huynh khi con không học 3 ngày liên tiếp")
    public ResponseEntity<?> sendThreeDaysAbsenceNotification(@Valid @RequestBody NotificationReqDTO requestDTO) {
        try {
            String userId = getUserIdByPhone(requestDTO.getTargetPhone());
            parentDashboardService.sendThreeDaysAbsenceNotification(userId);
            return success();
        } catch (Exception e) {
            log.error("Error sending three days absence notification", e);
            return serverError(null);
        }
    }

    /**
     * Send weekly study report notification
     */
    @PostMapping("/weekly-study-report")
    @Operation(summary = "Gửi báo cáo học tập tuần", description = "Gửi báo cáo học tập hàng tuần cho phụ huynh")
    public ResponseEntity<?> sendWeeklyStudyReport(@Valid @RequestBody NotificationReqDTO requestDTO) {
        try {
            String userId = getUserIdByPhone(requestDTO.getTargetPhone());
            parentDashboardService.sendWeeklyStudyReport(userId);
            return success();
        } catch (Exception e) {
            log.error("Error sending weekly study report notification", e);
            return serverError(null);
        }
    }

    /**
     * Send custom study reminder notification
     */
    @PostMapping("/custom-study-reminder")
    @Operation(summary = "Gửi thông báo nhắc nhở tùy chỉnh", description = "Gửi thông báo nhắc nhở với nội dung tùy chỉnh và lesson ID")
    public ResponseEntity<?> sendCustomStudyReminder(@Valid @RequestBody CustomStudyReminderReqDTO requestDTO) {
        try {
            String userId = getUserIdByPhone(requestDTO.getTargetPhone());
            parentDashboardService.sendCustomStudyReminder(
                    userId,
                    requestDTO.getTitle(),
                    requestDTO.getCustomMessage(),
                    requestDTO.getLessonId()
            );
            return success();
        } catch (Exception e) {
            log.error("Error sending custom study reminder notification", e);
            return serverError(null);
        }
    }
} 