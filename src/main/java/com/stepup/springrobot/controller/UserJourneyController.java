package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.monitoring.UserErrorDTO;
import com.stepup.springrobot.dto.monitoring.UserJourneyDTO;
import com.stepup.springrobot.service.UserErrorTracker;
import com.stepup.springrobot.service.UserJourneyTracker;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/monitoring/user-journey")
@Tag(name = "User Journey Monitoring", description = "APIs for tracking and analyzing user journeys and errors")
@Slf4j
public class UserJourneyController {

    @Autowired
    private UserJourneyTracker userJourneyTracker;

    @Autowired
    private UserErrorTracker userErrorTracker;

    @GetMapping("/history/{userId}")
    @Operation(summary = "Get user journey history",
            description = "Retrieve the journey history for a specific user")
    public ResponseEntity<Map<String, Object>> getUserJourneyHistory(
            @Parameter(description = "User ID", required = true)
            @PathVariable String userId,

            @Parameter(description = "Maximum number of entries to return", required = false)
            @RequestParam(defaultValue = "50") int limit) {

        try {
            List<UserJourneyDTO> journeyHistory = userJourneyTracker.getUserJourneyHistory(userId, limit);

            Map<String, Object> response = new HashMap<>();
            response.put("user_id", userId);
            response.put("total_entries", journeyHistory.size());
            response.put("journey_history", journeyHistory);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error retrieving user journey history for userId: {}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "Failed to retrieve user journey history",
                            "message", e.getMessage()));
        }
    }

    @GetMapping("/errors/{userId}")
    @Operation(summary = "Get user error history",
            description = "Retrieve the error history for a specific user")
    public ResponseEntity<Map<String, Object>> getUserErrorHistory(
            @Parameter(description = "User ID", required = true)
            @PathVariable String userId,

            @Parameter(description = "Maximum number of entries to return", required = false)
            @RequestParam(defaultValue = "20") int limit) {

        try {
            List<UserErrorDTO> errorHistory = userErrorTracker.getUserErrorHistory(userId, limit);

            Map<String, Object> response = new HashMap<>();
            response.put("user_id", userId);
            response.put("total_errors", errorHistory.size());
            response.put("error_history", errorHistory);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error retrieving user error history for userId: {}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "Failed to retrieve user error history",
                            "message", e.getMessage()));
        }
    }

    @GetMapping("/errors/statistics/{userId}")
    @Operation(summary = "Get user error statistics",
            description = "Get comprehensive error statistics for a specific user")
    public ResponseEntity<Map<String, Object>> getUserErrorStatistics(
            @Parameter(description = "User ID", required = true)
            @PathVariable String userId,

            @Parameter(description = "Number of days to analyze", required = false)
            @RequestParam(defaultValue = "7") int daysPeriod) {

        try {
            UserErrorTracker.UserErrorStatistics statistics =
                    userErrorTracker.getUserErrorStatistics(userId, daysPeriod);

            Map<String, Object> response = new HashMap<>();
            response.put("user_id", userId);
            response.put("analysis_period_days", daysPeriod);
            response.put("statistics", statistics);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error retrieving user error statistics for userId: {}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "Failed to retrieve user error statistics",
                            "message", e.getMessage()));
        }
    }

    @GetMapping("/active/{userId}")
    @Operation(summary = "Get active user journey",
            description = "Get currently active journey for a user")
    public ResponseEntity<Map<String, Object>> getActiveUserJourney(
            @Parameter(description = "User ID", required = true)
            @PathVariable String userId,

            @Parameter(description = "Session ID", required = false)
            @RequestParam(required = false) String sessionId) {

        try {
            UserJourneyTracker.UserJourneyContext activeJourney =
                    userJourneyTracker.getActiveJourney(userId, sessionId != null ? sessionId : "unknown");

            Map<String, Object> response = new HashMap<>();
            response.put("user_id", userId);
            response.put("session_id", sessionId);
            response.put("has_active_journey", activeJourney != null);

            if (activeJourney != null) {
                response.put("active_journey", activeJourney);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error retrieving active user journey for userId: {}", userId, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "Failed to retrieve active user journey",
                            "message", e.getMessage()));
        }
    }

    @GetMapping("/errors/patterns")
    @Operation(summary = "Get error patterns",
            description = "Get common error patterns across all users")
    public ResponseEntity<Map<String, Object>> getErrorPatterns(
            @Parameter(description = "Minimum occurrences to include pattern", required = false)
            @RequestParam(defaultValue = "3") int minOccurrences) {

        try {
            List<UserErrorTracker.ErrorPattern> errorPatterns =
                    userErrorTracker.getErrorPatterns(minOccurrences);

            Map<String, Object> response = new HashMap<>();
            response.put("min_occurrences", minOccurrences);
            response.put("total_patterns", errorPatterns.size());
            response.put("error_patterns", errorPatterns);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error retrieving error patterns", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "Failed to retrieve error patterns",
                            "message", e.getMessage()));
        }
    }

    @PostMapping("/errors/{errorId}/resolve")
    @Operation(summary = "Mark error as resolved",
            description = "Manually mark an error as resolved with resolution details")
    public ResponseEntity<Map<String, Object>> resolveError(
            @Parameter(description = "Error ID", required = true)
            @PathVariable String errorId,

            @RequestBody Map<String, Object> resolutionDetails) {

        try {
            String resolutionMethod = (String) resolutionDetails.getOrDefault("resolution_method", "MANUAL");
            String details = (String) resolutionDetails.getOrDefault("details", "Manually resolved");
            Map<String, Object> contextAfterError =
                    (Map<String, Object>) resolutionDetails.getOrDefault("context_after_error", new HashMap<>());

            userErrorTracker.trackErrorResolution(errorId, resolutionMethod, true, details, contextAfterError);

            Map<String, Object> response = new HashMap<>();
            response.put("error_id", errorId);
            response.put("resolution_status", "success");
            response.put("resolution_method", resolutionMethod);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error resolving error with ID: {}", errorId, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "Failed to resolve error",
                            "message", e.getMessage()));
        }
    }

    @PostMapping("/journey/start")
    @Operation(summary = "Start user journey tracking",
            description = "Manually start tracking a user journey")
    public ResponseEntity<Map<String, Object>> startUserJourney(
            @RequestBody Map<String, Object> journeyRequest) {

        try {
            String userId = (String) journeyRequest.get("user_id");
            String sessionId = (String) journeyRequest.get("session_id");
            String journeyTypeStr = (String) journeyRequest.getOrDefault("journey_type", "CONVERSATION");
            String robotId = (String) journeyRequest.get("robot_id");
            Map<String, Object> initialContext =
                    (Map<String, Object>) journeyRequest.getOrDefault("initial_context", new HashMap<>());

            UserJourneyDTO.JourneyType journeyType = UserJourneyDTO.JourneyType.valueOf(journeyTypeStr);

            String journeyId = userJourneyTracker.startUserJourney(userId, sessionId, journeyType, robotId, initialContext);

            Map<String, Object> response = new HashMap<>();
            response.put("journey_id", journeyId);
            response.put("user_id", userId);
            response.put("session_id", sessionId);
            response.put("journey_type", journeyType);
            response.put("status", "started");
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error starting user journey", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "Failed to start user journey",
                            "message", e.getMessage()));
        }
    }

    @PostMapping("/journey/{journeyId}/end")
    @Operation(summary = "End user journey tracking",
            description = "Manually end tracking a user journey")
    public ResponseEntity<Map<String, Object>> endUserJourney(
            @Parameter(description = "Journey ID", required = true)
            @PathVariable String journeyId,

            @RequestBody Map<String, Object> endRequest) {

        try {
            boolean success = (Boolean) endRequest.getOrDefault("success", true);
            String reason = (String) endRequest.getOrDefault("reason", "Manual completion");

            userJourneyTracker.endUserJourney(journeyId, success, reason);

            Map<String, Object> response = new HashMap<>();
            response.put("journey_id", journeyId);
            response.put("status", "ended");
            response.put("success", success);
            response.put("reason", reason);
            response.put("timestamp", System.currentTimeMillis());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Error ending user journey: {}", journeyId, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "Failed to end user journey",
                            "message", e.getMessage()));
        }
    }
}
