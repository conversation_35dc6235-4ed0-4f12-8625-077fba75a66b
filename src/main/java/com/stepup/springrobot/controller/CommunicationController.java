package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.communication.WifiConnectReqDTO;
import com.stepup.springrobot.service.TestExecutionService;
import com.stepup.springrobot.service.communication.ConnectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/communication")
public class CommunicationController extends BaseController {
    @Autowired
    private ConnectService connectService;

    @Autowired
    private TestExecutionService testExecutionService;

    @GetMapping("/wifi/scan")
    public ResponseEntity<?> scanWifi(HttpServletRequest request, @RequestParam("robot_id") String robotId) throws IOException {
        return success(connectService.scanWifi(request, robotId));
    }

    @GetMapping("/wifi/scan_result")
    public ResponseEntity<?> getScanWifiResult(HttpServletRequest request, @RequestParam("request_id") String requestId) throws IOException {
        return success(connectService.getScanWifiResult(request, requestId));
    }

    @PostMapping("/wifi/connect")
    public ResponseEntity<?> connectWifi(HttpServletRequest request, @Valid @RequestBody WifiConnectReqDTO connectReqDTO) throws IOException {
        return success(connectService.connectWifi(request, connectReqDTO));
    }

    @GetMapping("/wifi/connect_result")
    public ResponseEntity<?> getConnectWifiResult(HttpServletRequest request, @RequestParam("request_id") String requestId) throws IOException {
        return success(connectService.getConnectWifiResult(request, requestId));
    }

    @GetMapping("/firmware/update")
    public ResponseEntity<?> updateFirmware(HttpServletRequest request, @RequestParam("robot_id") String robotId) throws IOException {
        return success(connectService.updateFirmware(request, robotId));
    }

    @GetMapping("/firmware/update_result")
    public ResponseEntity<?> getUpdateWifiResult(HttpServletRequest request, @RequestParam("robot_id") String robotId) throws IOException {
        return success(connectService.getUpdateFirmwareResult(request, robotId));
    }

    @GetMapping("/execute")
    public ResponseEntity<?> executeTest(@RequestParam("bot_id") Long botId) {
        testExecutionService.executeTestAsync(botId);
        return success("Started test asynchronously for bot_id=" + botId);
    }
}
