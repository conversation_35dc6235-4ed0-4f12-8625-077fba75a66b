package com.stepup.springrobot.controller.web;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.controller.BaseController;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.admin.ConversationReportContentDTO;
import com.stepup.springrobot.model.chat.RobotUserConversation;
import com.stepup.springrobot.model.chat.RobotUserConversationRecordHistory;
import com.stepup.springrobot.service.AdminService;
import com.stepup.springrobot.service.communication.RobotReportInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Controller
@CrossOrigin("*")
@RequestMapping("web/admin")
public class AdminWebController extends BaseController {
    @Autowired
    private AdminService adminService;

    @Autowired
    private RobotReportInfoService robotReportInfoService;

    @GetMapping("/conversations")
    public ModelAndView listConversations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(required = false) Long id,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) Long botId,
            @RequestParam(required = false) String conversationSource,
            @RequestParam(required = false) Boolean hasAsrQc) {

        Page<RobotUserConversation> conversations = adminService
                .findAllWithFilters(id, phone, userId, startDate, endDate, botId, keyword, conversationSource, hasAsrQc, PageRequest.of(page, 10));

        ModelAndView mav = new ModelAndView("conversations/list");
        mav.addObject("conversations", conversations);
        mav.addObject("id", id);
        mav.addObject("phone", phone);
        mav.addObject("userId", userId);
        mav.addObject("startDate", startDate);
        mav.addObject("endDate", endDate);
        mav.addObject("botId", botId);
        mav.addObject("keyword", keyword);
        mav.addObject("conversationSource", conversationSource);
        mav.addObject("hasAsrQc", hasAsrQc);
        return mav;
    }

    @GetMapping("/conversations/{id}")
    public ModelAndView conversationDetail(@PathVariable Long id,
                                           @RequestParam(defaultValue = "0") int page) {
        RobotUserConversation conversation = adminService.findConversationById(id);
        List<RobotUserConversationRecordHistory> messages = adminService
                .findByRobotUserConversationIdOrderByIdAsc(id, PageRequest.of(page, 200));

        ModelAndView mav = new ModelAndView("conversations/detail");
        mav.addObject("conversation", conversation);
        mav.addObject("messages", messages);
        mav.addObject("likes",
                messages.stream().map(RobotUserConversationRecordHistory::getLikes).reduce(0, Integer::sum));
        mav.addObject("dislikes",
                messages.stream().map(RobotUserConversationRecordHistory::getDislikes).reduce(0, Integer::sum));
        return mav;
    }

    @GetMapping("/conversations/{id}/messages")
    @ResponseBody
    public Page<RobotUserConversationRecordHistory> loadMoreMessages(
            @PathVariable Long id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(required = false) Long afterId) {
        if (afterId != null) {
            return adminService.findByRobotUserConversationIdAndIdGreaterThanOrderByIdAsc(id, afterId,
                    PageRequest.of(0, 200));
        }

        return Page.empty();
    }

    @GetMapping("/dashboard")
    public String adminDashboard() {
        return "admin/admin"; // This maps to admin.html in templates/admin folder
    }

    @GetMapping("/upload")
    public String showUploadForm(Model model) {
        return "admin/upload";
    }

    @GetMapping("/face_demo")
    public String showFaceDemo(Model model) {
        return "admin/face_demo";
    }

    @GetMapping("/settings")
    public String showRobotSettings(Model model) {
        return "admin/settings";
    }

    @GetMapping("/study_settings")
    public String showStudySettings(Model model) {
        return "admin/study_settings";
    }

    @GetMapping("/metrics")
    public String metricsPage() {
        return "admin/metric/metrics";
    }

    @GetMapping("/metrics/sentry")
    public String sentryPage() {
        return "admin/metric/sentry";
    }

    @GetMapping("/metrics/robot")
    public String robotLogPage() {
        return "admin/metric/robot_log";
    }

    @GetMapping("/log_robot")
    public String logRobotPage() {
        return "admin/log_robot";
    }

    @GetMapping("/conversations/{id}/report/view")
    public ModelAndView viewConversationReport(@PathVariable Long id) {
        ConversationReportContentDTO reportData = adminService.getConversationReportContentById(id, null);

        ModelAndView mav = new ModelAndView("conversations/report");
        mav.addObject("conversationId", id);
        mav.addObject("reportData", reportData);
        return mav;
    }

    @GetMapping("/conversations/{id}/asr-qc")
    @ResponseBody
    public ResponseEntity<?> getAsrQcData(@PathVariable Long id) {
        return success(adminService.getAsrQcData(id));
    }

    @PostMapping("/conversations/{id}/download-user-messages")
    public ResponseEntity<byte[]> downloadUserMessages(@PathVariable Long id) {
        try {
            byte[] excelBytes = adminService.generateUserMessagesExcel(id);

            // Set up response headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "user_messages_" + id + ".xlsx");

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelBytes);

        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/conversation/report")
    public ModelAndView conversationReport(@RequestParam("messagesInput") String messagesInputJson) {
        List<String> messagesInput;
        try {
            // Convert JSON string to List<String>
            ObjectMapper objectMapper = new ObjectMapper();
            messagesInput = objectMapper.readValue(messagesInputJson, new TypeReference<List<String>>() {
            });
        } catch (Exception e) {
            // Handle parsing error
            ModelAndView errorView = new ModelAndView("error");
            errorView.addObject("message", "Error parsing messages: " + e.getMessage());
            return errorView;
        }

        ConversationReportContentDTO reportData = adminService.getConversationReportContentById(0L, messagesInput);

        ModelAndView mav = new ModelAndView("conversations/report");
        mav.addObject("conversationId", 0L);
        mav.addObject("reportData", reportData);
        return mav;
    }

    @PostMapping("/conversations/messages/{messageId}/pronunciation-score")
    @ResponseBody
    public ResponseEntity<?> generatePronunciationScore(@PathVariable Long messageId) {
        try {
            JsonNode score = adminService.getPronunciationScoreForSentenceById(messageId);
            if (score != null) {
                return success(new DataResponseDTO<>(200, "Success", score));
            } else {
                return ResponseEntity.badRequest()
                        .body(new DataResponseDTO<>(400, "Failed to generate pronunciation score", null));
            }
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new DataResponseDTO<>(500, "Error generating pronunciation score: " + e.getMessage(), null));
        }
    }

    @PostMapping("/conversations/messages/regenerate-score")
    @ResponseBody
    public ResponseEntity<?> regeneratePronunciationScore(@RequestBody Map<String, String> request) {
        try {
            String text = request.get("text");
            String sentenceIdStr = request.get("sentenceId");

            if (text == null || text.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new DataResponseDTO<>(400, "Text input is required", null));
            }

            if (sentenceIdStr == null || sentenceIdStr.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new DataResponseDTO<>(400, "Sentence ID is required", null));
            }

            Long sentenceId;
            try {
                sentenceId = Long.parseLong(sentenceIdStr);
            } catch (NumberFormatException e) {
                return ResponseEntity.badRequest()
                        .body(new DataResponseDTO<>(400, "Invalid sentence ID format", null));
            }

            JsonNode score = adminService.getPronunciationScoreForText(sentenceId, text);
            if (score != null) {
                return success(new DataResponseDTO<>(200, "Success", score));
            } else {
                return ResponseEntity.badRequest()
                        .body(new DataResponseDTO<>(400, "Failed to generate pronunciation score", null));
            }
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new DataResponseDTO<>(500, "Error generating pronunciation score: " + e.getMessage(), null));
        }
    }

    @PostMapping("/conversations/messages/{messageId}/asr-qc")
    @ResponseBody
    public ResponseEntity<?> updateAsrQcForMessage(@PathVariable Long messageId,
                                                   @RequestBody com.stepup.springrobot.dto.speech.AsrQcUpdateRequestDTO request) {
        try {
            request.setMessageId(messageId);
            return success(adminService.updateMessageAsrQc(request));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new DataResponseDTO<>(500, "Error updating ASR QC: " + e.getMessage(), null));
        }
    }

    @GetMapping("/conversations/asr-qc")
    @ResponseBody
    public ResponseEntity<?> getAllUserRecordsForAsrQc(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            return success(adminService.getAllUserRecordsForAsrQc(page, size));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new DataResponseDTO<>(500, "Error retrieving USER records: " + e.getMessage(), null));
        }
    }
}