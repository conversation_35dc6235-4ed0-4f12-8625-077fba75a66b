package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.study.AssignLessonReqDTO;
import com.stepup.springrobot.service.StudyService;
import com.stepup.springrobot.service.data.ExportStudyDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/study")
public class StudyController extends BaseController {
    @Autowired
    private StudyService studyService;

    @Autowired
    private ExportStudyDataService exportStudyDataService;

    @GetMapping("/plan")
    public ResponseEntity<?> getStudyData(HttpServletRequest request) throws IOException {
        return success(studyService.getStudyPlan(request));
    }

    @GetMapping("/lessons")
    public ResponseEntity<?> getLessonsByTopics(HttpServletRequest request, @RequestParam("topic_id") Long topicId) throws IOException {
        return success(studyService.getLessonsByTopicId(request, topicId));
    }

    @GetMapping("/lesson/result")
    public ResponseEntity<?> getLessonResult(HttpServletRequest request, @RequestParam("lesson_id") Long lessonId) throws IOException {
        return success(studyService.getLessonResult(request, lessonId));
    }

    @GetMapping("/lesson/conversation")
    public ResponseEntity<?> getLessonConversation(HttpServletRequest request, @RequestParam("lesson_id") Long lessonId) throws IOException {
        return success(studyService.getUserLastestLessonConversation(request, lessonId));
    }

    @PutMapping("/lesson/assign")
    public ResponseEntity<?> assignLesson(HttpServletRequest request, @Valid @RequestBody AssignLessonReqDTO reqDTO) throws IOException {
        return success(studyService.assignLesson(request, reqDTO));
    }

    @GetMapping("/export/phone/{phoneNumber}")
    public ResponseEntity<?> exportDataByPhone(@PathVariable String phoneNumber,
                                               @RequestParam("sheet_name") String sheetName) throws IOException {
        exportStudyDataService.exportStudyDataByPhone(phoneNumber, sheetName);
        return success("Data exported successfully for phone: " + phoneNumber + " to sheet: " + sheetName);
    }

    /**
     * Test endpoint to manually trigger the daily export job
     */
    @GetMapping("/export/daily")
    public ResponseEntity<?> testDailyExport() {
        try {
            exportStudyDataService.dailyExportStudyData();
            return success("Daily export job completed successfully");
        } catch (Exception e) {
            return new ResponseEntity<>("Failed to execute daily export job: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Export data tracking time to Google Sheets
     */
    @GetMapping("/export/data-tracking-time")
    public ResponseEntity<?> exportDataTrackingTime() {
        try {
            exportStudyDataService.exportDataTrackingTime();
            return success("Data tracking time exported successfully to sheet: data_tracking_time");
        } catch (Exception e) {
            return new ResponseEntity<>("Failed to export data tracking time: " + e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get list of completed lesson IDs for today
     */
    @GetMapping("/completed-lessons")
    public ResponseEntity<?> getCompletedLessonsToday(HttpServletRequest request) throws IOException {
        return success(studyService.getCompletedLessons(request));
    }
}
