package com.stepup.springrobot.controller.speech;

import com.stepup.springrobot.controller.BaseController;
import com.stepup.springrobot.dto.speech.AsrQcUpdateRequestDTO;
import com.stepup.springrobot.service.speech.AsrService;
import com.stepup.springrobot.service.speech.CheckSpeechService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot-speech/api/v1/check")
@Tag(name = "Speech Recognition", description = "APIs for speech recognition and wake word detection")
public class SpeechController extends BaseController {
    @Autowired
    private CheckSpeechService checkSpeechService;

    @Autowired
    private AsrService asrService;

    @PostMapping("/speech")
    public ResponseEntity<?> checkSpeech(@RequestParam("audio-file") MultipartFile file,
                                         @RequestParam("text-refs") String textRefs,
                                         HttpServletRequest request) throws IOException {
        return success(checkSpeechService.uploadSpeech(request, file, textRefs));
    }

    /**
     * Nhận diện text từ file audio
     * Giới hạn số lần dùng trong 1 ngày
     */
    @PostMapping("/asr")
    public ResponseEntity<?> getAudioScriptEn(@RequestParam("audio-file") MultipartFile file, HttpServletRequest request) throws Exception {
        return success(asrService.generateSpeechToText(request, file, false));
    }

    @PostMapping(value = "/wake_word", consumes = "application/octet-stream")
    public ResponseEntity<?> detectWakeWord(@RequestBody byte[] audioData) {
        return success(checkSpeechService.detectWakeWordFromBytes(audioData));
    }

    @PostMapping("/asr_qc")
    @Operation(summary = "Update ASR/Intent QC fields for a message")
    public ResponseEntity<?> updateAsrQc(@RequestBody AsrQcUpdateRequestDTO request) {
        return success(checkSpeechService.updateAsrQc(request));
    }
}
