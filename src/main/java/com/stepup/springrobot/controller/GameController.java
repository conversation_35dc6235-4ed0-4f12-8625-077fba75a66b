package com.stepup.springrobot.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stepup.springrobot.dto.game.AlphaBlastSummaryReqDTO;
import com.stepup.springrobot.dto.game.UserBalanceDTO;
import com.stepup.springrobot.dto.game.UserItemsDTO;
import com.stepup.springrobot.dto.game.WordRaceSummaryReqDTO;
import com.stepup.springrobot.service.GameService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@CrossOrigin("*")
@RequestMapping("/robot/api/v1/device/game")
public class GameController extends BaseController {

    @Autowired
    private GameService gameService;

    @GetMapping()
    public ResponseEntity<?> getGamesList(HttpServletRequest request) {
        return success(gameService.getGamesList(request));
    }

    @GetMapping("/echo_tower/init")
    public ResponseEntity<?> getGameData(HttpServletRequest request) throws JsonProcessingException {
        return success(gameService.initGameEchoTowerData(request));
    }

    @GetMapping("/echo_tower/next_floor")
    public ResponseEntity<?> nextGameEchoTowerData(HttpServletRequest request) {
        return success(gameService.nextGameEchoTowerData(request));
    }

    @PostMapping("/echo_tower/check_speech")
    public ResponseEntity<?> echoTowerCheckSpeech(HttpServletRequest request, @RequestParam("word_id") Long wordId, @RequestParam("audio_file") MultipartFile file) throws JsonProcessingException {
        return success(gameService.checkEchoTowerPronounceScore(request, file, wordId));
    }

    @GetMapping("/echo_tower/summary")
    public ResponseEntity<?> echoTowerCheckSpeech(HttpServletRequest request) throws JsonProcessingException {
        return success(gameService.getEchoTowerSummary(request));
    }

    @GetMapping("/word_race/init")
    public ResponseEntity<?> initWordRace(HttpServletRequest request) {
        return success(gameService.initWordRace(request));
    }

    @PostMapping("/word_race/summary")
    public ResponseEntity<?> getWordRaceSummary(HttpServletRequest request, @Valid @RequestBody WordRaceSummaryReqDTO requestDTO) {
        return success(gameService.getWordRaceSummary(request, requestDTO));
    }

    @GetMapping("/alpha_blast/init")
    public ResponseEntity<?> initAlphaBlast(HttpServletRequest request) {
        return success(gameService.initAlphaBlast(request));
    }

    @PostMapping("/alpha_blast/summary")
    public ResponseEntity<?> getAlphaBlastSummary(HttpServletRequest request, @Valid @RequestBody AlphaBlastSummaryReqDTO requestDTO) {
        return success(gameService.getAlphaBlastSummary(request, requestDTO));
    }

    // User balance endpoints
    @GetMapping("/user/balance")
    public ResponseEntity<?> getUserBalance(HttpServletRequest request) {
        return success(gameService.getUserBalance(request));
    }

    @PutMapping("/user/balance")
    public ResponseEntity<?> updateUserBalance(HttpServletRequest request, @Valid @RequestBody UserBalanceDTO requestDTO) {
        return success(gameService.updateUserBalance(request, requestDTO));
    }

    // User items endpoints
    @GetMapping("/user/items")
    public ResponseEntity<?> getUserItems(HttpServletRequest request) {
        return success(gameService.getUserItems(request));
    }

    @PutMapping("/user/items")
    public ResponseEntity<?> updateUserItems(HttpServletRequest request, @Valid @RequestBody UserItemsDTO requestDTO) {
        return success(gameService.updateUserItems(request, requestDTO));
    }
}
