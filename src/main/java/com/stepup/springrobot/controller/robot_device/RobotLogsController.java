package com.stepup.springrobot.controller.robot_device;

import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.controller.BaseController;
import com.stepup.springrobot.model.robot.RobotReportInfo;
import com.stepup.springrobot.model.speech.WakeWordDetection;
import com.stepup.springrobot.repository.speech.WakeWordDetectionRepository;
import com.stepup.springrobot.service.communication.RobotReportInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("robot")
public class RobotLogsController extends BaseController {

    @Autowired
    private RobotReportInfoService robotReportInfoService;

    @Autowired
    private WakeWordDetectionRepository wakeWordDetectionRepository;

    @GetMapping("/logs")
    public ResponseEntity<?> getErrorLogs(
            @RequestParam(value = "robotId", required = false) String robotId,
            @RequestParam(value = "start", required = false) Long startMillis,
            @RequestParam(value = "end", required = false) Long endMillis,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size
    ) {
        java.util.Date start = startMillis != null ? new java.util.Date(startMillis) : null;
        java.util.Date end = endMillis != null ? new java.util.Date(endMillis) : null;
        Page<RobotReportInfo> logs = robotReportInfoService.getErrorLogs(robotId, start, end, PageRequest.of(page, size));
        return ResponseEntity.ok(logs);
    }

    @GetMapping("/logs/wake-words")
    public ResponseEntity<?> getWakeWordAudio(
            @RequestParam(value = "robotId", required = false) String robotId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size
    ) {
        // robotId is reserved for future use when robotId is stored with detections
        PageRequest pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<WakeWordDetection> result = wakeWordDetectionRepository.findAll(pageable);
        java.util.Map<String, Object> body = new java.util.HashMap<>();
        body.put("status", CodeDefine.OK);
        body.put("message", "Success");
        body.put("data", result.getContent());
        return ResponseEntity.ok(body);
    }
}


