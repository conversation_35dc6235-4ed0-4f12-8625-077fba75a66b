package com.stepup.springrobot.controller.robot_device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.controller.BaseController;
import com.stepup.springrobot.dto.chat.ConversationLogReqDTO;
import com.stepup.springrobot.dto.communication.*;
import com.stepup.springrobot.dto.device.MappingRobotToPhoneReqDTO;
import com.stepup.springrobot.dto.device.UnMapRobotReqDTO;
import com.stepup.springrobot.dto.mqtt.MqttAuthenReqDTO;
import com.stepup.springrobot.dto.mqtt.MqttAuthorizeReqDTO;
import com.stepup.springrobot.service.communication.ConnectService;
import com.stepup.springrobot.service.communication.RobotDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/device")
public class RobotDeviceController extends BaseController {
    @Autowired
    private RobotDeviceService robotDeviceService;

    @Autowired
    private ConnectService connectService;

    @PostMapping("/wifi/scan_result")
    public ResponseEntity<?> saveWifiScanResult(HttpServletRequest request, @Valid @RequestBody WifiScanResultReqDTO resultReqDTO) throws IOException {
        return success(robotDeviceService.saveWifiScanResult(request, resultReqDTO));
    }

    @PostMapping("/wifi/connect_result")
    public ResponseEntity<?> saveConnectWifiResult(HttpServletRequest request, @Valid @RequestBody WifiConnectResultDTO resultReqDTO) throws IOException {
        return success(robotDeviceService.saveWifiConnectResult(request, resultReqDTO));
    }

    @PostMapping("/firmware/update_result")
    public ResponseEntity<?> saveUpdateFirmwareResult(HttpServletRequest request, @Valid @RequestBody MqttUpdateResultReqDTO resultReqDTO) throws IOException {
        return success(robotDeviceService.saveUpdateFirmwareResult(request, resultReqDTO));
    }

    @PostMapping("/mqtt/result")
    public ResponseEntity<?> saveMqttResult(HttpServletRequest request, @Valid @RequestBody MqttUpdateResultReqDTO resultReqDTO) throws IOException {
        return success(robotDeviceService.saveMqttRequestResult(request, resultReqDTO));
    }

    @PostMapping("/alarm/result")
    public ResponseEntity<?> saveAlarmResult(HttpServletRequest request, @Valid @RequestBody MqttUpdateResultReqDTO resultReqDTO) throws IOException {
        return success(robotDeviceService.saveAlarmResult(request, resultReqDTO));
    }

    @PostMapping("/trigger_test")
    public ResponseEntity<?> triggerTest(@RequestBody JsonNode data) throws JsonProcessingException {
        return success(connectService.sendTestTriggerMessage(data));
    }

    @GetMapping("/trigger_data")
    public ResponseEntity<?> getTriggerData(@RequestParam("trigger_type") String triggerType) throws JsonProcessingException {
        return success(connectService.getTriggerDataByType(triggerType));
    }

    @PostMapping("/mqtt/authenticate")
    public ResponseEntity<?> mqttAuthenticate(@RequestBody MqttAuthenReqDTO reqDTO) throws JsonProcessingException {
        return new ResponseEntity<>(robotDeviceService.mqttAuthenticate(reqDTO), HttpStatus.OK);
    }

    @PostMapping("/mqtt/authorize")
    public ResponseEntity<?> mqttAuthorize(@RequestBody MqttAuthorizeReqDTO reqDTO) throws JsonProcessingException {
        return new ResponseEntity<>(robotDeviceService.mqttAuthorize(reqDTO), HttpStatus.OK);
    }

    @PostMapping("/conversation/log")
    public ResponseEntity<?> mqttAuthorize(@Valid @RequestBody ConversationLogReqDTO data) throws JsonProcessingException {
        return new ResponseEntity<>(robotDeviceService.saveConversationLog(data), HttpStatus.OK);
    }

    @PostMapping("/setting/volume")
    public ResponseEntity<?> setVolume(@Valid @RequestBody RobotVolumeReqDTO data) {
        return success(robotDeviceService.saveRobotVolumeSetting(data));
    }

    @PostMapping("/setting/brightness")
    public ResponseEntity<?> setBrightness(@Valid @RequestBody RobotVolumeReqDTO data) {
        return success(robotDeviceService.saveRobotScreenBrightnessSetting(data));
    }

    @PostMapping("/update_sd/result")
    public ResponseEntity<?> saveUpdateSDResult(HttpServletRequest request,
                                                @Valid @RequestBody UpdateSDStatusReqDTO resultReqDTO) throws IOException {
        return success(robotDeviceService.saveUpdateSDRequestResult(request, resultReqDTO));
    }

    @PostMapping("/mapping_to_phone")
    public ResponseEntity<?> mapRobotToPhone(HttpServletRequest request,
                                             @Valid @RequestBody MappingRobotToPhoneReqDTO mappingRobotToPhoneReqDTO) throws IOException {
        return success(robotDeviceService.mapRobotToPhone(request, mappingRobotToPhoneReqDTO));
    }

    @PostMapping("/unmap")
    public ResponseEntity<?> unmapDevice(HttpServletRequest request, UnMapRobotReqDTO unMapRobotReqDTO) throws IOException {
        return success(robotDeviceService.unmapDevice(request, unMapRobotReqDTO));
    }
}
