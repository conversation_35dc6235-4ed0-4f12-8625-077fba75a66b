package com.stepup.springrobot.controller.robot_device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.controller.BaseController;
import com.stepup.springrobot.dto.chat.ConversationLogReqDTO;
import com.stepup.springrobot.dto.communication.*;
import com.stepup.springrobot.dto.mqtt.MqttAuthenReqDTO;
import com.stepup.springrobot.dto.mqtt.MqttAuthorizeReqDTO;
import com.stepup.springrobot.dto.robot.RobotDeviceLoginReqDTO;
import com.stepup.springrobot.model.robot.Robot;
import com.stepup.springrobot.repository.robot.RobotRepository;
import com.stepup.springrobot.security.robot.RobotJwtService;
import com.stepup.springrobot.service.communication.ConnectService;
import com.stepup.springrobot.service.communication.RobotDeviceService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v2/device")
public class RobotDeviceV2Controller extends BaseController {
    private final RobotDeviceService robotDeviceService;

    private final ConnectService connectService;

    private final RobotRepository robotRepository;

    private final RobotJwtService robotJwtService;

    public RobotDeviceV2Controller(RobotDeviceService robotDeviceService, ConnectService connectService,
            RobotRepository robotRepository, RobotJwtService robotJwtService) {
        this.robotDeviceService = robotDeviceService;
        this.connectService = connectService;
        this.robotRepository = robotRepository;
        this.robotJwtService = robotJwtService;
    }

    @PostMapping("/wifi/scan_result")
    public ResponseEntity<?> saveWifiScanResult(HttpServletRequest request,
            @Valid @RequestBody WifiScanResultReqDTO resultReqDTO) throws IOException {
        return success(robotDeviceService.saveWifiScanResult(request, resultReqDTO));
    }

    @PostMapping("/wifi/connect_result")
    public ResponseEntity<?> saveConnectWifiResult(HttpServletRequest request,
            @Valid @RequestBody WifiConnectResultDTO resultReqDTO) throws IOException {
        return success(robotDeviceService.saveWifiConnectResult(request, resultReqDTO));
    }

    @PostMapping("/firmware/update_result")
    public ResponseEntity<?> saveUpdateFirmwareResult(HttpServletRequest request,
            @Valid @RequestBody MqttUpdateResultReqDTO resultReqDTO) throws IOException {
        return success(robotDeviceService.saveUpdateFirmwareResult(request, resultReqDTO));
    }

    @PostMapping("/mqtt/result")
    public ResponseEntity<?> saveMqttResult(HttpServletRequest request,
            @Valid @RequestBody MqttUpdateResultReqDTO resultReqDTO) throws IOException {
        return success(robotDeviceService.saveMqttRequestResult(request, resultReqDTO));
    }

    @PostMapping("/alarm/result")
    public ResponseEntity<?> saveAlarmResult(HttpServletRequest request,
            @Valid @RequestBody MqttUpdateResultReqDTO resultReqDTO) throws IOException {
        return success(robotDeviceService.saveAlarmResult(request, resultReqDTO));
    }

    @PostMapping("/trigger_test")
    public ResponseEntity<?> triggerTest(@RequestBody JsonNode data) throws JsonProcessingException {
        return success(connectService.sendTestTriggerMessage(data));
    }

    @GetMapping("/trigger_data")
    public ResponseEntity<?> getTriggerData(@RequestParam("trigger_type") String triggerType)
            throws JsonProcessingException {
        return success(connectService.getTriggerDataByType(triggerType));
    }

    @PostMapping("/mqtt/authenticate")
    public ResponseEntity<?> mqttAuthenticate(@RequestBody MqttAuthenReqDTO reqDTO) throws JsonProcessingException {
        return new ResponseEntity<>(robotDeviceService.mqttAuthenticate(reqDTO), HttpStatus.OK);
    }

    @PostMapping("/mqtt/authorize")
    public ResponseEntity<?> mqttAuthorize(@RequestBody MqttAuthorizeReqDTO reqDTO) throws JsonProcessingException {
        return new ResponseEntity<>(robotDeviceService.mqttAuthorize(reqDTO), HttpStatus.OK);
    }

    @PostMapping("/conversation/log")
    public ResponseEntity<?> mqttAuthorize(@Valid @RequestBody ConversationLogReqDTO data)
            throws JsonProcessingException {
        return new ResponseEntity<>(robotDeviceService.saveConversationLog(data), HttpStatus.OK);
    }

    @PostMapping("/setting/volume")
    public ResponseEntity<?> setVolume(@Valid @RequestBody RobotVolumeReqDTO data) {
        return success(robotDeviceService.saveRobotVolumeSetting(data));
    }

    @PostMapping("/setting/brightness")
    public ResponseEntity<?> setBrightness(@Valid @RequestBody RobotVolumeReqDTO data) {
        return success(robotDeviceService.saveRobotScreenBrightnessSetting(data));
    }

    /**
     * Robot login endpoint: returns JWT if deviceId/password valid
     */
    @PostMapping("/login")
    public ResponseEntity<?> robotLogin(@Valid @RequestBody RobotDeviceLoginReqDTO loginReqDTO) {
        return robotRepository.findByDeviceId(loginReqDTO.getDeviceId())
                .filter(Robot::isActivated)
                .filter(robot -> Objects.equals(loginReqDTO.getPassword(), robot.getPassword()))
                .map(robot -> ResponseEntity.ok(Map.of("token", robotJwtService.generateToken(robot))))
                .orElseGet(() -> ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(Map.of("error", "Invalid deviceId or password")));
    }

    /**
     * Robot logout endpoint: revokes the current JWT
     */
    @PostMapping("/log")
    public ResponseEntity<?> robotLogout() {
        // if (authHeader == null || !authHeader.startsWith("Bearer ")) {
        // return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
        // .body(Map.of("error", "Missing or invalid Authorization header"));
        // }

        // String token = authHeader.substring(7);
        // robotJwtService.revokeToken(token);
        return ResponseEntity.ok(Map.of("message", "Token revoked"));
    }

    @PostMapping("/ws-handshake")
    public ResponseEntity<?> wsHandshake(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "Missing or invalid Authorization header"));
        }

        String token = authHeader.substring(7);
        if (!robotJwtService.validateToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "Invalid token"));
        }

        // Generate a short-lived token specifically for WebSocket
        String wsToken = robotJwtService.generateWebSocketToken(token);
        return ResponseEntity.ok(Map.of("wsToken", wsToken));
    }
}
