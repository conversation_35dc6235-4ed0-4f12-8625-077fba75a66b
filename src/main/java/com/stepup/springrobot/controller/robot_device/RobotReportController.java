package com.stepup.springrobot.controller.robot_device;

import com.stepup.springrobot.controller.BaseController;
import com.stepup.springrobot.dto.communication.RobotErrorLogReqDTO;
import com.stepup.springrobot.service.communication.RobotReportInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("robot/api/v1/report_info")
public class RobotReportController extends BaseController {
    @Autowired
    private RobotReportInfoService robotReportInfoService;

    @PostMapping("")
    public ResponseEntity<?> saveRobotErrorLog(@Valid @RequestBody RobotErrorLogReqDTO reqDTO) {
        return success(robotReportInfoService.saveRobotErrorLog(reqDTO));
    }
}
