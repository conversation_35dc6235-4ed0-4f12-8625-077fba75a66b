package com.stepup.springrobot.controller;

import com.stepup.springrobot.dto.onboarding.OnboardAnswerReqDTO;
import com.stepup.springrobot.service.OnboardService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/onboarding")
public class OnboardingController extends BaseController {
    @Autowired
    private OnboardService onboardService;

    @GetMapping()
    public ResponseEntity<?> getOnboardingData() {
        return success(onboardService.getOnboardQuestion());
    }

    @Operation(summary = "Save onboarding answer",
            description = "onboarding_answers: [{'type': 'IS_ROBOT_OWNER', 'answer': {'text': 'yes'}}, {'type': 'USERNAME', 'answer': {'text': 'viet'}}]")
    @PostMapping()
    public ResponseEntity<?> saveOnboardingAnswer(HttpServletRequest request, @Valid @RequestBody OnboardAnswerReqDTO onboardingQuestion) throws IOException {
        return success(onboardService.saveOnBoardingAnswer(request, onboardingQuestion, false));
    }

    @PutMapping()
    public ResponseEntity<?> updateOnboardingAnswer(HttpServletRequest request, @Valid @RequestBody OnboardAnswerReqDTO onboardingQuestion) throws IOException {
        return success(onboardService.updateOnBoardingAnswer(request, onboardingQuestion, false));
    }
}
