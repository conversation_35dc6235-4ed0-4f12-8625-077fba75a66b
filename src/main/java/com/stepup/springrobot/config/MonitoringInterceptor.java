package com.stepup.springrobot.config;

import com.stepup.springrobot.dto.monitoring.MonitoringLogDTO;
import com.stepup.springrobot.dto.monitoring.TraceContextDTO;
import com.stepup.springrobot.model.DatadogLogType;
import com.stepup.springrobot.service.DatadogService;
import com.stepup.springrobot.service.TraceContextService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class MonitoringInterceptor implements HandlerInterceptor {

    @Autowired
    private DatadogService datadogService;

    @Autowired
    private TraceContextService traceContextService;

    @Value("${spring.profiles.active:development}")
    private String environment;

    @Value("${app.version:1.0.0}")
    private String appVersion;

    @Value("${datadog_service:spring-robot}")
    private String serviceName;

    private static final String START_TIME_ATTRIBUTE = "startTime";
    private static final String TRACE_CONTEXT_ATTRIBUTE = "traceContext";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        long startTime = System.currentTimeMillis();
        request.setAttribute(START_TIME_ATTRIBUTE, startTime);

        // Create or extract trace context
        TraceContextDTO traceContext = createTraceContextFromRequest(request);
        request.setAttribute(TRACE_CONTEXT_ATTRIBUTE, traceContext);
        traceContextService.setTraceContext(traceContext);

        // Log request start
        logHttpRequestStart(request, traceContext);

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        // This is called after the controller method but before the view is rendered
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        Long startTime = (Long) request.getAttribute(START_TIME_ATTRIBUTE);
        TraceContextDTO traceContext = (TraceContextDTO) request.getAttribute(TRACE_CONTEXT_ATTRIBUTE);

        if (startTime != null) {
            long duration = System.currentTimeMillis() - startTime;

            if (ex != null) {
                logHttpRequestError(request, response, ex, duration, traceContext);
            } else {
                logHttpRequestEnd(request, response, duration, traceContext);
            }
        }

        // Clear trace context
        traceContextService.clearTraceContext();
    }

    private TraceContextDTO createTraceContextFromRequest(HttpServletRequest request) {
        // Try to extract from headers first
        String traceId = request.getHeader("X-Trace-ID");
        String spanId = request.getHeader("X-Span-ID");
        String userId = extractUserIdFromRequest(request);
        String sessionId = request.getSession().getId();

        if (traceId != null && spanId != null) {
            return TraceContextDTO.builder()
                    .traceId(traceId)
                    .spanId(spanId)
                    .userId(userId)
                    .sessionId(sessionId)
                    .timestamp(Instant.now())
                    .build();
        } else {
            return traceContextService.createNewTrace(userId, sessionId);
        }
    }

    private String extractUserIdFromRequest(HttpServletRequest request) {
        // Try to extract user ID from JWT token or session
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            // Extract from JWT token if available
            // This would require JWT service injection
            return "extracted_from_jwt"; // Placeholder
        }

        // Try from query parameter
        String userId = request.getParameter("userId");
        if (userId != null) {
            return userId;
        }

        return "anonymous";
    }

    private void logHttpRequestStart(HttpServletRequest request, TraceContextDTO traceContext) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("http_method", request.getMethod());
        metadata.put("http_url", request.getRequestURL().toString());
        metadata.put("http_query_string", request.getQueryString());
        metadata.put("http_remote_addr", request.getRemoteAddr());
        metadata.put("http_user_agent", request.getHeader("User-Agent"));
        metadata.put("http_content_type", request.getContentType());

        MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                .logType(DatadogLogType.HTTP_REQUEST_START)
                .message(String.format("HTTP %s %s started", request.getMethod(), request.getRequestURI()))
                .level("DEBUG")
                .timestamp(Instant.now())
                .serviceName("spring-robot")
                .httpMethod(request.getMethod())
                .httpUrl(request.getRequestURL().toString())
                .traceContext(traceContext)
                .metadata(metadata)
                .build();

        datadogService.sendLogToDatadog(monitoringLog);
    }

    private void logHttpRequestEnd(HttpServletRequest request, HttpServletResponse response, long duration, TraceContextDTO traceContext) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("http_method", request.getMethod());
        metadata.put("http_url", request.getRequestURL().toString());
        metadata.put("http_status_code", response.getStatus());
        metadata.put("http_duration_ms", duration);

        MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                .logType(DatadogLogType.HTTP_REQUEST_END)
                .message(String.format("HTTP %s %s completed in %d ms with status %d",
                        request.getMethod(), request.getRequestURI(), duration, response.getStatus()))
                .level("INFO")
                .timestamp(Instant.now())
                .serviceName("spring-robot")
                .httpMethod(request.getMethod())
                .httpUrl(request.getRequestURL().toString())
                .durationMs(duration)
                .traceContext(traceContext)
                .metadata(metadata)
                .build();

        datadogService.sendLogToDatadog(monitoringLog);
    }

    private void logHttpRequestError(HttpServletRequest request, HttpServletResponse response, Exception ex, long duration, TraceContextDTO traceContext) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("http_method", request.getMethod());
        metadata.put("http_url", request.getRequestURL().toString());
        metadata.put("http_status_code", response.getStatus());
        metadata.put("http_duration_ms", duration);
        metadata.put("error_type", ex.getClass().getSimpleName());

        MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                .logType(DatadogLogType.HTTP_REQUEST_ERROR)
                .message(String.format("HTTP %s %s failed after %d ms with error: %s",
                        request.getMethod(), request.getRequestURI(), duration, ex.getMessage()))
                .level("ERROR")
                .timestamp(Instant.now())
                .serviceName("spring-robot")
                .httpMethod(request.getMethod())
                .httpUrl(request.getRequestURL().toString())
                .durationMs(duration)
                .errorMessage(ex.getMessage())
                .traceContext(traceContext)
                .metadata(metadata)
                .build();

        datadogService.sendLogToDatadog(monitoringLog);
    }
}
