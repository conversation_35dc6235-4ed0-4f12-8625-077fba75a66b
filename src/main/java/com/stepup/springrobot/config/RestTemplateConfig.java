package com.stepup.springrobot.config;

import okhttp3.OkHttpClient;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Configuration
public class RestTemplateConfig {

    @Value("${sms_gateway_username}")
    private String SMS_GATEWAY_USERNAME;

    @Value("${sms_gateway_password}")
    private String SMS_GATEWAY_PASSWORD;

    @Bean
    public RestTemplate restTemplate() {
        HttpHost host = new HttpHost("localhost", 80, "http");
        CloseableHttpClient client = HttpClientBuilder.create().
                setDefaultCredentialsProvider(provider()).useSystemProperties().build();
        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactoryDigestAuth(host, client);

        return new RestTemplate(requestFactory);
    }

    private CredentialsProvider provider() {
        CredentialsProvider provider = new BasicCredentialsProvider();
        UsernamePasswordCredentials credentials =
                new UsernamePasswordCredentials(SMS_GATEWAY_USERNAME, SMS_GATEWAY_PASSWORD);
        provider.setCredentials(AuthScope.ANY, credentials);
        return provider;
    }

//    @Bean
//    public OkHttpClient okHttpClient() {
//        return new OkHttpClient.Builder()
//                .connectTimeout(Duration.ofSeconds(30))
//                .readTimeout(Duration.ofSeconds(30))
//                .writeTimeout(Duration.ofSeconds(30))
//                .build();
//    }
}
