package com.stepup.springrobot.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "monitoring")
public class MonitoringConfiguration {

    private boolean enabled = true;
    private Datadog datadog = new Datadog();
    private WebSocket webSocket = new WebSocket();
    private HttpRequests httpRequests = new HttpRequests();
    private ExternalApi externalApi = new ExternalApi();
    private ServiceMethods serviceMethods = new ServiceMethods();

    @Data
    public static class Datadog {
        private boolean enabled = true;
        private String logLevel = "INFO"; // DEBUG, INFO, WARN, ERROR
        private boolean includeRequestBody = false;
        private boolean includeResponseBody = false;
        private boolean includeStackTrace = true;
        private int maxRetries = 3;
        private long timeoutMs = 20000;
    }

    @Data
    public static class WebSocket {
        private boolean enabled = true;
        private boolean logConnections = true;
        private boolean logMessages = false; // Can be verbose
        private boolean logErrors = true;
        private boolean includeSessionAttributes = false;
        private int maxMessageSizeToLog = 1024; // bytes
    }

    @Data
    public static class HttpRequests {
        private boolean enabled = true;
        private boolean logRequestStart = false; // DEBUG level
        private boolean logRequestEnd = true;
        private boolean logRequestErrors = true;
        private boolean includeHeaders = false;
        private boolean includeQueryParams = true;
        private String[] excludedPaths = {"/actuator/**", "/health", "/metrics", "/static/**"};
    }

    @Data
    public static class ExternalApi {
        private boolean enabled = true;
        private boolean logRequestStart = true;
        private boolean logSuccess = true;
        private boolean logErrors = true;
        private boolean includeRequestBody = false;
        private boolean includeResponseBody = false;
        private boolean includeHeaders = false;
        private long defaultTimeoutMs = 20000;
        private int defaultRetries = 0;
    }

    @Data
    public static class ServiceMethods {
        private boolean enabled = true;
        private boolean logMethodStart = false; // DEBUG level
        private boolean logMethodEnd = false;  // DEBUG level
        private boolean logMethodErrors = true;
        private long minDurationToLogMs = 1000; // Only log methods that take longer than this
        private String[] includedPackages = {"com.stepup.springrobot.service"};
        private String[] excludedClasses = {"DatadogService", "TraceContextService"};
    }
}
