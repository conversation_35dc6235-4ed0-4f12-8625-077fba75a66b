package com.stepup.springrobot.config.firebase;

import com.google.firebase.messaging.*;
import com.stepup.springrobot.dto.notification.PushNotificationRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Slf4j

@Service
public class FCMService {
    public void sendMessage(Map<String, String> data, PushNotificationRequest request)
            throws InterruptedException, ExecutionException {
        Message message = getPreconfiguredMessageWithData(data, request);
        String response = sendAndGetResponse(message);
        log.info("Sent message with data. Topic: " + request.getTopic() + ", " + response);
    }

    /**
     * G<PERSON><PERSON> đến topic, không kèm data
     * @param request
     * @throws InterruptedException
     * @throws ExecutionException
     */
    public void sendMessageTopicWithoutData(PushNotificationRequest request)
            throws InterruptedException, ExecutionException {
        Message message = getPreconfiguredMessageWithoutData(request);
        String response = sendAndGetResponse(message);
        log.info("Sent message without data. Topic: " + request.getTopic() + ", " + response);
    }

    /**
     * Gửi đến 1 devices chỉ định
     * @param request
     * @throws InterruptedException
     * @throws ExecutionException
     */
    public void sendMessageToToken(PushNotificationRequest request)
            throws InterruptedException, ExecutionException {
        Message message;
        if (request.getData() != null && !request.getData().isEmpty()) {
            message = getPreconfiguredMessageToTokenWithData(request);
        } else {
            message = getPreconfiguredMessageToToken(request);
        }
        String response = sendAndGetResponse(message);
        log.info("Sent message to token. Device token: " + request.getToken() + ", " + response);
    }

    /**
     * Gửi cùng 1 content tới multi devices thông qua token.
     * @param request
     * @param listDevicesToken
     * @throws FirebaseMessagingException
     */
    public void sendMessageToMultiToken(PushNotificationRequest request, List<String> listDevicesToken) throws FirebaseMessagingException {
        MulticastMessage message = MulticastMessage.builder()
                .setNotification(Notification.builder().setTitle(request.getTitle()).setBody(request.getMessage()).build())
                .addAllTokens(listDevicesToken)
                .build();
        BatchResponse response = FirebaseMessaging.getInstance().sendMulticast(message);
        log.info(response.getSuccessCount() + " messages were sent successfully");
        if (response.getFailureCount() > 0) {
            List<SendResponse> responses = response.getResponses();
            List<String> failedTokens = new ArrayList<>();
            for (int i = 0; i < responses.size(); i++) {
                if (!responses.get(i).isSuccessful()) {
                    // The order of responses corresponds to the order of the registration tokens.
                    failedTokens.add(listDevicesToken.get(i));
                }
            }

            log.info("List of tokens that caused failures: " + failedTokens);
        }
    }

    private String sendAndGetResponse(Message message) throws InterruptedException, ExecutionException {
        return FirebaseMessaging.getInstance().sendAsync(message).get();
    }

    private AndroidConfig getAndroidConfig(String topic) {
        return AndroidConfig.builder()
                .setTtl(Duration.ofMinutes(2).toMillis()).setCollapseKey(topic)
                .setPriority(AndroidConfig.Priority.HIGH)
                .setNotification(AndroidNotification.builder().setSound(NotificationParameter.SOUND.getValue())
                        .setColor(NotificationParameter.COLOR.getValue()).setTag(topic).build()).build();
    }

    private ApnsConfig getApnsConfig(String topic) {
        return ApnsConfig.builder()
                .setAps(Aps.builder().setCategory(topic).setThreadId(topic).build()).build();
    }

    private Message getPreconfiguredMessageToToken(PushNotificationRequest request) {
        return getPreconfiguredMessageBuilder(request).setToken(request.getToken())
                .build();
    }

    private Message getPreconfiguredMessageWithoutData(PushNotificationRequest request) {
        return getPreconfiguredMessageBuilder(request).setTopic(request.getTopic())
                .build();
    }

    private Message getPreconfiguredMessageWithData(Map<String, String> data, PushNotificationRequest request) {
        return getPreconfiguredMessageBuilder(request).putAllData(data).setTopic(request.getTopic())
                .build();
    }

    private Message getPreconfiguredMessageToTokenWithData(PushNotificationRequest request) {
        return getPreconfiguredMessageBuilder(request)
                .putAllData(request.getData())
                .setToken(request.getToken())
                .build();
    }

    private Message.Builder getPreconfiguredMessageBuilder(PushNotificationRequest request) {
        AndroidConfig androidConfig = getAndroidConfig(request.getTopic());
        ApnsConfig apnsConfig = getApnsConfig(request.getTopic());
        return Message.builder()
                .setApnsConfig(apnsConfig).setAndroidConfig(androidConfig)
                .setNotification(Notification.builder().setTitle(request.getTitle()).setBody(request.getMessage()).build());
    }
}
