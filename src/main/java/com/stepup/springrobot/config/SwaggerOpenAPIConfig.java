package com.stepup.springrobot.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class SwaggerOpenAPIConfig {

    @Value("${swagger.openapi.prod-url}")
    private String productionUrl;

    @Value("${swagger.openapi.staging-url}")
    private String stagingUrl;

    @Value("${swagger.openapi.dev-url}")
    private String developmentUrl;

    @Value("${swagger.openapi.local-url}")
    private String localUrl;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Robot Management API")
                        .description("This API exposes endpoints to manage Robot Project. Để sử dụng JWT token, vui lòng nhập token với tiền tố 'Bearer ' vào ô Authorization.")
                        .version("1.0")
                        .contact(new Contact()
                                .name("ThanhDT")
                                .email("<EMAIL>")))
                .servers(List.of(
                        new Server().url(productionUrl).description("Production Server"),
                        new Server().url(stagingUrl).description("Staging Server"),
                        new Server().url(developmentUrl).description("Development Server"),
                        new Server().url(localUrl).description("Local Server")))
                .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("Enter JWT token without 'Bearer ' prefix")));
    }
}