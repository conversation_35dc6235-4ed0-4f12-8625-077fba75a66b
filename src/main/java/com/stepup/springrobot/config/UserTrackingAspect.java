package com.stepup.springrobot.config;

import com.stepup.springrobot.service.UserErrorTracker;
import com.stepup.springrobot.service.UserJourneyTracker;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Aspect
@Component
@Slf4j
public class UserTrackingAspect {

    @Autowired
    private UserErrorTracker userErrorTracker;

    @Autowired
    private UserJourneyTracker userJourneyTracker;

    /**
     * Track errors in service methods
     */
    @AfterThrowing(pointcut = "execution(* com.stepup.springrobot.service..*(..))", throwing = "exception")
    public void trackServiceErrors(JoinPoint joinPoint, Exception exception) {
        try {
            String serviceName = joinPoint.getTarget().getClass().getSimpleName();
            String methodName = joinPoint.getSignature().getName();

            // Extract user context from request or method parameters
            UserContext userContext = extractUserContext(joinPoint);

            if (userContext.getUserId() != null) {
                // Capture context before error
                Map<String, Object> contextBeforeError = new HashMap<>();
                contextBeforeError.put("service_name", serviceName);
                contextBeforeError.put("method_name", methodName);
                contextBeforeError.put("method_args", joinPoint.getArgs());
                contextBeforeError.put("timestamp", System.currentTimeMillis());

                // Track the error
                String errorId = userErrorTracker.trackUserError(
                        userContext.getUserId(),
                        userContext.getSessionId(),
                        userContext.getConversationId(),
                        userContext.getRobotId(),
                        exception,
                        serviceName,
                        methodName,
                        contextBeforeError
                );

                log.info("User error tracked: userId={}, errorId={}, service={}, method={}",
                        userContext.getUserId(), errorId, serviceName, methodName);
            }

        } catch (Exception e) {
            log.error("Error in UserTrackingAspect: {}", e.getMessage(), e);
        }
    }

    /**
     * Track successful authentication events
     */
    @AfterReturning(pointcut = "execution(* com.stepup.springrobot.service..*.*auth*(..)) || " +
            "execution(* com.stepup.springrobot.service..*.*login*(..))")
    public void trackAuthenticationSuccess(JoinPoint joinPoint) {
        try {
            UserContext userContext = extractUserContext(joinPoint);

            if (userContext.getUserId() != null) {
                userJourneyTracker.trackAuthentication(
                        userContext.getUserId(),
                        userContext.getSessionId(),
                        true,
                        null
                );

                log.debug("Authentication success tracked: userId={}", userContext.getUserId());
            }

        } catch (Exception e) {
            log.error("Error tracking authentication success: {}", e.getMessage(), e);
        }
    }

    /**
     * Track conversation-related method calls
     */
    @Before("execution(* com.stepup.springrobot.service.AIRobotConversationService.*(..)) || " +
            "execution(* com.stepup.springrobot.service.LLMConversationService.*(..))")
    public void trackConversationStart(JoinPoint joinPoint) {
        try {
            String methodName = joinPoint.getSignature().getName();
            UserContext userContext = extractUserContext(joinPoint);

            if (userContext.getUserId() != null && isConversationStartMethod(methodName)) {
                // Start user journey if not already started
                String journeyId = userJourneyTracker.startUserJourney(
                        userContext.getUserId(),
                        userContext.getSessionId(),
                        com.stepup.springrobot.dto.monitoring.UserJourneyDTO.JourneyType.CONVERSATION,
                        userContext.getRobotId(),
                        createInitialContext(joinPoint)
                );

                log.debug("Conversation journey started: userId={}, journeyId={}",
                        userContext.getUserId(), journeyId);
            }

        } catch (Exception e) {
            log.error("Error tracking conversation start: {}", e.getMessage(), e);
        }
    }

    /**
     * Track ASR processing
     */
    @AfterReturning(pointcut = "execution(* com.stepup.springrobot.service.RecognizeService.*(..)) && " +
            "args(..)", returning = "result")
    public void trackASRProcessing(JoinPoint joinPoint, Object result) {
        try {
            String methodName = joinPoint.getSignature().getName();
            UserContext userContext = extractUserContext(joinPoint);

            if (userContext.getUserId() != null && isASRMethod(methodName)) {
                boolean success = result != null;
                String transcript = success ? result.toString() : null;

                userJourneyTracker.trackASRProcessing(
                        userContext.getUserId(),
                        userContext.getSessionId(),
                        "INTERNAL", // Default provider
                        success,
                        transcript,
                        success ? null : "ASR processing failed"
                );

                log.debug("ASR processing tracked: userId={}, success={}",
                        userContext.getUserId(), success);
            }

        } catch (Exception e) {
            log.error("Error tracking ASR processing: {}", e.getMessage(), e);
        }
    }

    /**
     * Track LLM processing
     */
    @AfterReturning(pointcut = "execution(* com.stepup.springrobot.service.LLMConversationService.*(..)) && " +
            "args(..)", returning = "result")
    public void trackLLMProcessing(JoinPoint joinPoint, Object result) {
        try {
            String methodName = joinPoint.getSignature().getName();
            UserContext userContext = extractUserContext(joinPoint);

            if (userContext.getUserId() != null && isLLMMethod(methodName)) {
                boolean success = result != null;
                String response = success ? result.toString() : null;

                userJourneyTracker.trackLLMProcessing(
                        userContext.getUserId(),
                        userContext.getSessionId(),
                        "DEFAULT_MODEL", // Could be extracted from config
                        success,
                        response,
                        success ? null : "LLM processing failed"
                );

                log.debug("LLM processing tracked: userId={}, success={}",
                        userContext.getUserId(), success);
            }

        } catch (Exception e) {
            log.error("Error tracking LLM processing: {}", e.getMessage(), e);
        }
    }

    /**
     * Extract user context from method parameters or request
     */
    private UserContext extractUserContext(JoinPoint joinPoint) {
        UserContext context = new UserContext();

        try {
            // Try to extract from HTTP request
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                context.setUserId(request.getHeader("userId"));
                context.setSessionId(request.getHeader("sessionId"));
                context.setConversationId(request.getHeader("conversationId"));
                context.setRobotId(request.getHeader("robotId"));
            }

            // Try to extract from method parameters
            Object[] args = joinPoint.getArgs();
            for (Object arg : args) {
                if (arg != null) {
                    String argString = arg.toString();

                    // Look for common parameter patterns
                    if (context.getUserId() == null && argString.matches(".*user.*id.*")) {
                        // Extract user ID from parameter
                        try {
                            if (arg instanceof String && ((String) arg).matches("\\d+")) {
                                context.setUserId((String) arg);
                            }
                        } catch (Exception ignored) {
                        }
                    }

                    // Look for conversation ID patterns
                    if (context.getConversationId() == null && argString.matches(".*conversation.*id.*")) {
                        try {
                            if (arg instanceof String) {
                                context.setConversationId((String) arg);
                            } else if (arg instanceof Number) {
                                context.setConversationId(arg.toString());
                            }
                        } catch (Exception ignored) {
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.debug("Could not extract full user context: {}", e.getMessage());
        }

        return context;
    }

    /**
     * Create initial context for journey tracking
     */
    private Map<String, Object> createInitialContext(JoinPoint joinPoint) {
        Map<String, Object> context = new HashMap<>();
        context.put("service_name", joinPoint.getTarget().getClass().getSimpleName());
        context.put("method_name", joinPoint.getSignature().getName());
        context.put("start_timestamp", System.currentTimeMillis());
        return context;
    }

    /**
     * Check if method is a conversation start method
     */
    private boolean isConversationStartMethod(String methodName) {
        return methodName.toLowerCase().contains("robot") ||
                methodName.toLowerCase().contains("conversation") ||
                methodName.toLowerCase().contains("chat");
    }

    /**
     * Check if method is an ASR method
     */
    private boolean isASRMethod(String methodName) {
        return methodName.toLowerCase().contains("recognize") ||
                methodName.toLowerCase().contains("speech") ||
                methodName.toLowerCase().contains("asr");
    }

    /**
     * Check if method is an LLM method
     */
    private boolean isLLMMethod(String methodName) {
        return methodName.toLowerCase().contains("llm") ||
                methodName.toLowerCase().contains("conversation") ||
                methodName.toLowerCase().contains("response");
    }

    /**
     * Inner class to hold user context
     */
    private static class UserContext {
        private String userId;
        private String sessionId;
        private String conversationId;
        private String robotId;

        // Getters and setters
        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getSessionId() {
            return sessionId;
        }

        public void setSessionId(String sessionId) {
            this.sessionId = sessionId;
        }

        public String getConversationId() {
            return conversationId;
        }

        public void setConversationId(String conversationId) {
            this.conversationId = conversationId;
        }

        public String getRobotId() {
            return robotId;
        }

        public void setRobotId(String robotId) {
            this.robotId = robotId;
        }
    }
}
