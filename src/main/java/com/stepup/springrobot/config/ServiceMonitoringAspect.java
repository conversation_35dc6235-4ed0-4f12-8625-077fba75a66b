package com.stepup.springrobot.config;

import com.stepup.springrobot.dto.monitoring.TraceContextDTO;
import com.stepup.springrobot.service.DatadogService;
import com.stepup.springrobot.service.TraceContextService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class ServiceMonitoringAspect {

    @Autowired
    private DatadogService datadogService;

    @Autowired
    private TraceContextService traceContextService;

    @Around("execution(* com.stepup.springrobot.service.AIRobotConversationService.*(..)) && " +
            "execution(* com.stepup.springrobot.service.LLMConversationService..*(..))")
    public Object monitorServiceMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();

        // Get or create trace context
        TraceContextDTO traceContext = traceContextService.getTraceContext();
        if (traceContext == null) {
            traceContext = traceContextService.createNewTrace("system", "service_call");
        } else {
            traceContext = traceContextService.createChildSpan(className + "." + methodName);
        }

        long startTime = System.currentTimeMillis();

        // Log method start
        datadogService.logServiceMethodStart(className, methodName, traceContext);

        try {
            Object result = joinPoint.proceed();

            long duration = System.currentTimeMillis() - startTime;

            // Log method success
            datadogService.logServiceMethodEnd(className, methodName, duration, traceContext);

            return result;

        } catch (Exception e) {
            // Log method error
            datadogService.logServiceMethodError(className, methodName, e, traceContext);
            throw e;
        }
    }

    @Around("execution(* com.stepup.springrobot.service..*.*(..)) && " +
            "@annotation(org.springframework.web.bind.annotation.RestController)")
    public Object monitorControllerMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();

        TraceContextDTO traceContext = traceContextService.getTraceContext();
        long startTime = System.currentTimeMillis();

        try {
            Object result = joinPoint.proceed();
            long duration = System.currentTimeMillis() - startTime;

            log.info("Controller method {}.{} completed in {} ms", className, methodName, duration);

            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Controller method {}.{} failed after {} ms: {}",
                    className, methodName, duration, e.getMessage());
            throw e;
        }
    }
}
