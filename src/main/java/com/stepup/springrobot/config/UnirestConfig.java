package com.stepup.springrobot.config;

import kong.unirest.Unirest;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
public class UnirestConfig {

    @PostConstruct
    public void configureUnirest() {
        // Configure Unirest globally once at application startup
        Unirest.config()
                .connectTimeout(20000)
                .socketTimeout(20000);
    }
}
