package com.stepup.springrobot.model.chat;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.sun.istack.NotNull;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "robot_user_conversation_record_history", indexes = {
        @Index(name = "robot_conversation_record_history_character", columnList = "robot_user_conversation_id, character")
})
public class RobotUserConversationRecordHistory extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Enumerated(EnumType.STRING)
    private AICharacter character;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "gpt_character")
    private GPTCharacter gptCharacter;

    @NotNull
    @Column(name = "robot_user_conversation_id")
    private Long robotUserConversationId;

    @NotNull
    @Column(columnDefinition = "text")
    private String content;

    @Column(name = "audio")
    private String audio;

    @Column(name = "is_finish_message")
    private Boolean isFinishMessage;

    @Column(name = "response_time")
    private Long responseTime;

    @Column(name = "voice_data", columnDefinition = "text")
    private String voiceData;

    private String emotion;

    private String image;

    private String video;

    @Column(name = "data", columnDefinition = "text")
    private String data;

    @Column(name = "servo_data", columnDefinition = "text")
    private String servoData;

    @Column(name = "data_report")
    private String dataReport;

    @Column(name = "robot_type")
    private String robotType;

    @Column(name = "emotions", columnDefinition = "text")
    private String emotions;

    @Column(name = "listening_animation", columnDefinition = "text")
    private String listeningAnimation;

    private String language;

    @Column(name = "text_viewer", columnDefinition = "text")
    private String textViewer;

    private Integer volume;

    @Builder.Default
    @Column(name = "likes", columnDefinition = "int default 0")
    private Integer likes = 0;

    @Builder.Default
    @Column(name = "dislikes", columnDefinition = "int default 0")
    private Integer dislikes = 0;

    @Column(name = "score", columnDefinition = "text")
    private String score;

    //asr evaluation
    @Column(name = "is_asr_correct")
    private Boolean isAsrCorrect;

    @Column(name = "asr_corrected_content")
    private String asrCorrectedContent;

    @Column(name = "is_intent_affected")
    private Boolean isIntentAffected;

    @Column(name = "intent_corrected_content")
    private String intentCorrectedContent;
}
