package com.stepup.springrobot.model.chat;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.sun.istack.NotNull;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "robot_user_conversation", indexes = {
        @Index(name = "robot_user_conversation_socket_session_id_index", columnList = "socket_session_id"),
        @Index(name = "robot_user_conversation_completed_timestamp", columnList = "completed_timestamp")
})
public class RobotUserConversation extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "socket_session_id")
    private String socketSessionId;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "robot_id")
    private String robotId;

    @Column(name = "profile_id")
    private String profileId;

    private String ip;

    @Column(name = "external_conversation_id")
    private String externalConversationId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "conversation_type")
    private ConversationType conversationType;

    @Column(name = "bot_id")
    private Long botId;

    @Enumerated(EnumType.STRING)
    @Column(name = "bot_type")
    private BotSourceType botType;

    @Enumerated(EnumType.STRING)
    @Column(name = "asr_type")
    private STTHandlerType asrType;

    @Enumerated(EnumType.STRING)
    @Column(name = "conversation_source")
    private ConversationSource conversationSource;

    @Column(name = "log", columnDefinition = "text")
    private String log;

    private String phone;

    private String video;

    @Column(name = "conversation_reports", columnDefinition = "text")
    private String conversationReports;

    @Column(name = "conversation_summaries", columnDefinition = "text")
    private String conversationSummary;

    @Column(name = "gifs", columnDefinition = "text")
    private String gifs;

    @Column(name = "server_log", columnDefinition = "text")
    private String serverLog;

    @Column(name = "completed_timestamp")
    private Long completedTime;

    @Column(name = "pronoun_score")
    private Long pronounScore;
}
