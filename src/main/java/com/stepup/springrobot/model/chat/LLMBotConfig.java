package com.stepup.springrobot.model.chat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Entity
@Table(name = "llm_bot_config")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LLMBotConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "bot_id", nullable = false, unique = true)
    private Long botId;

    @Column(name = "silence_threshold", nullable = false)
    private Integer silenceThreshold;
}
