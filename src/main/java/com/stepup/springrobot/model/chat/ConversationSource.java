package com.stepup.springrobot.model.chat;

import java.util.HashMap;
import java.util.Map;

public enum ConversationSource {
    ROBOT("ROBOT"),
    APP("APP"),
    WEB_MVP("WEB_MVP");

    public static final Map<String, ConversationSource> CONVERSATION_SOURCE_HASH_MAP = new HashMap<>();

    private final String source;

    ConversationSource(String source) {
        this.source = source;
    }

    static {
        for (ConversationSource e : values()) {
            CONVERSATION_SOURCE_HASH_MAP.put(e.source, e);
        }
    }

    public static ConversationSource from(String source) {
        return CONVERSATION_SOURCE_HASH_MAP.get(source);
    }

    public String getSource() {
        return this.source;
    }
}