package com.stepup.springrobot.model.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Set;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "users",
        indexes = {
                @Index(name = "users_phone_index", columnList = "phone", unique = true),
        })
public class User extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @Column(name = "phone", nullable = false)
    private String phone;

    @Column(name = "password", nullable = false)
    private String password;

    @Builder.Default
    @Column(name = "is_active", columnDefinition = "BOOLEAN default true")
    private Boolean isActive = true;

    @Builder.Default
    @ElementCollection(targetClass = UserRole.class, fetch = FetchType.EAGER)
    @CollectionTable(name = "user_roles", joinColumns = @JoinColumn(name = "user_id"))
    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false)
    private Set<UserRole> roles = Set.of(UserRole.USER);

    private String ip;

    @Column(name = "device_id")
    private String deviceId;

    @Column(name = "app_v")
    private String appV;

    private String os;

    @JsonProperty("device_name")
    private String deviceName;

    @Builder.Default
    @Column(name = "redeemable_balance", columnDefinition = "BIGINT default 2000")
    private Long redeemableBalance = 2000L;


}
