package com.stepup.springrobot.model;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum ModelAIProvider {
    openai("openai"),
    stepup_robot_manufacture("stepup_robot_manufacture");

    public static final Map<String, ModelAIProvider> CHAT_GPT_PROVIDER_HASH_MAP = new HashMap<>();

    private final String provider;

    ModelAIProvider(String provider) {
        this.provider = provider;
    }

    static {
        for (ModelAIProvider e : values()) {
            CHAT_GPT_PROVIDER_HASH_MAP.put(e.provider, e);
        }
    }

    public static ModelAIProvider from(String provider) {
        return CHAT_GPT_PROVIDER_HASH_MAP.get(provider);
    }

}