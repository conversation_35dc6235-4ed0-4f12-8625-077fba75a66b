package com.stepup.springrobot.model.study;

import lombok.*;

import javax.persistence.*;
import java.util.List;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "study_lesson_manifests")
public class StudyLessonManifest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "bot_id", nullable = false)
    private Long botId;

    @Column(name = "is_preload", columnDefinition = "boolean default true")
    private Boolean isPreload;

    @OneToMany(mappedBy = "manifest", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<StudyLessonGif> gifs;
}
