package com.stepup.springrobot.model.study;

import lombok.*;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "study_lesson_gifs", indexes = {
        @Index(name = "idx_gif_url", columnList = "gif_url")
})
public class StudyLessonGif {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "manifest_id", nullable = false)
    private Long manifestId;

    @Column(name = "gif_url", nullable = false)
    private String gifUrl;

    @Column(name = "local_path", nullable = false)
    private String localPath;

    @Column(name = "etag")
    private String etag;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "manifest_id", referencedColumnName = "id", insertable = false, updatable = false)
    private StudyLessonManifest manifest;
} 