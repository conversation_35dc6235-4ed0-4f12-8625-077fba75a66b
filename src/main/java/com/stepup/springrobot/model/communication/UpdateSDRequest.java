package com.stepup.springrobot.model.communication;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;

import javax.persistence.*;


@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "update_sd_requests")
public class UpdateSDRequest extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "robot_id")
    private String robotId;

    private String path;

    private String url;

    @Enumerated(EnumType.STRING)
    @Column(name = "action_type")
    private UpdateSDActionType actionType;

    @Enumerated(EnumType.STRING)
    @Column(name = "data_type")
    private UpdateSDDataType dataType;

    @Enumerated(EnumType.STRING)
    private CommunicationRequestStatusType status;

    @Column(name = "received_data", columnDefinition = "TEXT")
    private String receivedData;
}
