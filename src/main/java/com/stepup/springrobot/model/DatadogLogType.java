package com.stepup.springrobot.model;

import java.util.HashMap;
import java.util.Map;

public enum DatadogLogType {
    // Existing conversation logs
    CONVERSATION_LLM_RESPONSE("CONVERSATION_LLM_RESPONSE"),
    CONVERSATION_FAST_RESPONSE("CONVERSATION_FAST_RESPONSE"),
    CONVERSATION_TTS("CONVERSATION_TTS"),
    CONVERSATION_UPLOAD_AUDIO("CONVERSATION_UPLOAD_AUDIO"),
    CONVERSATION_ANIMATION("CONVERSATION_ANIMATION"),
    CONVERSATION_SERVER_RESPONSE("CONVERSATION_SERVER_RESPONSE"),

    // WebSocket monitoring
    WEBSOCKET_CONNECTION_ESTABLISHED("WEBSOCKET_CONNECTION_ESTABLISHED"),
    WEBSOCKET_CONNECTION_CLOSED("WEBSOCKET_CONNECTION_CLOSED"),
    WEBSOCKET_MESSAGE_RECEIVED("WEBSOCKET_MESSAGE_RECEIVED"),
    WEBSOCKET_MESSAGE_SENT("WEBSOCKET_MESSAGE_SENT"),
    WEBSOCKET_ERROR("WEBSOCKET_ERROR"),

    // HTTP API monitoring
    HTTP_REQUEST_START("HTTP_REQUEST_START"),
    HTTP_REQUEST_END("HTTP_REQUEST_END"),
    HTTP_REQUEST_ERROR("HTTP_REQUEST_ERROR"),

    // External API calls monitoring
    EXTERNAL_API_CALL_START("EXTERNAL_API_CALL_START"),
    EXTERNAL_API_CALL_SUCCESS("EXTERNAL_API_CALL_SUCCESS"),
    EXTERNAL_API_CALL_ERROR("EXTERNAL_API_CALL_ERROR"),

    // MQTT monitoring
    MQTT_MESSAGE_PUBLISHED("MQTT_MESSAGE_PUBLISHED"),
    MQTT_MESSAGE_RECEIVED("MQTT_MESSAGE_RECEIVED"),
    MQTT_SUBSCRIPTION_ADDED("MQTT_SUBSCRIPTION_ADDED"),
    MQTT_SUBSCRIPTION_REMOVED("MQTT_SUBSCRIPTION_REMOVED"),
    MQTT_ERROR("MQTT_ERROR"),

    // Database monitoring
    DATABASE_QUERY_START("DATABASE_QUERY_START"),
    DATABASE_QUERY_END("DATABASE_QUERY_END"),
    DATABASE_QUERY_ERROR("DATABASE_QUERY_ERROR"),

    // Service flow monitoring
    SERVICE_METHOD_START("SERVICE_METHOD_START"),
    SERVICE_METHOD_END("SERVICE_METHOD_END"),
    SERVICE_METHOD_ERROR("SERVICE_METHOD_ERROR"),

    // Authentication & Authorization
    AUTH_LOGIN_SUCCESS("AUTH_LOGIN_SUCCESS"),
    AUTH_LOGIN_FAILED("AUTH_LOGIN_FAILED"),
    AUTH_TOKEN_VALIDATION("AUTH_TOKEN_VALIDATION"),
    AUTH_PERMISSION_DENIED("AUTH_PERMISSION_DENIED"),
    
    // User Journey Tracking
    USER_JOURNEY_TRACKING("USER_JOURNEY_TRACKING"),
    USER_JOURNEY_STAGE_COMPLETED("USER_JOURNEY_STAGE_COMPLETED"),
    USER_JOURNEY_STAGE_FAILED("USER_JOURNEY_STAGE_FAILED"),
    
    // User Error Tracking
    USER_ERROR_TRACKING("USER_ERROR_TRACKING"),
    USER_ERROR_RESOLUTION("USER_ERROR_RESOLUTION"),
    USER_ERROR_PATTERN("USER_ERROR_PATTERN"),
    USER_ERROR_STATISTICS("USER_ERROR_STATISTICS");

    private static final Map<String, DatadogLogType> TEXT_TO_SPEECH_VOICE_HASH_MAP = new HashMap<>();

    private final String feature;

    DatadogLogType(String feature) {
        this.feature = feature;
    }

    static {
        for (DatadogLogType e : values()) {
            TEXT_TO_SPEECH_VOICE_HASH_MAP.put(e.feature, e);
        }
    }

    public static DatadogLogType from(String feature) {
        return TEXT_TO_SPEECH_VOICE_HASH_MAP.get(feature);
    }

    public String getFeature() {
        return this.feature;
    }
}
