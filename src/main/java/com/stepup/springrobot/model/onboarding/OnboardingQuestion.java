package com.stepup.springrobot.model.onboarding;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.vladmihalcea.hibernate.type.json.JsonNodeBinaryType;
import lombok.*;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@TypeDef(name = "jsonb", typeClass = JsonNodeBinaryType.class)
@Table(name = "onboarding_question")
public class OnboardingQuestion extends ModifierTrackingEntity {
    @Id
    private Integer id;

    private String background;

    private String character;

    private String text;

    private String type;

    @Column(name = "question_order")
    private Double questionOrder;

    @JsonProperty("answer_type")
    @Enumerated(EnumType.STRING)
    @Column(name = "answer_type")
    private OnboardingAnswerType answerType;

    @Type(type = "jsonb")
    @Column(columnDefinition = "jsonb")
    private JsonNode data;
}
