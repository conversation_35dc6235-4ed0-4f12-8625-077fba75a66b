package com.stepup.springrobot.model.speech;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "wake_word_detections")
public class WakeWordDetection extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @Column(name = "robot_id")
    private Boolean robotId;

    @Column(name = "detected", nullable = false)
    private Boolean detected;

    @Column(name = "audio_url")
    private String audioUrl;

    @Column(name = "max_score")
    private Double maxScore;

    @Column(name = "threshold")
    private Double threshold;
}


