package com.stepup.springrobot.model.robot;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "robot_manufactures", indexes = {
        @Index(name = "robot_manufactures_idx_batch_number", columnList = "batch_number"),
        @Index(name = "robot_manufactures_idx_hardware_version", columnList = "hardware_version"),
})
public class RobotManufacture extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(255)")
    private String id;

    @Column(name = "robot_id")
    private String robotId;

    @Size(max = 100, message = "Device serial number must not exceed 100 characters")
    @Column(name = "device_serial_number", nullable = false, unique = true)
    private String deviceSerialNumber;

    @Size(max = 50, message = "Batch number must not exceed 50 characters")
    @Column(name = "batch_number", nullable = false)
    private String batchNumber;

    @NotNull(message = "Manufacturing status cannot be null")
    @Enumerated(EnumType.STRING)
    @Column(name = "manufacturing_status", nullable = false)
    private ManufacturingStatus manufacturingStatus;

    @Column(name = "hardware_version")
    private String hardwareVersion;

    @Column(name = "quality_check_notes", columnDefinition = "TEXT")
    private String qualityCheckNotes;

    @Column(name = "manufactured_date")
    private LocalDateTime manufacturedDate;

    @Column(name = "delivery_date")
    private LocalDateTime deliveryDate;

    @Column(name = "manufacturing_notes", columnDefinition = "TEXT")
    private String manufacturingNotes;

    public enum ManufacturingStatus {
        PLANNED,
        IN_PRODUCTION,
        QUALITY_CHECK,
        QUALITY_FAILED,
        READY_TO_SHIP,
        SHIPPED,
        ASSIGNED,
        CANCELLED
    }
} 