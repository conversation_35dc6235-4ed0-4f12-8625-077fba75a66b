package com.stepup.springrobot.model.robot;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "robot_error_logs", indexes = {
        @Index(name = "robot_error_logs_robot_id", columnList = "robot_id"),
        @Index(name = "robot_error_logs_err_type", columnList = "info_type")
})
public class RobotReportInfo extends ModifierTrackingEntity {
    @Id
    @Column(name = "id", columnDefinition = "VARCHAR(255)")
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    private String id;

    @NotNull
    @Column(name = "robot_id", nullable = false)
    private String robotId;

    @Column(name = "info_type", nullable = false, columnDefinition = "TEXT")
    private String infoType;

    @Column(name = "screen", nullable = false)
    private String screen;

    @Column(name = "firmware_version", nullable = false)
    private String firmwareVersion;

    @Column(name = "msg_detail", columnDefinition = "TEXT")
    private String msgDetail;
} 