package com.stepup.springrobot.model.robot;

import com.stepup.springrobot.model.ModifierTrackingEntity;
import com.stepup.springrobot.model.chat.STTHandlerType;
import lombok.*;

import javax.persistence.*;

@Builder
@EqualsAndHashCode(callSuper = false)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "robot_users", indexes = {
        @Index(name = "robot_users_robot_index", columnList = "user_id, robot_id, is_active", unique = true)
})
public class RobotUser extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private String userId;

    @Column(name = "robot_id", nullable = false)
    private String robotId;

    private String name;

    @Column(name = "is_active")
    private Boolean isActive;

    @Enumerated(EnumType.STRING)
    @Column(name = "asr_type")
    private STTHandlerType asrType;
}