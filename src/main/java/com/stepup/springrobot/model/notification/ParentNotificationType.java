package com.stepup.springrobot.model.notification;

/**
 * Enum defining different types of parent dashboard notifications
 * Used by frontend to implement specific display logic for each notification type
 */
public enum ParentNotificationType {
    STUDY_TIME_REMINDER("STUDY_TIME_REMINDER"),
    CHILD_NOT_STARTED_LEARNING("CHILD_NOT_STARTED_LEARNING"),
    LESSON_COMPLETED("LESSON_COMPLETED"),
    INCOMPLETE_LESSON("INCOMPLETE_LESSON"),
    PRONUNCIATION_ISSUE("PRONUNCIATION_ISSUE"),
    THREE_DAYS_ABSENCE("THREE_DAYS_ABSENCE"),
    WEEKLY_STUDY_REPORT("WEEKLY_STUDY_REPORT"),
    STUDY_BREAK_TIME("STUDY_BREAK_TIME"),
    CHILD_ACTIVELY_LEARNING("CHILD_ACTIVELY_LEARNING"),
    CUSTOM_STUDY_REMINDER("CUSTOM_STUDY_REMINDER");

    private final String type;

    ParentNotificationType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
} 