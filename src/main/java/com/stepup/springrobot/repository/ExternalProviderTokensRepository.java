package com.stepup.springrobot.repository;

import com.stepup.springrobot.model.ExternalProviderTokens;
import com.stepup.springrobot.model.ModelAIProvider;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ExternalProviderTokensRepository extends JpaRepository<ExternalProviderTokens, String> {
    List<ExternalProviderTokens> findByProvider(ModelAIProvider provider);
}