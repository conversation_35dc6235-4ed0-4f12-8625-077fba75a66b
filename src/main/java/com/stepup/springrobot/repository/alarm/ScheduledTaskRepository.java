package com.stepup.springrobot.repository.alarm;

import com.stepup.springrobot.model.alarm.ScheduledTask;
import com.stepup.springrobot.model.alarm.TaskStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ScheduledTaskRepository extends JpaRepository<ScheduledTask, Long> {
    
    /**
     * Find all pending tasks that are scheduled to run at or before the given time
     */
    @Query("SELECT st FROM ScheduledTask st WHERE st.status = :status AND st.scheduledTime <= :currentTime ORDER BY st.scheduledTime ASC")
    List<ScheduledTask> findByStatusAndScheduledTimeLessThanEqual(
            @Param("status") TaskStatus status, 
            @Param("currentTime") LocalDateTime currentTime
    );
    
    /**
     * Find tasks by task type and status
     */
    List<ScheduledTask> findByTaskTypeAndStatus(String taskType, TaskStatus status);
    
    /**
     * Find failed tasks that can be retried
     */
    @Query("SELECT st FROM ScheduledTask st WHERE st.status = 'FAILED' AND st.retryCount < st.maxRetries ORDER BY st.scheduledTime ASC")
    List<ScheduledTask> findFailedTasksForRetry();
    
    /**
     * Delete old completed or failed tasks
     */
    @Modifying
    @Query("DELETE FROM ScheduledTask st WHERE st.status IN ('DONE', 'FAILED') AND st.updatedAt < :cutoffDate")
    int deleteOldTasks(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    /**
     * Count tasks by status
     */
    long countByStatus(TaskStatus status);
    
    /**
     * Count tasks by task type and status
     */
    long countByTaskTypeAndStatus(String taskType, TaskStatus status);
} 