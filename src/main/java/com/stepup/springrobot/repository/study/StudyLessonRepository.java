package com.stepup.springrobot.repository.study;

import com.stepup.springrobot.model.study.StudyLesson;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface StudyLessonRepository extends JpaRepository<StudyLesson, Long> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE study_lessons RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();

    @Query(value = "SELECT * FROM study_lessons WHERE topic_id = :topic_id ORDER BY orders", nativeQuery = true)
    List<StudyLesson> getLessonsByTopicId(@Param("topic_id") Long topicId);

    @Query(value = "WITH CompletedConversationDurations AS (" +
            "SELECT ruc.bot_id, ruc.user_id, " +
            "EXTRACT(EPOCH FROM (COALESCE(MAX(ruch.created_at), ruc.created_at) - ruc.created_at)) AS duration_seconds " +
            "FROM robot_user_conversation ruc " +
            "LEFT JOIN robot_user_conversation_record_history ruch ON ruc.id = ruch.robot_user_conversation_id " +
            "WHERE ruc.bot_id BETWEEN 404 AND 422 " +
            "AND ruc.user_id NOT IN ('A085E3F89790', 'A085E3F88104', '806599CD0240') " +
            "AND ruc.server_log LIKE '%\"is_end_conversation\":true%' " +
            "AND ruc.server_log NOT LIKE '%Xin lỗi, hiện%' " +
            "GROUP BY ruc.id, ruc.bot_id, ruc.user_id), " +
            "AggregatedDurations AS (" +
            "SELECT bot_id, SUM(max_duration_per_user) AS total_longest_duration_seconds " +
            "FROM (SELECT bot_id, user_id, MAX(duration_seconds) AS max_duration_per_user " +
            "FROM CompletedConversationDurations GROUP BY bot_id, user_id) AS UserMaxDurations " +
            "GROUP BY bot_id), " +
            "UserCounts AS (" +
            "SELECT bot_id, COUNT(DISTINCT user_id) AS total_users, " +
            "COUNT(DISTINCT CASE WHEN server_log LIKE '%\"is_end_conversation\":true%' " +
            "AND server_log NOT LIKE '%Xin lỗi, hiện%' THEN user_id END) AS completed_users " +
            "FROM robot_user_conversation " +
            "WHERE bot_id BETWEEN 404 AND 422 " +
            "AND user_id NOT IN ('A085E3F89790', 'A085E3F88104', '806599CD0240') " +
            "GROUP BY bot_id) " +
            "SELECT uc.bot_id, uc.total_users, uc.completed_users, " +
            "FLOOR(COALESCE(ad.total_longest_duration_seconds, 0) / 60) || ' mins ' || " +
            "TRIM(TO_CHAR(MOD(COALESCE(ad.total_longest_duration_seconds, 0), 60), '999.999')) || ' secs' AS formatted_total_duration " +
            "FROM UserCounts uc LEFT JOIN AggregatedDurations ad ON uc.bot_id = ad.bot_id " +
            "ORDER BY uc.bot_id", nativeQuery = true)
    List<Object[]> getDataTrackingTimeRaw();

    @Query(value = "SELECT * FROM study_lessons WHERE bot_id IN :botIds", nativeQuery = true)
    List<StudyLesson> findLessonIdsByBotIds(@Param("botIds") List<Long> botIds);

  @Query(value = "SELECT DISTINCT sl.* \n" +
          "FROM study_lessons sl \n" +
          "INNER JOIN robot_user_conversation ruc ON sl.bot_id = ruc.bot_id \n" +
          "WHERE ruc.user_id = ?1 \n" +
          "AND ruc.created_at >= CURRENT_DATE - INTERVAL '6 days' \n" +
          "AND ruc.completed_timestamp IS NOT NULL", nativeQuery = true)
  List<StudyLesson> findCompletedLessonsInLast7DaysByUserId(String userId);
}