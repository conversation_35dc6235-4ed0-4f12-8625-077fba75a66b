package com.stepup.springrobot.repository.robot;

import com.stepup.springrobot.model.robot.RobotReportInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RobotReportInfoRepository extends JpaRepository<RobotReportInfo, String> {
    Page<RobotReportInfo> findAllByOrderByCreatedAtDesc(Pageable pageable);

    Page<RobotReportInfo> findByRobotIdOrderByCreatedAtDesc(String robotId, Pageable pageable);

    Page<RobotReportInfo> findByCreatedAtBetweenOrderByCreatedAtDesc(java.util.Date start, java.util.Date end, Pageable pageable);

    Page<RobotReportInfo> findByRobotIdAndCreatedAtBetweenOrderByCreatedAtDesc(String robotId, java.util.Date start, java.util.Date end, Pageable pageable);

    Page<RobotReportInfo> findByCreatedAtGreaterThanEqualOrderByCreatedAtDesc(java.util.Date start, Pageable pageable);

    Page<RobotReportInfo> findByCreatedAtLessThanEqualOrderByCreatedAtDesc(java.util.Date end, Pageable pageable);

    Page<RobotReportInfo> findByRobotIdAndCreatedAtGreaterThanEqualOrderByCreatedAtDesc(String robotId, java.util.Date start, Pageable pageable);

    Page<RobotReportInfo> findByRobotIdAndCreatedAtLessThanEqualOrderByCreatedAtDesc(String robotId, java.util.Date end, Pageable pageable);
}