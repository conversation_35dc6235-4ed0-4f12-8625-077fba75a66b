package com.stepup.springrobot.repository.robot;

import com.stepup.springrobot.model.robot.RobotUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RobotUserRepository extends JpaRepository<RobotUser, Long> {
    List<RobotUser> findByUserIdOrderByIdAsc(String userId);

    List<RobotUser> findByUserId(String userId);

    List<RobotUser> findByUserIdAndIsActiveTrue(String userId);

    RobotUser findFirstByUserIdOrderByIdDesc(String userId);

    Optional<RobotUser> findFirstByRobotIdOrderByIdDesc(String robotId);

    @Modifying
    @Query(value = "UPDATE robot_users SET is_active = false WHERE robot_id = ?1 or user_id = ?2", nativeQuery = true)
    void disableRobotUser(String robotId, String userId);

    @Query(value = "SELECT user_id FROM robot_users WHERE robot_id = ?1 and is_active = true", nativeQuery = true)
    Optional<String> getActiveRobotUser(String robotId);
}