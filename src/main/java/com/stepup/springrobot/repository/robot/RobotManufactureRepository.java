package com.stepup.springrobot.repository.robot;

import com.stepup.springrobot.model.robot.RobotManufacture;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RobotManufactureRepository extends JpaRepository<RobotManufacture, String> {

    Optional<RobotManufacture> findByDeviceSerialNumber(String deviceSerialNumber);

    List<RobotManufacture> findByBatchNumber(String batchNumber);

    List<RobotManufacture> findByManufacturingStatus(RobotManufacture.ManufacturingStatus status);

    boolean existsByDeviceSerialNumber(String deviceSerialNumber);
} 