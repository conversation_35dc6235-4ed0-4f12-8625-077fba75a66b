package com.stepup.springrobot.repository.chat;

import com.stepup.springrobot.model.chat.RobotUserConversation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

public interface RobotUserConversationRepository
        extends JpaRepository<RobotUserConversation, Long>, JpaSpecificationExecutor<RobotUserConversation> {
    RobotUserConversation findBySocketSessionId(String socketSessionId);

    Page<RobotUserConversation> findAllByOrderByIdDesc(Pageable pageable);

    RobotUserConversation findFirstByRobotIdAndBotIdOrderByIdDesc(String robotId, Long botId);

    RobotUserConversation findFirstByUserIdAndBotIdOrderByIdDesc(String userId, Long botId);

    RobotUserConversation findByExternalConversationId(String externalConversationId);

    @Query(value = "SELECT * FROM robot_user_conversation ruc WHERE ruc.log is not null ORDER BY ruc.id DESC", nativeQuery = true)
    Page<RobotUserConversation> findConversationsWithLog(Pageable pageable);

    @Transactional
    @Modifying
    @Query(value = "UPDATE robot_user_conversation SET phone = ?2 WHERE id = ?1", nativeQuery = true)
    void updatePhone(Long conversationId, String phone);

    @Transactional
    @Modifying
    @Query(value = "UPDATE robot_user_conversation SET conversation_reports = ?2 WHERE id = ?1", nativeQuery = true)
    void updateConversationReports(Long conversationId, String conversationReports);

    @Transactional
    @Modifying
    @Query(value = "UPDATE robot_user_conversation SET conversation_summaries = ?2 WHERE external_conversation_id = ?1", nativeQuery = true)
    void updateConversationSummary(String externalConversationId, String conversationSummary);

    @Query(value = "SELECT conversation_reports FROM robot_user_conversation WHERE id = ?1", nativeQuery = true)
    String findConversationReportsById(Long conversationId);

    List<RobotUserConversation> findByIdIn(Collection<Long> ids);

    @Query(value = "SELECT user_id " +
            "FROM robot_user_conversation " +
            "WHERE phone = ?1 or user_id = ?1 " +
            "LIMIT 1", nativeQuery = true)
    String getMVPUserIdByPhoneOrMVPId(String input);

    @Query(value = "WITH conversation_durations AS (" +
            "SELECT " +
            "ruc.id, " +
            "ruc.phone, " +
            "ruc.bot_id, " +
            "ruc.created_at as start_time, " +
            "COALESCE(MAX(ruch.created_at), ruc.created_at) as end_time, " +
            "EXTRACT(EPOCH FROM (COALESCE(MAX(ruch.created_at), ruc.created_at) - ruc.created_at)) as duration_seconds, " +
            "DATE(ruc.created_at) as conversation_date " +
            "FROM robot_user_conversation ruc " +
            "LEFT JOIN robot_user_conversation_record_history ruch ON ruc.id = ruch.robot_user_conversation_id " +
            "WHERE ruc.phone = ?1 " +
            "GROUP BY ruc.id, ruc.phone, ruc.bot_id, ruc.created_at" +
            "), " +
            "max_duration_per_day AS (" +
            "SELECT " +
            "conversation_date, " +
            "phone, " +
            "MAX(duration_seconds) as max_duration " +
            "FROM conversation_durations " +
            "GROUP BY conversation_date, phone" +
            ") " +
            "SELECT ruc.* " +
            "FROM robot_user_conversation ruc " +
            "INNER JOIN conversation_durations cd ON ruc.id = cd.id " +
            "INNER JOIN max_duration_per_day mdpd ON cd.conversation_date = mdpd.conversation_date " +
            "AND cd.phone = mdpd.phone " +
            "AND cd.duration_seconds = mdpd.max_duration " +
            "ORDER BY cd.conversation_date DESC", nativeQuery = true)
    List<RobotUserConversation> findLongestConversationByPhonePerDay(String phone);

    @Query(value = "WITH ranked_conversations AS (\n" +
            "    SELECT\n" +
            "        ruc.id,\n" +
            "        -- Lấy số điện thoại từ ruc hoặc từ bảng user nếu ruc.phone là NULL\n" +
            "        COALESCE(ruc.phone, u.phone) as phone,\n" +
            "        ruc.bot_id,\n" +
            "        ruc.created_at AS start_time,\n" +
            "        COALESCE(MAX(ruch.created_at), ruc.created_at) AS end_time,\n" +
            "        -- Xếp hạng các cuộc hội thoại trong mỗi ngày và mỗi bot_id theo thời lượng\n" +
            "        ROW_NUMBER() OVER(\n" +
            "            PARTITION BY DATE(ruc.created_at), ruc.bot_id -- Phân nhóm theo ngày và bot_id\n" +
            "            ORDER BY COALESCE(MAX(ruch.created_at), ruc.created_at) - ruc.created_at DESC\n" +
            "            ) as rn\n" +
            "    FROM\n" +
            "        robot_user_conversation ruc\n" +
            "            -- Join với bảng user để lấy phone khi ruc.phone là NULL\n" +
            "            LEFT JOIN\n" +
            "        users u ON ruc.user_id = u.id\n" +
            "            LEFT JOIN\n" +
            "        robot_user_conversation_record_history ruch ON ruc.id = ruch.robot_user_conversation_id\n" +
            "    WHERE\n" +
            "        -- Lọc theo phone từ một trong hai bảng\n" +
            "        COALESCE(ruc.phone, u.phone) = ?1\n" +
            "    GROUP BY\n" +
            "        ruc.id, COALESCE(ruc.phone, u.phone), ruc.bot_id, ruc.created_at\n" +
            ")\n" +
            "SELECT\n" +
            "    rc.id AS conversationId,\n" +
            "    rc.phone,\n" +
            "    rc.bot_id AS botId,\n" +
            "    rc.start_time AS startTime,\n" +
            "    rc.end_time AS endTime,\n" +
            "    DATE(rc.start_time) AS conversationDate,\n" +
            "    EXTRACT(EPOCH FROM (rc.end_time - rc.start_time)) AS durationSeconds\n" +
            "FROM\n" +
            "    ranked_conversations rc\n" +
            "WHERE\n" +
            "    rc.rn = 1 -- Chỉ lấy cuộc hội thoại dài nhất cho mỗi bot_id trong mỗi ngày\n" +
            "ORDER BY\n" +
            "    conversationDate DESC, rc.bot_id ASC", nativeQuery = true)
    List<Object[]> findLongestConversationDetailsPerDay(String phone);

    @Query(value = "SELECT COUNT(*) > 0 FROM robot_user_conversation " +
            "WHERE user_id = ?1 " +
            "AND DATE(created_at) = CURRENT_DATE", nativeQuery = true)
    boolean hasUserStartedLearningToday(String userId);

    @Query(value = "SELECT * FROM robot_user_conversation " +
            "WHERE user_id = ?1 " +
            "AND DATE(created_at) = CURRENT_DATE " +
            "ORDER BY created_at DESC " +
            "LIMIT 1", nativeQuery = true)
    RobotUserConversation findTodayLatestConversationByUserId(String userId);

    @Query(value = "SELECT COUNT(*) > 0 FROM robot_user_conversation " +
            "WHERE user_id = ?1 " +
            "AND created_at >= ?2 " +
            "AND DATE(created_at) = CURRENT_DATE", nativeQuery = true)
    boolean hasUserStartedLearningSince(String userId, java.sql.Timestamp sinceTime);

    @Query(value = "SELECT COALESCE(SUM(duration_minutes), 0) as total_minutes FROM (" +
            "SELECT EXTRACT(EPOCH FROM (COALESCE(MAX(ruch.created_at), ruc.created_at) - ruc.created_at)) / 60 as duration_minutes " +
            "FROM robot_user_conversation ruc " +
            "LEFT JOIN robot_user_conversation_record_history ruch ON ruc.id = ruch.robot_user_conversation_id " +
            "WHERE ruc.user_id = ?1 " +
            "AND DATE(ruc.created_at) = CURRENT_DATE " +
            "GROUP BY ruc.id" +
            ") as conversation_durations", nativeQuery = true)
    Double getTotalStudyTimeMinutesToday(String userId);

    @Query(value = "SELECT DISTINCT ruc.bot_id " +
            "FROM robot_user_conversation ruc " +
            "WHERE ruc.user_id = ?1 " +
            "AND DATE(ruc.created_at) = CURRENT_DATE " +
            "AND ruc.completed_timestamp IS NOT NULL ", nativeQuery = true)
    List<Long> getCompletedBotIdsToday(String userId);
}