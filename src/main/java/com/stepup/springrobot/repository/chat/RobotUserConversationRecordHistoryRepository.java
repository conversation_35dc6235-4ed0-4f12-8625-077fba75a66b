package com.stepup.springrobot.repository.chat;

import com.stepup.springrobot.dto.ConversationReportDTO;
import com.stepup.springrobot.model.chat.AICharacter;
import com.stepup.springrobot.model.chat.RobotUserConversationRecordHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;

public interface RobotUserConversationRecordHistoryRepository extends JpaRepository<RobotUserConversationRecordHistory, Long> {
    Page<RobotUserConversationRecordHistory> findByRobotUserConversationIdOrderByIdAsc(Long conversationId, Pageable pageable);

    Page<RobotUserConversationRecordHistory> findByRobotUserConversationIdAndIdGreaterThanOrderByIdAsc(
            Long conversationId,
            Long afterId,
            Pageable pageable
    );

    Page<RobotUserConversationRecordHistory> findAllByDataReportNotNull(Pageable pageable);

    RobotUserConversationRecordHistory findFirstByRobotUserConversationIdAndCharacterOrderByIdDesc(Long robotUserConversationId, AICharacter character);

    @Query(value = "SELECT * FROM robot_user_conversation_record_history WHERE robot_user_conversation_id = ?1 AND character IN ?2 ORDER BY ID", nativeQuery = true)
    List<RobotUserConversationRecordHistory> findByRobotUserConversationId(Long robotUserConversationId, List<String> characters);

    RobotUserConversationRecordHistory findFirstByRobotUserConversationIdAndCharacterInOrderByIdDesc(Long robotUserConversationId, Collection<AICharacter> characters);

    @Query(value = "SELECT new com.stepup.springrobot.dto.ConversationReportDTO(r.content, r.character) " +
            "FROM RobotUserConversationRecordHistory r " +
            "WHERE r.robotUserConversationId = :conversationId " +
            "ORDER BY r.id ASC")
    List<ConversationReportDTO> findConversationReportByConversationId(@Param("conversationId") Long conversationId);

    @Query(value = "SELECT rch.audio, rch.content, rch.language, rch.character " +
            "FROM robot_user_conversation_record_history rch " +
            "WHERE rch.robot_user_conversation_id = :conversationId " +
            "ORDER BY rch.id ASC", nativeQuery = true)
    List<Object[]> findConversationLanguagePrediction(
            @Param("conversationId") Long conversationId);

    RobotUserConversationRecordHistory findFirstByRobotUserConversationIdOrderByIdDesc(Long robotUserConversationId);

    // Find all records with character USER using native query for better performance
    @Query(value = "SELECT * FROM robot_user_conversation_record_history WHERE character = :character ORDER BY id DESC", 
           nativeQuery = true)
    Page<RobotUserConversationRecordHistory> findByCharacter(@Param("character") String character, Pageable pageable);

    // Find all USER records that have been QC'd (any QC field is not null)
    @Query(value = "SELECT * FROM robot_user_conversation_record_history " +
                   "WHERE character = :character " +
                   "AND (is_asr_correct IS NOT NULL OR asr_corrected_content IS NOT NULL " +
                   "OR is_intent_affected IS NOT NULL OR intent_corrected_content IS NOT NULL) " +
                   "ORDER BY id DESC", 
           nativeQuery = true)
    Page<RobotUserConversationRecordHistory> findUserRecordsWithQc(@Param("character") String character, Pageable pageable);
}