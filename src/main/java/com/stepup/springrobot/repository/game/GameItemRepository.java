package com.stepup.springrobot.repository.game;

import com.stepup.springrobot.model.game.GameItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GameItemRepository extends JpaRepository<GameItem, Long> {
    List<GameItem> findByUserIdAndIsActiveTrue(String userId);

    List<GameItem> findByUserId(String userId);

    boolean existsByUserIdAndItemId(String userId, String itemId);

    void deleteByUserIdAndItemId(String userId, String itemId);
} 