package com.stepup.springrobot.repository.learn;

import com.stepup.springrobot.dto.learn.LessonDetailDTO;
import com.stepup.springrobot.model.learn.LessonDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface LessonDetailRepository extends JpaRepository<LessonDetail, String> {
    @Transactional
    @Modifying
    @Query(value = "TRUNCATE TABLE lesson_details RESTART IDENTITY CASCADE", nativeQuery = true)
    void truncateTable();

    List<LessonDetail> findByLessonIdOrderByOrderAsc(Long lessonId);

    @Query(value = "SELECT lesson_id as lessonId, id FROM lesson_details WHERE data ->> 'bot_id' = ?1", nativeQuery = true)
    List<LessonDetailDTO> getStudyLessonIdByBotId(String botId);
}