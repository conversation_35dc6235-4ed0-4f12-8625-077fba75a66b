package com.stepup.springrobot.repository.auth;

import com.stepup.springrobot.model.auth.RefreshToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RefreshTokenRepository extends JpaRepository<RefreshToken, String> {
    Optional<RefreshToken> findByToken(String token);

    List<RefreshToken> findByUserId(String userId);

    RefreshToken findByUserIdAndDeviceId(String userId, String deviceId);

    void deleteByUserIdAndDeviceId(String userId, String deviceId);
}