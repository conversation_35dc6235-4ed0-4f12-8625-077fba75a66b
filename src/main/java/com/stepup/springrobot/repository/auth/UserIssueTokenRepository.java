package com.stepup.springrobot.repository.auth;

import com.stepup.springrobot.model.auth.UserIssueToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserIssueTokenRepository extends JpaRepository<UserIssueToken, Long> {
    @Query(value = "select * from user_issue_token where token = ?1", nativeQuery = true)
    UserIssueToken findByToken(String tokenSign);

    List<UserIssueToken> findByUserIdOrderByIdAsc(String userId);

    UserIssueToken findByDeviceId(String deviceId);

    long deleteByUserIdAndDeviceId(String userId, String deviceId);

    @Modifying
    @Query(value = "UPDATE user_issue_token SET device_token = NULL WHERE device_token = ?1", nativeQuery = true)
    void clearDeviceTokenByToken(String deviceToken);
}