package com.stepup.springrobot.websocket.handler;

import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.model.chat.ConversationSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.util.MultiValueMap;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Map;

@Slf4j
public class ParamInterceptor implements HandshakeInterceptor {
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        String botId = extractParam(request, CodeDefine.WEBSOCKET_PARAM_BOT_ID);
        if (botId != null) {
            attributes.put("bot_id", botId);
        }

        String isConvert = extractParam(request, CodeDefine.WEBSOCKET_PARAM_IS_CONVERT);
        if (isConvert != null) {
            attributes.put("is_convert", isConvert);
        }

        String token = extractParam(request, "token");
        if (token != null) {
            attributes.put("token", token);
        }

        String robotId = extractParam(request, "robot_id");
        if (robotId != null) {
            attributes.put("robot_id", robotId);
        }

        String audioFormat = extractParam(request, "audio_format");
        if (audioFormat != null) {
            attributes.put("audio_format", audioFormat);
        }

        String isWebMvp = extractParam(request, "is_web_mvp");
        if (isWebMvp != null) {
            attributes.put("is_web_mvp", isWebMvp);
        }

        // Determine conversation source from endpoint
        String endpoint = request.getURI().getPath();
        ConversationSource conversationSource = getConversationSourceFromEndpoint(endpoint);
        attributes.put("conversation_source", conversationSource);

        return true;
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Exception exception) {

    }

    private String extractParam(ServerHttpRequest request, String param) {
        MultiValueMap<String, String> parameters =
                UriComponentsBuilder.fromUri(request.getURI()).build().getQueryParams();

        List<String> params = parameters.get(param);
        return params != null && !params.isEmpty() ? params.get(0) : null;
    }

    private ConversationSource getConversationSourceFromEndpoint(String endpoint) {
        if (endpoint == null) {
            return ConversationSource.ROBOT; // default
        }

        switch (endpoint) {
            case "/ws/app/free_talk":
                return ConversationSource.APP;
            case "/ws/web/free_talk":
                return ConversationSource.WEB_MVP;
            default:
                // This covers CodeDefine.SOCKET_VER_2_ENDPOINT (/ws/free_talk) and /ws/v2/free_talk
                return ConversationSource.ROBOT;
        }
    }
}
