package com.stepup.springrobot.websocket.handler;

import com.stepup.springrobot.model.chat.ConversationSource;
import com.stepup.springrobot.repository.robot.RobotUserRepository;
import com.stepup.springrobot.security.JwtService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import java.util.Map;

@Slf4j
@Service
public class WebSocketUtils {
    @Autowired
    private JwtService jwtService;

    @Autowired
    private RobotUserRepository robotUserRepository;

    public String getUserIdFromSocketSession(WebSocketSession session) {
        String userId = null;
        try {
            String token = (String) session.getAttributes().get("token");
            if (!StringUtils.isEmpty(token)) {
                Map<String, Object> claims = jwtService.extractAllClaims(token);
                userId = claims.get("user_id").toString();
                log.info("user_id: {}", userId);
            }
        } catch (Exception e) {
            log.error("Lỗi lấy userId từ token: {}", e.getMessage(), e);
        }

        if (StringUtils.isEmpty(userId)) {
            userId = (String) session.getAttributes().get("robot_id");

            if (!StringUtils.isEmpty(userId)) {
                userId = robotUserRepository.findFirstByRobotIdOrderByIdDesc(userId).isPresent() ?
                        robotUserRepository.findFirstByRobotIdOrderByIdDesc(userId).get().getUserId() :
                        userId;
            }
        }

        if (StringUtils.isEmpty(userId)) {
            userId = "undefined";
        }

        return userId;
    }

    public String getRobotIdFromSocketSession(WebSocketSession session) {
        String robotId = (String) session.getAttributes().get("robot_id");

        if (StringUtils.isEmpty(robotId)) {
            robotId = "undefined";
        }

        return robotId;
    }

    public String getAudioFormatFromSocketSession(WebSocketSession session) {
        String audioFormat = (String) session.getAttributes().get("audio_format");

        if (StringUtils.isEmpty(audioFormat)) {
            audioFormat = "undefined";
        }

        return audioFormat;
    }


    public ConversationSource getConversationSourceFromSession(WebSocketSession session) {
        // First try to get from session attributes (set by ParamInterceptor)
        ConversationSource source = (ConversationSource) session.getAttributes().get("conversation_source");
        if (source != null) {
            return source;
        }

        // Fallback to determining from endpoint
        String endpoint = session.getUri() != null ? session.getUri().getPath() : null;
        return getConversationSourceFromEndpoint(endpoint);
    }

    public ConversationSource getConversationSourceFromEndpoint(String endpoint) {
        if (endpoint == null) {
            return ConversationSource.ROBOT; // default
        }

        switch (endpoint) {
            case "/ws/app/free_talk":
                return ConversationSource.APP;
            case "/ws/web/free_talk":
                return ConversationSource.WEB_MVP;
            default:
                // This covers CodeDefine.SOCKET_VER_2_ENDPOINT (/ws/free_talk) and /ws/v2/free_talk
                return ConversationSource.ROBOT;
        }
    }
}
