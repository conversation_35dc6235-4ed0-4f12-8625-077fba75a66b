package com.stepup.springrobot.websocket.interceptor;

import com.stepup.springrobot.dto.monitoring.TraceContextDTO;
import com.stepup.springrobot.dto.monitoring.WebSocketEventDTO;
import com.stepup.springrobot.service.DatadogService;
import com.stepup.springrobot.service.TraceContextService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.time.Instant;
import java.util.Map;

@Slf4j
@Component
public class MonitoringWebSocketInterceptor implements HandshakeInterceptor {

    @Autowired
    private DatadogService datadogService;

    @Autowired
    private TraceContextService traceContextService;

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) {

        // Create trace context for WebSocket connection
        String userId = extractUserIdFromRequest(request);
        String robotId = extractRobotIdFromRequest(request);

        TraceContextDTO traceContext = traceContextService.createNewTrace(userId, "websocket_session");
        traceContextService.updateTraceContext(robotId, null, null);

        // Store trace context in WebSocket session attributes
        attributes.put("traceContext", traceContext);
        attributes.put("connectionStartTime", System.currentTimeMillis());

        // Log WebSocket handshake
        WebSocketEventDTO event = WebSocketEventDTO.builder()
                .sessionId("pending") // Session ID not available yet
                .endpoint(request.getURI().getPath())
                .eventType("HANDSHAKE")
                .userId(userId)
                .robotId(robotId)
                .remoteAddress(request.getRemoteAddress() != null ? request.getRemoteAddress().toString() : "unknown")
                .timestamp(Instant.now())
                .build();

        datadogService.logWebSocketEvent(event);

        log.info("WebSocket handshake initiated for user: {}, robot: {}, endpoint: {}",
                userId, robotId, request.getURI().getPath());

        return true;
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {

        if (exception != null) {
            String userId = extractUserIdFromRequest(request);
            String robotId = extractRobotIdFromRequest(request);

            WebSocketEventDTO event = WebSocketEventDTO.builder()
                    .sessionId("failed")
                    .endpoint(request.getURI().getPath())
                    .eventType("HANDSHAKE_FAILED")
                    .userId(userId)
                    .robotId(robotId)
                    .errorMessage(exception.getMessage())
                    .timestamp(Instant.now())
                    .build();

            datadogService.logWebSocketEvent(event);

            log.error("WebSocket handshake failed for user: {}, robot: {}, error: {}",
                    userId, robotId, exception.getMessage());
        }
    }

    private String extractUserIdFromRequest(ServerHttpRequest request) {
        String userId = request.getURI().getQuery();
        if (userId != null && userId.contains("userId=")) {
            String[] params = userId.split("&");
            for (String param : params) {
                if (param.startsWith("userId=")) {
                    return param.substring("userId=".length());
                }
            }
        }
        return "anonymous";
    }

    private String extractRobotIdFromRequest(ServerHttpRequest request) {
        String query = request.getURI().getQuery();
        if (query != null && query.contains("robotId=")) {
            String[] params = query.split("&");
            for (String param : params) {
                if (param.startsWith("robotId=")) {
                    return param.substring("robotId=".length());
                }
            }
        }
        return "unknown";
    }
}
