package com.stepup.springrobot.websocket.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.chat.*;
import com.stepup.springrobot.dto.monitoring.MonitoringLogDTO;
import com.stepup.springrobot.dto.study.AssetManifestDTO;
import com.stepup.springrobot.model.DatadogLogType;
import com.stepup.springrobot.model.chat.ConversationLogType;
import com.stepup.springrobot.model.chat.ConversationSource;
import com.stepup.springrobot.model.chat.RobotConversationResponseType;
import com.stepup.springrobot.model.chat.STTHandlerType;
import com.stepup.springrobot.service.AIRobotConversationService;
import com.stepup.springrobot.service.DatadogService;
import com.stepup.springrobot.service.SharedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class RobotNewConversationSessionService extends BaseConversationSessionService {
    private final long conversationTimeoutMinutes;

    private final Instant startConversationTime;

    public RobotNewConversationSessionService(WebSocketSession session, ObjectMapper objectMapper,
                                              AIRobotConversationService aiRobotConversationService, long conversationTimeoutMinutes,
                                              String socketHost, String grpcAsrUri, String grpcAsrUriEn,
                                              String userId, String robotId, String audioFormat, STTHandlerType sttHandlerType, String stressTestRobotIds,
                                              SharedService sharedService, DatadogService datadogService) {
        super(session, objectMapper, aiRobotConversationService, conversationTimeoutMinutes, socketHost,
                grpcAsrUri, grpcAsrUriEn,
                userId, robotId, audioFormat, sttHandlerType, stressTestRobotIds, sharedService, datadogService);
        this.conversationTimeoutMinutes = conversationTimeoutMinutes;
        this.startConversationTime = Instant.now();
    }

    @Override
    public void initializeSessionConfig() {
        startPingTask();
        Instant start = Instant.now();
        Long botId = getBotIdParam();
        StringBuilder logMessage = new StringBuilder();
        boolean isWebMvp = getIsFromWebMvp();
        ConversationSource conversationSource = getConversationSource();
        RobotConversationMsgResDTO robotConversationMsgResDTO = aiRobotConversationService.sendCustomUserSentence(
                userId, robotId, "Hello", session.getId(), session.getRemoteAddress().toString(), botId, logMessage,
                sttHandlerType, null, isWebMvp, conversationSource);
        language = robotConversationMsgResDTO.getLanguage();
        silenceThreshold = robotConversationMsgResDTO.getSilenceThreshold();

        List<AIRobotConversationResDTO> resDTOS = robotConversationMsgResDTO.getMessages();
        Long conversationId = resDTOS.get(0).getConversationId();

        //TO DO: get manifest from DB
        AssetManifestDTO manifestDTO = AssetManifestDTO.builder()
                .isPreload(robotConversationMsgResDTO.getIsPreload())
                .images(robotConversationMsgResDTO.getGifManifestDTOS())
                .build();

        RobotConversationMessageDTO audioMessage = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_RESPONSE)
                .manifest(manifestDTO)
                .data(RobotChatResponseDTO.builder()
                        .messages(robotConversationMsgResDTO.getResponseMessages())
                        .listeningEmotion(robotConversationMsgResDTO.getListeningEmotion())
                        .gifs(robotConversationMsgResDTO.getGifs())
                        .answerMode(robotConversationMsgResDTO.getAnswerMode())
                        .build())
                .socketSessionId(session.getId())
                .conversationId(conversationId)
                .build();

        if (session.isOpen()) {
            try {
                String chatResponse = objectMapper.writeValueAsString(audioMessage);
                session.sendMessage(new TextMessage(chatResponse)); // Send the bot response back to the user
                long duration = Duration.between(start, Instant.now()).toMillis();
                sharedService.saveConversationLog(conversationId,
                        ConversationLogType.SERVER_RESPONSE, chatResponse, duration, audioMessage.getIsSessionCompleted());

                // send to datadog
                String remoteIp = null;
                if (session.getRemoteAddress() != null && session.getRemoteAddress().getAddress() != null) {
                    remoteIp = session.getRemoteAddress().getAddress().getHostAddress();
                }
                MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                        .logType(DatadogLogType.CONVERSATION_SERVER_RESPONSE)
                        .robotId(robotId)
                        .statusCode(CodeDefine.OK)
                        .conversationId(conversationId)
                        .durationMs(duration)
                        .clientIp(remoteIp)
                        .message("OK")
                        .level("INFO")
                        .timestamp(Instant.now())
                        .build();
                datadogService.handleMonitoringLog(monitoringLog);

                logInfo("(BE - Robot) Send bot response - conversation_id: " + conversationId
                        + ", response: " + chatResponse);
                String responseTime = "ROBOT - res time: " + duration + "ms";
                logMessage.insert(0, responseTime);
                log.error(logMessage.toString());
            } catch (IOException e) {
                logError(e, "Failed to send first bot response");

                // send to datadog
                MonitoringLogDTO monitoringLog = MonitoringLogDTO.builder()
                        .logType(DatadogLogType.CONVERSATION_SERVER_RESPONSE)
                        .robotId(robotId)
                        .statusCode(CodeDefine.SERVER_ERROR)
                        .conversationId(conversationId)
                        .durationMs(Duration.between(start, Instant.now()).toMillis())
                        .clientIp(session.getRemoteAddress().getAddress().getHostAddress())
                        .message("Failed to send first bot response" + e.getMessage())
                        .level("ERROR")
                        .timestamp(Instant.now())
                        .build();
                datadogService.handleMonitoringLog(monitoringLog);
            }
        }
    }

    protected void sendEndConversationTimeOut() {
        List<RobotResponseMessageDTO> responseMessageDTOS = List.of(RobotResponseMessageDTO.builder()
                .text(CodeDefine.CONVERSATION_FINISH_SENTENCE)
                .audio(CodeDefine.CONVERSATION_FINISH_AUDIO)
                .build());

        RobotConversationMessageDTO audioMessage = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_RESPONSE)
                .data(RobotChatResponseDTO.builder()
                        .messages(responseMessageDTOS)
                        .hasNextMessage(false)
                        .isEndConversation(true)
                        .build())
                .socketSessionId(session.getId())
                .build();

        if (session.isOpen()) {
            try {
                String chatResponse = objectMapper.writeValueAsString(audioMessage);
                session.sendMessage(new TextMessage(chatResponse)); // Send the bot response back to the user
                logInfo("(BE - Robot) Send bot response: " + chatResponse);
            } catch (IOException e) {
                logError(e, "Failed to send last bot response");
            }
        }
    }

    @Override
    protected byte[] convertAudioFormat(byte[] audioData) {
        return audioData;
    }

    @Override
    protected File convertToWavFile(byte[] audioData, String fileName) {
        return createWavFileFromPcm(audioData, fileName, log);
    }

    public static File createWavFileFromPcm(byte[] audioData, String fileName, org.slf4j.Logger logger) {
        File outputFile = new File(fileName);

        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
             FileOutputStream fileOutputStream = new FileOutputStream(outputFile)) {

            // Calculate data size from the actual audio data
            int dataSize = audioData.length;
            byte[] header = createWavHeader(dataSize);

            // Combine header and audio data
            outputStream.write(header);
            outputStream.write(audioData);

            // Write to file
            fileOutputStream.write(outputStream.toByteArray());

            if (logger != null) {
                logger.info("WAV file created: {}", outputFile.getAbsolutePath());
            }
            return outputFile;

        } catch (IOException e) {
            if (logger != null) {
                logger.error("Error converting audio data to WAV", e);
            }
            if (outputFile.exists()) {
                outputFile.delete();
            }
            return null;
        }
    }

    private static byte[] createWavHeader(int dataSize) {
        int HEADER_SIZE = 44;
        int CHANNELS = 1;
        int SAMPLE_RATE = 16000;
        int BITS_PER_SAMPLE = 16;
        ByteBuffer buffer = ByteBuffer.allocate(HEADER_SIZE);
        buffer.order(ByteOrder.LITTLE_ENDIAN);

        // RIFF header
        buffer.put("RIFF".getBytes()); // ChunkID
        buffer.putInt(dataSize + 36); // ChunkSize
        buffer.put("WAVE".getBytes()); // Format
        // fmt subchunk
        buffer.put("fmt ".getBytes()); // Subchunk1ID
        buffer.putInt(16); // Subchunk1Size
        buffer.putShort((short) 1); // AudioFormat (PCM)
        buffer.putShort((short) CHANNELS); // NumChannels
        buffer.putInt(SAMPLE_RATE); // SampleRate
        buffer.putInt(SAMPLE_RATE * CHANNELS * BITS_PER_SAMPLE / 8); // ByteRate
        buffer.putShort((short) (CHANNELS * BITS_PER_SAMPLE / 8)); // BlockAlign
        buffer.putShort((short) BITS_PER_SAMPLE); // BitsPerSample

        // data subchunk
        buffer.put("data".getBytes()); // Subchunk2ID
        buffer.putInt(dataSize); // Subchunk2Size

        return buffer.array();
    }

    @Override
    protected RobotConversationMessageDTO handleGetBotResponse(String transcript, File file, String sessionId,
                                                               String speechToTextResponse, Long botId, StringBuilder logMessage) {
        if (!Arrays.asList(stressTestRobotIds.split(",")).contains(userId) && Duration
                .between(startConversationTime, Instant.now()).toMillis() > conversationTimeoutMinutes * 60 * 1000) {
            log.error("======== Hết thời gian hội thoại");
            log.error("======== Hết thời gian hội thoại");
            sendEndConversationTimeOut();
            return null;
        }

        String fileName = file.getName();
        ConversationSource conversationSource = getConversationSource();
        RobotConversationMsgResDTO robotConversationMsgResDTO = aiRobotConversationService.getRobotResponse(userId, robotId,
                transcript, file, sessionId, speechToTextResponse, session.getRemoteAddress().toString(), botId,
                logMessage, STTHandlerType.ASR, true, conversationSource);
        language = robotConversationMsgResDTO.getLanguage();
        silenceThreshold = robotConversationMsgResDTO.getSilenceThreshold();

        List<AIRobotConversationResDTO> resDTOS = robotConversationMsgResDTO.getMessages();
        String status = resDTOS.get(resDTOS.size() - 1).getStatus();
        if (Objects.equals(status, "ACTION")) {
            CompletableFuture.runAsync(() -> handleGetMoreResponse(botId, logMessage,
                    aiRobotConversationService.getUserAudioUrl(fileName)));
        }

        boolean hasNextMessage = Objects.equals(status, "ACTION");
        boolean isConversationCompleted = Objects.equals(status, "END");

        return RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_RESPONSE)
                .data(RobotChatResponseDTO.builder()
                        .messages(robotConversationMsgResDTO.getResponseMessages())
                        .hasNextMessage(hasNextMessage)
                        .listeningEmotion(hasNextMessage ? null : robotConversationMsgResDTO.getListeningEmotion())
                        .answerMode(robotConversationMsgResDTO.getAnswerMode())
                        .isEndConversation(isConversationCompleted || Objects.equals(status, "ERROR"))
                        .build())
                .socketSessionId(session.getId())
                .conversationId(resDTOS.get(0).getConversationId())
                .isSessionCompleted(isConversationCompleted)
                .build();
    }

    private void handleGetMoreResponse(Long botId, StringBuilder logMessage, String userAudio) {
        Instant start = Instant.now();

        ConversationSource conversationSource = getConversationSource();

        RobotConversationMsgResDTO robotConversationMsgResDTO = aiRobotConversationService.sendCustomUserSentence(
                userId, robotId, "ACTION", session.getId(), session.getRemoteAddress().toString(), botId, logMessage,
                sttHandlerType, userAudio, false, conversationSource);
        language = robotConversationMsgResDTO.getLanguage();
        silenceThreshold = robotConversationMsgResDTO.getSilenceThreshold();

        List<AIRobotConversationResDTO> resDTOS = robotConversationMsgResDTO.getMessages();
        Long conversationId = resDTOS.get(0).getConversationId();
        RobotConversationMessageDTO stallingMsg = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_RESPONSE)
                .data(RobotChatResponseDTO.builder()
                        .messages(robotConversationMsgResDTO.getResponseMessages())
                        .listeningEmotion(robotConversationMsgResDTO.getListeningEmotion())
                        .answerMode(robotConversationMsgResDTO.getAnswerMode())
                        .build())
                .socketSessionId(session.getId())
                .conversationId(conversationId)
                .build();
        try {
            String chatResponse = objectMapper.writeValueAsString(stallingMsg);
            session.sendMessage(new TextMessage(chatResponse)); // Send the bot response back to the user
            sharedService.saveConversationLog(conversationId, ConversationLogType.SERVER_RESPONSE, chatResponse,
                    Duration.between(start, Instant.now()).toMillis(), stallingMsg.getIsSessionCompleted());
            logInfo("(BE - Robot) Send bot response - conversation_id: " + conversationId + ", response: "
                    + chatResponse);
        } catch (IOException e) {
            logError(e, "Failed to send bot response");
        }
    }

    @Override
    protected void handleSendStalling(StringBuilder logMessage, String userAnswer) {
        Instant startStalling = Instant.now();
        List<RobotResponseMessageDTO> stallingMsgs = aiRobotConversationService.getStallingMessage(session.getId(),
                userAnswer);
        RobotConversationMessageDTO stallingMsg = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_STALLING)
                .data(RobotChatResponseDTO.builder()
                        .messages(stallingMsgs)
                        .build())
                .socketSessionId(session.getId())
                .build();
        try {
            String chatResponse = objectMapper.writeValueAsString(stallingMsg);
            long duration = Duration.between(startStalling, Instant.now()).toMillis();
            // sharedService.saveConversationLog(stallingMsgs.get(0).getConversationId(),
            // ConversationLogType.SERVER_RESPONSE, chatResponse, duration);
            if (hasReturnedChatResponse.get()) {
                return;
            }

            session.sendMessage(new TextMessage(chatResponse)); // Send the bot response back to the user
            logMessage.append(", stalling: ").append(duration).append("ms");
            logInfo("(BE - Robot) Send stalling bot response: " + chatResponse);
        } catch (IOException e) {
            logError(e, "Failed to send bot response");
        }
    }
}
