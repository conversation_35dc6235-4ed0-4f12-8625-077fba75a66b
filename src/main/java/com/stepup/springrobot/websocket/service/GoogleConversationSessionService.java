package com.stepup.springrobot.websocket.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.gax.rpc.ClientStream;
import com.google.api.gax.rpc.ResponseObserver;
import com.google.api.gax.rpc.StreamController;
import com.google.cloud.speech.v1p1beta1.*;
import com.google.protobuf.BoolValue;
import com.google.protobuf.ByteString;
import com.stepup.springrobot.dto.chat.AIRobotConversationResDTO;
import com.stepup.springrobot.dto.chat.RobotASRMessageDTO;
import com.stepup.springrobot.dto.chat.RobotConversationMessageDTO;
import com.stepup.springrobot.dto.chat.RobotResponseMessageDTO;
import com.stepup.springrobot.model.chat.RobotConversationResponseType;
import com.stepup.springrobot.model.chat.STTHandlerType;
import com.stepup.springrobot.service.AIRobotConversationService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Log4j2
public class GoogleConversationSessionService implements AutoCloseable {
    private final WebSocketSession session;
    private SpeechClient speechClient;
    private ClientStream<StreamingRecognizeRequest> clientStream;
    private final List<byte[]> binaryDataBuffer = new ArrayList<>();
    private final ObjectMapper objectMapper;
    private boolean isPausingRecording = false;
    private ScheduledExecutorService scheduler;
    private ScheduledFuture<?> conversationTimeoutTask;
    private static final long CONVERSATION_TIMEOUT_MINUTES = 5;

    private final AIRobotConversationService aiRobotConversationService;
    private final String userId;
    private final String robotId;


    public GoogleConversationSessionService(WebSocketSession session, ObjectMapper objectMapper, AIRobotConversationService aiRobotConversationService,
                                            String userId, String robotId) {
        this.session = session;
        this.objectMapper = objectMapper;
        this.aiRobotConversationService = aiRobotConversationService;
        this.userId = userId;
        this.robotId = robotId;
    }

    public void initializeSpeechClient() throws Exception {
        speechClient = SpeechClient.create();
        log.info("SpeechClient created");

        initializeClientStream();
        log.info("ClientStream created");

        scheduler = Executors.newSingleThreadScheduledExecutor();

        // Start the conversation timer (limit to 5 minutes)
        startConversationTimeout();
    }

    // Start the 5-minute conversation timeout timer
    private void startConversationTimeout() {
        log.info("Conversation timer started. The conversation will end in " + CONVERSATION_TIMEOUT_MINUTES + " minutes.");

        conversationTimeoutTask = scheduler.schedule(() -> {
            log.info("Conversation timeout reached. Ending the conversation.");
            stopConversation();  // End the conversation when the timer expires
        }, CONVERSATION_TIMEOUT_MINUTES, TimeUnit.MINUTES);
    }

    private void stopConversation() {
        stopStreamingRecognition();  // Close the current stream if active

        // Close the WebSocket session
        try {
            RobotResponseMessageDTO responseMessageDTO = RobotResponseMessageDTO.builder()
                    .text("Kết thúc hội thoại sau 5 phút.")
                    .audio("https://smedia.stepup.edu.vn/audio/onion/user/240907/1725722763952_5969.wav")
                    .build();
            RobotConversationMessageDTO audioMessage = RobotConversationMessageDTO.builder()
                    .type(RobotConversationResponseType.CHAT_RESPONSE)
                    .data(List.of(responseMessageDTO))
                    .build();
            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(audioMessage)));
            session.close(CloseStatus.NORMAL.withReason("Conversation time limit reached."));
        } catch (IOException e) {
            log.error("Error closing WebSocket session", e);
        }

        // Cancel the conversation timeout task if it's still running
        if (conversationTimeoutTask != null && !conversationTimeoutTask.isDone()) {
            conversationTimeoutTask.cancel(true);
            log.info("Conversation timeout task cancelled.");
        }
    }

    private void initializeClientStream() {
        ResponseObserver<StreamingRecognizeResponse> responseObserver = new ResponseObserver<>() {
            ArrayList<StreamingRecognizeResponse> responses = new ArrayList<>();

            @Override
            public void onStart(StreamController controller) {
                // Handle stream start
            }

            @Override
            public void onResponse(StreamingRecognizeResponse response) {
                responses.add(response);
                try {
                    log.info("Received response: {}", response);
                    if (isPausingRecording) {
                        log.info("Đang dừng ghi âm, không xử lý response");
                        return;
                    }

                    StreamingRecognitionResult result = response.getResultsList().get(0);
                    SpeechRecognitionAlternative alternative = result.getAlternativesList().get(0);
                    String transcript = alternative.getTranscript();
                    log.info("===transcript: {}", transcript);

                    boolean isEndOfSpeech = Objects.equals(Boolean.TRUE, result.getIsFinal()) && !StringUtils.isEmpty(transcript);
                    RobotConversationMessageDTO audioMessage = RobotConversationMessageDTO.builder()
                            .type(RobotConversationResponseType.ASR)
                            .data(RobotASRMessageDTO.builder()
                                    .transcript(transcript)
                                    .isStop(isEndOfSpeech)
                                    .build())
                            .build();
                    // Send the transcript back to the WebSocket client
                    session.sendMessage(new TextMessage(objectMapper.writeValueAsString(audioMessage)));

                    if (isEndOfSpeech) {
                        isPausingRecording = true;
                        stopStreamingRecognition();  // Stop streaming after speech ends
                        Long botId = null;
                        if (session.getAttributes().get("bot_id") != null) {
                            botId = Long.parseLong(String.valueOf(session.getAttributes().get("bot_id")));
                        }

                        getBotResponse(transcript, alternative.toString(), botId);  // Handle bot logic based on the recognized transcript
                    }
                } catch (Exception e) {
                    log.info("Error processing google response: {}", e.getMessage(), e);
                    clientStream = null;
                }
            }

            @Override
            public void onComplete() {
                // Handle completion
            }

            @Override
            public void onError(Throwable t) {
                // Handle errors
                log.info("Error processing client stream: {}", t.getMessage(), t);
                clientStream = null;
            }
        };

        clientStream = speechClient.streamingRecognizeCallable().splitCall(responseObserver);

        RecognitionConfig recognitionConfig = RecognitionConfig.newBuilder()
                .setEncoding(RecognitionConfig.AudioEncoding.LINEAR16)
                .setLanguageCode("en-US")
                .setSampleRateHertz(16000)
                .setEnableAutomaticPunctuation(true)
                .setEnableSpokenPunctuation(BoolValue.of(true))
                .setModel("latest_long")
                .build();

        StreamingRecognitionConfig streamingRecognitionConfig = StreamingRecognitionConfig.newBuilder()
                .setConfig(recognitionConfig)
                .setInterimResults(true)  // Enable interim results for real-time feedback
                .setSingleUtterance(false) // Enable single utterance for long-running recognition
                .build();

        StreamingRecognizeRequest request = StreamingRecognizeRequest.newBuilder()
                .setStreamingConfig(streamingRecognitionConfig)
                .build();

        log.info("Sending initial config request");
        clientStream.send(request);
    }

    // Stop streaming when the user stops speaking
    private void stopStreamingRecognition() {
        if (clientStream != null) {
            clientStream.closeSend();  // Close the current ClientStream to stop sending audio
            clientStream = null;  // Mark it as closed
            log.info("ClientStream closed.");
        }
    }

    public void handleAudioData(ByteBuffer audioData, boolean isStop) {
        if (isStop || isPausingRecording) {
            return;
        }

        if (clientStream == null) {
            initializeClientStream();
            log.info("Khởi tạo lại ClientStream");
        }

//        // Convert audio to 16-bit PCM
//        byte[] convertedAudio = convertToLinear16(audioData.array());
//
//        // Store the received binary data
//        binaryDataBuffer.add(convertedAudio);

        // Store the received binary data
        binaryDataBuffer.add(audioData.array());

        ByteString tempByteString = ByteString.copyFrom(audioData);
        StreamingRecognizeRequest request = StreamingRecognizeRequest.newBuilder()
                .setAudioContent(tempByteString)
                .build();
        clientStream.send(request);
        log.info("Send audio content: {}", tempByteString.size());
    }

    private byte[] convertToLinear16(byte[] input) {
        if (input.length % 4 != 0) {
            throw new IllegalArgumentException("Input byte array length must be a multiple of 4");
        }

        ByteBuffer inputBuffer = ByteBuffer.wrap(input).order(ByteOrder.LITTLE_ENDIAN);
        ByteBuffer outputBuffer = ByteBuffer.allocate(input.length / 2).order(ByteOrder.LITTLE_ENDIAN);

        while (inputBuffer.remaining() >= 4) {
            float sample = inputBuffer.getFloat();
            short pcm16 = (short) Math.max(Math.min(sample * 32768.0f, 32767), -32768);
            outputBuffer.putShort(pcm16);
        }

        return outputBuffer.array();
    }

    public File saveAudio(String sessionId) {
        // Combine all the binary data into one byte array
        byte[] audioData = combineBinaryData(binaryDataBuffer);

        // Convert the byte array to an audio file
        File file = convertToWavFile(audioData, sessionId + "_" + System.currentTimeMillis() + ".wav");

        // Clear the buffer
        binaryDataBuffer.clear();

        return file;
    }

    private byte[] combineBinaryData(List<byte[]> binaryDataBuffer) {
        int totalLength = binaryDataBuffer.stream().mapToInt(data -> data.length).sum();
        byte[] combinedData = new byte[totalLength];
        int offset = 0;
        for (byte[] data : binaryDataBuffer) {
            System.arraycopy(data, 0, combinedData, offset, data.length);
            offset += data.length;
        }

        return combinedData;
    }

    private File convertToWavFile(byte[] audioData, String fileName) {
        try {
            InputStream byteArrayInputStream = new ByteArrayInputStream(audioData);
            AudioFormat audioFormat = getAudioFormat();
            AudioInputStream audioInputStream = new AudioInputStream(byteArrayInputStream, audioFormat, audioData.length / audioFormat.getFrameSize());
            File outputFile = new File(fileName);
            AudioSystem.write(audioInputStream, AudioFileFormat.Type.WAVE, outputFile);
            log.info("WAV file created: {}", outputFile.getAbsolutePath());
            return outputFile;
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }

    private AudioFormat getAudioFormat() {
        float sampleRate = 16000.0F; // 16kHz
        int sampleSizeInBits = 16;   // 16-bit
        int channels = 1;            // Mono
        boolean signed = true;
        boolean bigEndian = false;
        return new AudioFormat(sampleRate, sampleSizeInBits, channels, signed, bigEndian);
    }

    @Override
    public void close() {
        try {
            if (clientStream != null) {
                clientStream.closeSend();
            }
        } finally {
            if (speechClient != null) {
                speechClient.shutdown();
            }
        }
    }

    // Handle bot response after receiving speech-to-text transcript
    private void getBotResponse(String transcript, String speechToTextResponse, Long botId) {
        Instant start = Instant.now();
        File file = saveAudio(session.getId());
        StringBuilder logMessage = new StringBuilder();
        List<AIRobotConversationResDTO> resDTOS = aiRobotConversationService.getRobotResponse(userId, robotId, transcript, file, session.getId(), speechToTextResponse,
                session.getRemoteAddress().toString(), botId, logMessage, STTHandlerType.GOOGLE, false, null).getMessages();
        List<RobotResponseMessageDTO> responseMessageDTOS = resDTOS.stream()
                .map(resDTO -> RobotResponseMessageDTO.builder()
                        .text(resDTO.getText())
                        .audio(resDTO.getMp3())
                        .emotion(resDTO.getEmotion())
                        .servo(resDTO.getServo())
                        .animations(resDTO.getAnimations())
                        .build())
                .collect(Collectors.toList());

        RobotConversationMessageDTO audioMessage = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_RESPONSE)
                .data(responseMessageDTOS)
                .build();

        if (session.isOpen()) {
            try {
                session.sendMessage(new TextMessage(objectMapper.writeValueAsString(audioMessage)));  // Send the bot response back to the user
                log.info("Send bot response: {}", resDTOS);
                String responseTime = "ROBOT - res time: " + Duration.between(start, Instant.now()).toMillis() + "ms";
                logMessage.insert(0, responseTime);
                log.error(logMessage.toString());
                isPausingRecording = false;

            } catch (IOException e) {
                log.error("Failed to send bot response", e);
            }
        }
    }
}
