package com.stepup.springrobot.websocket.service.grpc;

import com.stepup.springrobot.dto.grpc.TranscriptionResponseDTO;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.Metadata;
import io.grpc.stub.MetadataUtils;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import streaming_voice.StreamVoiceGrpc;
import streaming_voice.StreamingVoice;

@Slf4j
public class NewGrpcAsrClientService {

    private final String grpcUri;
    private ManagedChannel channel;
    private StreamVoiceGrpc.StreamVoiceStub asyncStub;

    // Metadata for gRPC - these should be configurable
    private static final Metadata.Key<String> TOKEN_KEY = Metadata.Key.of("token", Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> SPEECH_TIMEOUT_KEY = Metadata.Key.of("speech_timeout",
            Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> SPEECH_MAX_KEY = Metadata.Key.of("speech_max",
            Metadata.ASCII_STRING_MARSHALLER);
    private static final Metadata.Key<String> SILENCE_TIMEOUT_KEY = Metadata.Key.of("silence_timeout",
            Metadata.ASCII_STRING_MARSHALLER);

    // Example values - make these externalized/configurable
    private static final String TOKEN_VALUE = "su_asr";
    private static final String SPEECH_TIMEOUT_VALUE = "2";
    private static final String SPEECH_MAX_VALUE = "60";
    private static final String SILENCE_TIMEOUT_VALUE = "5";

    public NewGrpcAsrClientService(String grpcUri) {
        this.grpcUri = grpcUri;
        try {
            log.info("gRPC ASR Client: Initializing channel to ASR service at: {}", this.grpcUri);
            this.channel = ManagedChannelBuilder.forTarget(this.grpcUri)
                    .usePlaintext() // IMPORTANT: For production, use TLS/SSL
                    .build();

            Metadata headers = new Metadata();
            headers.put(TOKEN_KEY, TOKEN_VALUE);
            headers.put(SPEECH_TIMEOUT_KEY, SPEECH_TIMEOUT_VALUE);
            headers.put(SPEECH_MAX_KEY, SPEECH_MAX_VALUE);
            headers.put(SILENCE_TIMEOUT_KEY, SILENCE_TIMEOUT_VALUE);

            this.asyncStub = StreamVoiceGrpc.newStub(channel)
                    .withInterceptors(MetadataUtils.newAttachHeadersInterceptor(headers));

            log.info("gRPC ASR Client: Channel and stub initialized successfully.");
        } catch (Exception e) {
            log.error("gRPC ASR Client: Failed to initialize channel: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to initialize gRPC client for ASR service", e);
        }
    }

    public Flux<TranscriptionResponseDTO> streamAudio(Flux<byte[]> audioFlux) {
        Sinks.Many<TranscriptionResponseDTO> sink = Sinks.many().multicast().onBackpressureBuffer();

        StreamObserver<StreamingVoice.VoiceRequest> requestObserver = asyncStub
                .sendVoice(new StreamObserver<StreamingVoice.TextReply>() {
                    @Override
                    public void onNext(StreamingVoice.TextReply reply) {
                        if (reply.hasResult() && !reply.getResult().getHypothesesList().isEmpty()) {
                            String transcript = reply.getResult().getHypotheses(0).getTranscriptNormed();
                            boolean isFinal = reply.getResult().getFinal();
                            // log.debug("gRPC ASR Client: Response - Final: {}, Transcript: {}", isFinal,
                            // transcript);
                            sink.tryEmitNext(new TranscriptionResponseDTO(transcript, isFinal));
                        } else {
                            log.debug("gRPC ASR Client: Received TextReply without valid transcription result.");
                        }
                    }

                    @Override
                    public void onError(Throwable t) {
                        log.error("gRPC ASR Client: Stream error from ASR service: {}", t.getMessage(), t);
                        sink.tryEmitError(t);
                    }

                    @Override
                    public void onCompleted() {
                        log.info("gRPC ASR Client: Stream completed by ASR service.");
                        sink.tryEmitComplete();
                    }
                });

        audioFlux
                .map(chunk -> StreamingVoice.VoiceRequest.newBuilder()
                        .setByteBuff(com.google.protobuf.ByteString.copyFrom(chunk))
                        .build())
                .doOnNext(requestObserver::onNext) // Simplified sending to gRPC
                .doOnError(error -> {
                    log.error("gRPC ASR Client: Error in audioFlux before sending to gRPC: {}", error.getMessage(),
                            error);
                    requestObserver.onError(error);
                })
                .doOnComplete(() -> {
                    log.info("gRPC ASR Client: Audio flux completed, signaling gRPC request stream completion.");
                    requestObserver.onCompleted();
                })
                .doOnCancel(() -> {
                    log.info("gRPC ASR Client: Audio flux cancelled, signaling gRPC request stream completion.");
                    if (channel != null && !channel.isShutdown()) {
                        requestObserver.onCompleted();
                    }
                })
                .subscribe();

        return sink.asFlux();
    }

    public void close() {
        log.info("gRPC ASR Client: Shutting down channel.");
        if (channel != null && !channel.isShutdown()) {
            try {
                channel.shutdown();
            } catch (Exception e) {
                log.error("gRPC ASR Client: Interrupted during channel shutdown: {}", e.getMessage(), e);
            } finally {
                if (!channel.isShutdown()) {
                    channel.shutdownNow();
                }
            }
        }
        log.info("gRPC ASR Client: Channel shut down.");
    }
}