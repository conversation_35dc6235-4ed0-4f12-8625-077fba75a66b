// Bot List Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeBotList();
});

function initializeBotList() {
    setupSearchFunctionality();
    setupBotCardInteractions();
    setupStatsAnimations();
    setupAccessibilityFeatures();
}

function setupSearchFunctionality() {
    const searchInput = document.querySelector('input[name="search"]');
    const searchForm = document.querySelector('form[action="/bot-view/list"]');
    
    if (searchInput && searchForm) {
        let searchTimeout;
        
        // Auto-submit form when typing stops
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchForm.submit();
            }, 500);
        });
        
        // Highlight search term in bot names
        const searchTerm = new URLSearchParams(window.location.search).get('search');
        if (searchTerm) {
            highlightSearchTerm(searchTerm);
        }
        
        // Clear search functionality
        const clearSearchBtn = document.querySelector('.btn-outline-primary');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', function(e) {
                e.preventDefault();
                window.location.href = '/bot-view/list';
            });
        }
    }
}

function highlightSearchTerm(searchTerm) {
    const botNames = document.querySelectorAll('.bot-name');
    botNames.forEach(name => {
        const text = name.textContent;
        const highlightedText = text.replace(
            new RegExp(searchTerm, 'gi'),
            match => `<mark class="bg-warning">${match}</mark>`
        );
        name.innerHTML = highlightedText;
    });
}

function setupBotCardInteractions() {
    const botCards = document.querySelectorAll('.bot-card');
    
    botCards.forEach(card => {
        // Add loading animation class for bots that are loading
        const statusElement = card.querySelector('.status-loading');
        if (statusElement) {
            card.classList.add('loading');
        }
        
        // Add click interaction
        card.addEventListener('click', function() {
            // Add a subtle click effect
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
        
        // Add keyboard navigation
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
        
        // Make cards focusable
        card.setAttribute('tabindex', '0');
    });
}

function setupStatsAnimations() {
    const statsNumbers = document.querySelectorAll('.stats-card h4');
    
    // Animate numbers on page load
    statsNumbers.forEach(number => {
        const finalValue = parseInt(number.textContent);
        animateNumber(number, 0, finalValue, 1000);
    });
}

function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current;
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

function setupAccessibilityFeatures() {
    // Add ARIA labels and roles
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        searchInput.setAttribute('aria-label', 'Tìm kiếm bot theo tên');
        searchInput.setAttribute('role', 'searchbox');
    }
    
    const botCards = document.querySelectorAll('.bot-card');
    botCards.forEach((card, index) => {
        const botName = card.querySelector('.bot-name')?.textContent || `Bot ${index + 1}`;
        card.setAttribute('aria-label', `Bot: ${botName}`);
        card.setAttribute('role', 'button');
    });
    
    // Add skip link for keyboard navigation
    addSkipLink();
}

function addSkipLink() {
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Bỏ qua đến nội dung chính';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
        position: absolute;
        top: -40px;
        left: 6px;
        background: #000;
        color: white;
        padding: 8px;
        text-decoration: none;
        z-index: 1000;
        border-radius: 4px;
    `;
    
    skipLink.addEventListener('focus', function() {
        this.style.top = '6px';
    });
    
    skipLink.addEventListener('blur', function() {
        this.style.top = '-40px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
    
    // Add main content id
    const mainContent = document.querySelector('.container-fluid');
    if (mainContent) {
        mainContent.id = 'main-content';
    }
}

// Export functions for potential external use
window.BotListUtils = {
    highlightSearchTerm,
    animateNumber,
    setupBotCardInteractions
};


