document.addEventListener("DOMContentLoaded", () => {
  const recordButton = document.getElementById("recordButton");
  const stopButton = document.getElementById("stopButton");
  const transcriptionDiv = document.getElementById("transcription");
  const statusDiv = document.getElementById("status");

  let mediaRecorder;
  let socket;
  let audioContext;
  let processor;
  let input;
  let globalStream;

  const TARGET_SAMPLE_RATE = 16000; // Should match backend/gRPC config
  const BUFFER_SIZE = 4096; // Adjust for desired latency vs. network overhead

  // audioProcessorUrl is now globally defined in the HTML

  recordButton.onclick = async () => {
    statusDiv.textContent = "Connecting...";
    try {
      globalStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: false,
      });

      audioContext = new (window.AudioContext || window.webkitAudioContext)();
      // audioProcessorUrl is defined in the HTML by Spring MVC
      await audioContext.audioWorklet.addModule(audioProcessorUrl);
      input = audioContext.createMediaStreamSource(globalStream);
      processor = new AudioWorkletNode(audioContext, "audio-processor", {
        processorOptions: {
          targetSampleRate: TARGET_SAMPLE_RATE,
          bufferSize: BUFFER_SIZE,
        },
      });

      input.connect(processor);
      // processor.connect(audioContext.destination); // Optional: play back mic audio

      // Correct WebSocket URL for Spring Boot
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
      socket = new WebSocket(
        `${protocol}//${window.location.host}/ws/stream-audio`
      );

      socket.onopen = () => {
        console.log("WebSocket connection established.");
        statusDiv.textContent = "Recording...";
        recordButton.disabled = true;
        stopButton.disabled = false;
        transcriptionDiv.innerHTML = "<p><em>Listening...</em></p>";
      };

      processor.port.onmessage = (event) => {
        if (socket && socket.readyState === WebSocket.OPEN) {
          console.log("Sending audio data to server:", event.data);
          socket.send(event.data); // Send Int16Array ArrayBuffer
        }
      };

      socket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.transcript) {
          let existingP = transcriptionDiv.querySelector(
            `p[data-final="false"]`
          );
          if (data.final) {
            if (existingP) {
              existingP.textContent = data.transcript;
              existingP.setAttribute("data-final", "true");
              existingP.style.color = "black"; // Reset color for final
            } else {
              const p = document.createElement("p");
              p.textContent = data.transcript;
              p.setAttribute("data-final", "true");
              transcriptionDiv.appendChild(p);
            }
          } else {
            // Update an interim results display
            if (!existingP) {
              existingP = document.createElement("p");
              existingP.setAttribute("data-final", "false");
              existingP.style.color = "gray";
              transcriptionDiv.appendChild(existingP);
            }
            existingP.textContent = data.transcript + "...";
          }
        }
      };

      socket.onclose = (event) => {
        console.log("WebSocket connection closed:", event);
        statusDiv.textContent = "Disconnected.";
        if (event.wasClean === false) {
          statusDiv.textContent = `Disconnected (code: ${event.code}, reason: ${
            event.reason || "N/A"
          }). Try refreshing.`;
        }
        cleanup();
      };

      socket.onerror = (error) => {
        console.error("WebSocket error:", error);
        statusDiv.textContent =
          "Error connecting. Please check console and refresh.";
        cleanup();
      };
    } catch (err) {
      console.error("Error starting recording:", err);
      statusDiv.textContent = `Error: ${err.message}`;
      if (err.name === "NotAllowedError" || err.name === "SecurityError") {
        statusDiv.textContent =
          "Microphone access denied. Please allow microphone access and refresh.";
      }
      cleanup();
    }
  };

  stopButton.onclick = () => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send("EOS"); // Signal end of stream
    }
    cleanup();
    statusDiv.textContent = "Recording stopped.";
  };

  function cleanup() {
    if (processor) {
      processor.port.postMessage("stop"); // Tell worklet to stop
      processor.disconnect();
      processor = null;
    }
    if (input) {
      input.disconnect();
      input = null;
    }
    if (audioContext && audioContext.state !== "closed") {
      audioContext.close();
      audioContext = null;
    }
    if (globalStream) {
      globalStream.getTracks().forEach((track) => track.stop());
      globalStream = null;
    }
    if (
      socket &&
      (socket.readyState === WebSocket.OPEN ||
        socket.readyState === WebSocket.CONNECTING)
    ) {
      socket.close();
    }
    socket = null;
    recordButton.disabled = false;
    stopButton.disabled = true;
  }
});
