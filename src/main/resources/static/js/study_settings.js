document.addEventListener("DOMContentLoaded", function () {
    // Get DOM elements
    const studySettingsForm = document.getElementById("studySettingsForm");
    const botIdInput = document.getElementById("botId");
    const silenceThresholdInput = document.getElementById("silenceThreshold");
    const settingResult = document.getElementById("settingResult");
    const resetFormButton = document.getElementById("resetForm");
    const lastBotIdSpan = document.getElementById("lastBotId");
    const lastSilenceThresholdSpan = document.getElementById("lastSilenceThreshold");
    const lastUpdateTimeSpan = document.getElementById("lastUpdateTime");

    // Load current configuration on page load
    loadCurrentConfiguration();

    // Handle form submission
    studySettingsForm.addEventListener("submit", function (event) {
        event.preventDefault();

        // Get form values
        const botId = botIdInput.value.trim();
        const silenceThreshold = parseInt(silenceThresholdInput.value);

        // Validate inputs
        if (!botId) {
            showResult("Please enter a Bot ID", "danger");
            botIdInput.focus();
            return;
        }

        if (isNaN(silenceThreshold) || silenceThreshold < 0 || silenceThreshold > 10000) {
            showResult("Please enter a valid silence threshold (0-10000 milliseconds)", "danger");
            silenceThresholdInput.focus();
            return;
        }

        // Prepare request data
        const requestData = {
            bot_id: botId,
            silence_threshold: silenceThreshold,
        };

        // Show loading state
        showResult("Updating silence threshold...", "info");
        const submitButton = event.target.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Processing...';

        // Send API request
        fetch("/robot/api/v1/admin/update_bot_silence_threshold", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(requestData),
        })
            .then((response) => {
                if (!response.ok) {
                    return response.json().then((data) => {
                        throw new Error(data.message || "Network response was not ok");
                    });
                }
                return response.json();
            })
            .then((data) => {
                showResult(
                    data.message || "Silence threshold updated successfully!",
                    "success"
                );
                console.log("API Response:", data);

                // Update current configuration display
                updateCurrentConfiguration(botId, silenceThreshold);

                // Clear form
                resetForm();
            })
            .catch((error) => {
                showResult("Error: " + error.message, "danger");
                console.error("Error:", error);
            })
            .finally(() => {
                // Restore button state
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
            });
    });

    // Handle reset button
    resetFormButton.addEventListener("click", function () {
        resetForm();
        showResult("Form has been reset", "info");
    });

    // Helper function to reset form
    function resetForm() {
        botIdInput.value = "";
        silenceThresholdInput.value = "";
        botIdInput.focus();
    }

    // Helper function to show result message
    function showResult(message, type) {
        settingResult.textContent = message;
        settingResult.className = "alert alert-" + type;
        settingResult.style.display = "block";

        // Auto-hide after 5 seconds for non-error messages
        if (type !== "danger") {
            setTimeout(() => {
                settingResult.style.display = "none";
            }, 5000);
        }
    }

    // Function to update current configuration display
    function updateCurrentConfiguration(botId, silenceThreshold) {
        lastBotIdSpan.textContent = botId;
        lastBotIdSpan.classList.remove("text-muted");
        lastBotIdSpan.classList.add("text-success", "fw-bold");

        lastSilenceThresholdSpan.textContent = silenceThreshold + " ms";
        lastSilenceThresholdSpan.classList.remove("text-muted");
        lastSilenceThresholdSpan.classList.add("text-success", "fw-bold");

        lastUpdateTimeSpan.textContent = new Date().toLocaleString();
        lastUpdateTimeSpan.classList.remove("text-muted");
        lastUpdateTimeSpan.classList.add("text-success", "fw-bold");
    }

    // Function to load current configuration (placeholder for future implementation)
    function loadCurrentConfiguration() {
        // This would typically fetch current configuration from the server
        console.log("Loading current configuration...");

        // Future implementation could include:
        // fetch("/robot/api/v1/admin/get_bot_silence_threshold")
        //   .then(response => response.json())
        //   .then(data => {
        //     if (data.botId) updateCurrentConfiguration(data.botId, data.silenceThreshold);
        //   })
        //   .catch(error => console.error("Error loading configuration:", error));
    }

    // Add input validation and formatting
    botIdInput.addEventListener("input", function () {
        // Remove any non-alphanumeric characters except hyphens and underscores
        this.value = this.value.replace(/[^a-zA-Z0-9\-_]/g, "");
    });

    silenceThresholdInput.addEventListener("input", function () {
        // Ensure only numbers and within range
        const value = parseInt(this.value);
        if (isNaN(value) || value < 0) {
            this.value = 0;
        } else if (value > 10000) {
            this.value = 10000;
        }
    });

    // Add keyboard shortcuts
    document.addEventListener("keydown", function (event) {
        // Ctrl+Enter or Cmd+Enter to submit form
        if ((event.ctrlKey || event.metaKey) && event.key === "Enter") {
            event.preventDefault();
            studySettingsForm.dispatchEvent(new Event("submit"));
        }

        // Ctrl+R or Cmd+R to reset form (prevent page refresh)
        if ((event.ctrlKey || event.metaKey) && event.key === "r") {
            event.preventDefault();
            resetForm();
            showResult("Form reset with keyboard shortcut", "info");
        }
    });
});
