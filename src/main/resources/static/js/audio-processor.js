class AudioProcessor extends AudioWorkletProcessor {
  constructor(options) {
    super();
    this.targetSampleRate = options.processorOptions.targetSampleRate || 16000;
    this.bufferSize = options.processorOptions.bufferSize || 4096;
    this.inputBuffer = []; // Stores Float32Array chunks from the input
    this.bytesSent = 0;
    this.isActive = true;

    this.port.onmessage = (event) => {
      if (event.data === "stop") {
        this.isActive = false;
      }
    };
  }

  process(inputs, outputs, parameters) {
    if (!this.isActive) {
      return false; // Stop processing
    }

    const input = inputs[0]; // Assuming mono input
    if (input && input.length > 0 && input[0].length > 0) {
      const channelData = input[0]; // Float32Array from -1.0 to 1.0
      this.inputBuffer.push(new Float32Array(channelData)); // Store a copy

      let totalSamples = 0;
      this.inputBuffer.forEach((buf) => (totalSamples += buf.length));

      // Resample and convert to Int16 when enough data is buffered or if stopping
      // This simple resampling just picks samples, a proper one would interpolate
      const ratio = sampleRate / this.targetSampleRate;

      // We need enough samples to form at least one output buffer after resampling
      // Or, if we are stopping, process whatever is left.
      if (totalSamples * (1 / ratio) >= this.bufferSize || !this.isActive) {
        const combinedBuffer = this.concatenateFloat32Arrays(this.inputBuffer);
        this.inputBuffer = []; // Clear the buffer

        const resampledBufferLength = Math.floor(combinedBuffer.length / ratio);
        const pcm16Buffer = new Int16Array(resampledBufferLength);
        let outputIndex = 0;

        for (let i = 0; i < resampledBufferLength; i++) {
          const originalIndex = Math.floor(i * ratio);
          if (originalIndex < combinedBuffer.length) {
            let s = Math.max(-1, Math.min(1, combinedBuffer[originalIndex]));
            pcm16Buffer[outputIndex++] = s * 0x7fff; // Convert to 16-bit PCM
          }
        }

        // Send the ArrayBuffer directly
        if (outputIndex > 0) {
          this.port.postMessage(pcm16Buffer.buffer.slice(0, outputIndex * 2)); // Ensure correct slice size for ArrayBuffer
          this.bytesSent += outputIndex * 2;
        }
      }
    }
    return this.isActive; // Keep processor alive if active
  }

  concatenateFloat32Arrays(arrays) {
    let totalLength = 0;
    arrays.forEach((arr) => (totalLength += arr.length));
    const result = new Float32Array(totalLength);
    let offset = 0;
    arrays.forEach((arr) => {
      result.set(arr, offset);
      offset += arr.length;
    });
    return result;
  }
}

registerProcessor("audio-processor", AudioProcessor);
