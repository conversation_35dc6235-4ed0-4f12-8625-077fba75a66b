/* Custom styles for Bot List Page */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Enhanced bot card animations */
.bot-card {
    position: relative;
    overflow: hidden;
}

.bot-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.bot-card:hover::before {
    left: 100%;
}

/* Enhanced search input */
.search-input {
    transition: all 0.3s ease;
}

.search-input:focus {
    transform: scale(1.02);
}

/* Pulse animation for loading bots */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.bot-card.loading {
    animation: pulse 2s infinite;
}

/* Status badges with better styling */
.bot-status {
    position: relative;
    overflow: hidden;
}

.bot-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.bot-status:hover::before {
    left: 100%;
}

/* Enhanced stats cards */
.stats-card {
    position: relative;
    overflow: hidden;
}

.stats-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transition: all 0.3s ease;
}

.stats-card:hover::after {
    transform: scale(1.5);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .search-container {
        padding: 1rem;
    }
    
    .bot-card {
        margin-bottom: 1rem;
    }
    
    .stats-card .row > div {
        margin-bottom: 1rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .bot-card {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .bot-name {
        color: #e2e8f0;
    }
    
    .bot-description {
        color: #a0aec0;
    }
    
    .stats-card {
        background-color: #2d3748;
        color: #e2e8f0;
    }
}

/* Loading skeleton animation */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Enhanced focus states for accessibility */
.bot-card:focus-within {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.search-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Print styles */
@media print {
    .search-container,
    .btn,
    .no-results .btn {
        display: none !important;
    }
    
    .bot-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
}


