/* Study Settings Styles */
.study-settings-card {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    margin-bottom: 20px;
    border: none;
}

.study-settings-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
}

.study-settings-card .card-title {
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 1.25rem;
}

.study-settings-card .card-subtitle {
    opacity: 0.9;
    font-size: 0.9rem;
}

.study-settings-card .card-body {
    padding: 25px;
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 5px;
}

/* Button Styles */
.btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    transform: translateY(-1px);
}

/* Alert Styles */
#settingResult {
    padding: 15px 20px;
    border-radius: 8px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* Configuration Status Card */
.bg-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

.config-item {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.config-item:last-child {
    border-bottom: none;
}

.config-item strong {
    color: #495057;
    font-weight: 600;
}

/* Help Section */
.border-info {
    border: 2px solid #17a2b8 !important;
    border-radius: 8px;
}

.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

.card-body ul {
    margin-bottom: 0;
    padding-left: 20px;
}

.card-body ul li {
    margin-bottom: 4px;
    color: #6c757d;
}

/* Icons */
.bi {
    font-size: 1rem;
}

.card-header .bi {
    font-size: 1.1rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.study-settings-card {
    animation: fadeIn 0.5s ease-out;
}

/* Status indicators */
.text-success.fw-bold {
    color: #28a745 !important;
    font-weight: 600 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .study-settings-card {
        margin-bottom: 15px;
        border-radius: 8px;
    }

    .study-settings-card .card-header {
        padding: 15px 20px;
        border-radius: 8px 8px 0 0;
    }

    .study-settings-card .card-body {
        padding: 20px;
    }

    .study-settings-card .card-title {
        font-size: 1.1rem;
    }

    .btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }

    .d-md-flex .btn {
        margin-bottom: 10px;
    }
}

@media (max-width: 576px) {
    .study-settings-card .card-body {
        padding: 15px;
    }

    .study-settings-card .card-header {
        padding: 12px 15px;
    }

    .form-control {
        padding: 10px 12px;
        font-size: 0.9rem;
    }
}

/* Focus states for accessibility */
.btn:focus,
.form-control:focus {
    outline: none;
}

.btn:focus-visible,
.form-control:focus-visible {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Loading state */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
}

/* Hover effects for cards */
.card:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
}

.border-info:hover {
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.2);
}
