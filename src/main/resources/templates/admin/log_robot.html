<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <title>Robot Logs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"/>
    <style>
        body {
            padding: 16px;
        }

        .table-responsive {
            max-height: 70vh;
            overflow: auto;
        }

        .log-detail {
            white-space: pre-wrap;
            font-family: monospace;
        }

        .controls {
            gap: 8px;
        }

        .tab-pane {
            padding-top: 16px;
        }
    </style>
    <script>
        const API_ERROR = '/robot/logs';
        const API_WAKE = '/robot/logs/wake-words';

        async function loadErrorLogs(page = 0) {
            const robotId = document.getElementById('robotId').value.trim();
            const size = document.getElementById('pageSize').value;
            const start = document.getElementById('startTime').value;
            const end = document.getElementById('endTime').value;
            const url = new URL(window.location.origin + API_ERROR);
            url.searchParams.set('page', page);
            url.searchParams.set('size', size);
            if (robotId) url.searchParams.set('robotId', robotId);
            if (start) url.searchParams.set('start', new Date(start).getTime());
            if (end) url.searchParams.set('end', new Date(end).getTime());

            const res = await fetch(url);
            const response = await res.json();
            // API returns DataResponseDTO when going through RobotLogsController
            const pageData = response && response.data && response.status === 200 ? response.data : response;
            renderErrorLogs(pageData);
        }

        function renderErrorLogs(pageData) {
            const tbody = document.getElementById('errorLogsTbody');
            tbody.innerHTML = '';
            if (!pageData || !pageData.content) return;

            pageData.content.forEach((item) => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${item.createdAt ? new Date(item.createdAt).toLocaleString() : ''}</td>
                    <td>${item.robotId || ''}</td>
                    <td>${item.firmwareVersion || ''}</td>
                    <td>${item.infoType || ''}</td>
                    <td>${item.screen || ''}</td>
                    <td class="log-detail">${item.msgDetail || ''}</td>
                `;
                tbody.appendChild(tr);
            });

            // pagination
            document.getElementById('pageInfo').textContent = `Page ${pageData.number + 1} / ${pageData.totalPages}`;
            document.getElementById('prevBtn').disabled = pageData.first;
            document.getElementById('nextBtn').disabled = pageData.last;
            document.getElementById('prevBtn').onclick = () => loadErrorLogs(pageData.number - 1);
            document.getElementById('nextBtn').onclick = () => loadErrorLogs(pageData.number + 1);
        }

        async function loadWakeWords(page = 0) {
            const robotId = document.getElementById('robotIdWake').value.trim();
            const size = document.getElementById('pageSizeWake').value;
            const url = new URL(window.location.origin + API_WAKE);
            url.searchParams.set('page', page);
            url.searchParams.set('size', size);
            if (robotId) url.searchParams.set('robotId', robotId);

            const res = await fetch(url);
            const data = await res.json();
            renderWakeWords(data.data || []);
        }

        function renderWakeWords(items) {
            const tbody = document.getElementById('wakeWordsTbody');
            tbody.innerHTML = '';
            if (!Array.isArray(items) || items.length === 0) {
                const tr = document.createElement('tr');
                tr.innerHTML = `<td colspan="6" class="text-center text-muted">Không có dữ liệu wakeword</td>`;
                tbody.appendChild(tr);
                return;
            }
            items.forEach((item) => {
                const tr = document.createElement('tr');
                const detectedText = typeof item.detected === 'boolean' ? (item.detected ? 'true' : 'false') : (item.detected ?? '');
                const maxScoreText = typeof item.maxScore === 'number' ? item.maxScore.toFixed(6) : (item.maxScore ?? '');
                const thresholdText = typeof item.threshold === 'number' ? item.threshold.toFixed(6) : (item.threshold ?? '');
                tr.innerHTML = `
                    <td>${item.createdAt ? new Date(item.createdAt).toLocaleString() : ''}</td>
                    <td>${item.id || ''}</td>
                    <td>${detectedText}</td>
                    <td>${item.audioUrl ? `<audio controls class="w-100"><source src="${item.audioUrl}" type="audio/mpeg"></audio>` : ''}</td>
                    <td>${maxScoreText}</td>
                    <td>${thresholdText}</td>
                `;
                tbody.appendChild(tr);
            });
        }

        window.addEventListener('DOMContentLoaded', () => {
            loadErrorLogs(0);

            const errorTabButton = document.querySelector('#logs-tab');
            const wakeTabButton = document.querySelector('#wake-tab');
            if (errorTabButton) {
                errorTabButton.addEventListener('click', (e) => {
                    // load first page when switching to Logs tab
                    loadErrorLogs(0);
                });
            }
            if (wakeTabButton) {
                wakeTabButton.addEventListener('click', (e) => {
                    loadWakeWords(0);
                });
            }
            // Ensure only one audio plays at a time
            document.addEventListener(
                'play',
                function (e) {
                    const audios = document.getElementsByTagName('audio');
                    for (let audio of audios) {
                        if (audio !== e.target) {
                            audio.pause();
                            audio.currentTime = 0;
                        }
                    }
                },
                true
            );
        });
    </script>
</head>
<body>
<div class="container-fluid">
    <h3 class="mb-3">Robot Logs</h3>

    <ul class="nav nav-tabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button id="logs-tab" class="nav-link active" data-bs-toggle="tab" data-bs-target="#logs-pane" type="button"
                    role="tab">Logs
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button id="wake-tab" class="nav-link" data-bs-toggle="tab" data-bs-target="#wake-pane" type="button"
                    role="tab">Wakeword Audio
            </button>
        </li>
    </ul>

    <div class="tab-content">
        <div id="logs-pane" class="tab-pane fade show active" role="tabpanel">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Error Logs</span>
                    <div class="d-flex controls">
                        <input id="robotId" class="form-control form-control-sm" placeholder="Filter by robotId"
                               style="width: 220px"/>
                        <select id="pageSize" class="form-select form-select-sm" style="width: 100px">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                        </select>
                        <input id="startTime" type="datetime-local" class="form-control form-control-sm"
                               style="width: 210px"/>
                        <input id="endTime" type="datetime-local" class="form-control form-control-sm"
                               style="width: 210px"/>
                        <button class="btn btn-sm btn-primary" onclick="loadErrorLogs(0)">Load</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered align-middle">
                            <thead class="table-light">
                            <tr>
                                <th>Time</th>
                                <th>Robot ID</th>
                                <th>Version</th>
                                <th>Type</th>
                                <th>Screen</th>
                                <th>Detail</th>
                            </tr>
                            </thead>
                            <tbody id="errorLogsTbody"></tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div id="pageInfo" class="small text-muted"></div>
                        <div class="btn-group">
                            <button id="prevBtn" class="btn btn-outline-secondary btn-sm">Prev</button>
                            <button id="nextBtn" class="btn btn-outline-secondary btn-sm">Next</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="wake-pane" class="tab-pane fade" role="tabpanel">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Wake Word Audio (by Robot ID)</span>
                    <div class="d-flex controls">
                        <input id="robotIdWake" class="form-control form-control-sm" placeholder="Robot ID"
                               style="width: 220px"/>
                        <select id="pageSizeWake" class="form-select form-select-sm" style="width: 100px">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                        </select>
                        <button class="btn btn-sm btn-primary" onclick="loadWakeWords(0)">Load</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered align-middle">
                            <thead class="table-light">
                            <tr>
                                <th>Time</th>
                                <th>ID</th>
                                <th>Detected</th>
                                <th>Audio</th>
                                <th>Max score</th>
                                <th>Threshold</th>
                            </tr>
                            </thead>
                            <tbody id="wakeWordsTbody"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-3 small text-muted">Ping and version fields can be added when data structure is available.</div>
</div>
</body>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</html>


