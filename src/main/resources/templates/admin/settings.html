<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Robot Settings</title>
    <link
            href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
            rel="stylesheet"
    />
    <link
            rel="stylesheet"
            href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css"
    />
    <link rel="stylesheet" th:href="@{/css/admin.css}"/>
    <link rel="stylesheet" th:href="@{/css/settings.css}"/>
</head>
<body>
<div class="admin-wrapper">
    <!-- Sidebar -->
    <div th:replace="admin/fragments/sidebar :: sidebar('settings')"></div>

    <!-- Main Content -->
    <div class="main-content">
        <nav class="top-navbar">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center">
                    <button id="sidebar-toggle" class="btn">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="admin-profile">
                        <div class="dropdown">
                            <button
                                    class="btn dropdown-toggle"
                                    type="button"
                                    id="profileDropdown"
                                    data-bs-toggle="dropdown"
                            >
                                <i class="bi bi-person-circle"></i>
                                <span>Admin</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="#"
                                    ><i class="bi bi-person"></i> Profile</a
                                    >
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#"
                                    ><i class="bi bi-box-arrow-right"></i> Logout</a
                                    >
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card settings-card">
                            <div class="card-header">
                                <h5 class="card-title">Robot Settings</h5>
                            </div>
                            <div class="card-body">
                                <form id="robotSettingsForm">
                                    <div class="mb-3">
                                        <label for="robotId" class="form-label">Robot ID</label>
                                        <input
                                                type="text"
                                                class="form-control"
                                                id="robotId"
                                                required
                                        />
                                    </div>

                                    <div class="mb-3">
                                        <label for="settingType" class="form-label"
                                        >Setting Type</label
                                        >
                                        <select class="form-select" id="settingType" required>
                                            <option value="" selected disabled>
                                                Select setting type
                                            </option>
                                            <option value="VOLUME">Volume</option>
                                            <option value="SCREEN_BRIGHTNESS">
                                                Screen Brightness
                                            </option>
                                            <option value="MAP_TO_PHONE">
                                                Map Robot to Phone
                                            </option>
                                            <option value="MAP_ROBOT_TO_BOT_ID">
                                                Map Robot To Bot Id
                                            </option>
                                        </select>
                                    </div>

                                    <div
                                            class="mb-3"
                                            id="volumeSettingSection"
                                            style="display: none"
                                    >
                                        <label for="volumeValue" class="form-label"
                                        >Volume (1-100)</label
                                        >
                                        <input
                                                type="number"
                                                class="form-control"
                                                id="volumeValue"
                                                min="1"
                                                max="21"
                                        />
                                    </div>

                                    <div
                                            class="mb-3"
                                            id="brightnessSettingSection"
                                            style="display: none"
                                    >
                                        <label for="brightnessValue" class="form-label"
                                        >Screen Brightness (1-100)</label
                                        >
                                        <input
                                                type="number"
                                                class="form-control"
                                                id="brightnessValue"
                                                min="1"
                                                max="100"
                                        />
                                    </div>

                                    <div
                                            class="mb-3"
                                            id="phoneSettingSection"
                                            style="display: none"
                                    >
                                        <label for="phoneValue" class="form-label"
                                        >Phone Number</label
                                        >
                                        <input
                                                type="text"
                                                class="form-control"
                                                id="phoneValue"
                                                placeholder="Enter phone number"
                                        />
                                    </div>

                                    <div
                                            class="mb-3"
                                            id="botIdSettingSection"
                                            style="display: none"
                                    >
                                        <label for="botIdValue" class="form-label"
                                        >Bot ID</label
                                        >
                                        <input
                                                type="text"
                                                class="form-control"
                                                id="botIdValue"
                                                placeholder="Enter Bot ID"
                                        />
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            Apply Settings
                                        </button>
                                    </div>
                                </form>

                                <div class="mt-3">
                                    <div
                                            id="settingResult"
                                            class="alert"
                                            style="display: none"
                                    ></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script th:src="@{/js/admin.js}"></script>
<script th:src="@{/js/settings.js}"></script>
</body>
</html>
