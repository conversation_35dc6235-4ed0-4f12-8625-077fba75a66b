<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
  <head>
    <title>Study Settings</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css"
    />
    <link rel="stylesheet" th:href="@{/css/admin.css}" />
    <link rel="stylesheet" th:href="@{/css/study_settings.css}" />
  </head>
  <body>
    <div class="admin-wrapper">
      <!-- Sidebar -->
      <div th:replace="admin/fragments/sidebar :: sidebar('study_settings')"></div>

      <!-- Main Content -->
      <div class="main-content">
        <nav class="top-navbar">
          <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
              <button id="sidebar-toggle" class="btn">
                <i class="bi bi-list"></i>
              </button>
              <div class="admin-profile">
                <div class="dropdown">
                  <button
                    class="btn dropdown-toggle"
                    type="button"
                    id="profileDropdown"
                    data-bs-toggle="dropdown"
                  >
                    <i class="bi bi-person-circle"></i>
                    <span>Admin</span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                      <a class="dropdown-item" href="#"
                        ><i class="bi bi-person"></i> Profile</a
                      >
                    </li>
                    <li>
                      <a class="dropdown-item" href="#"
                        ><i class="bi bi-box-arrow-right"></i> Logout</a
                      >
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </nav>

        <div class="content-wrapper">
          <div class="container-fluid">
            <div class="row">
              <div class="col-12">
                <div class="card study-settings-card">
                  <div class="card-header">
                    <h5 class="card-title">
                      <i class="bi bi-book me-2"></i>Study Settings - Silence Threshold Configuration
                    </h5>
                    <p class="card-subtitle text-muted mb-0">
                      Configure silence threshold for bot ID
                    </p>
                  </div>
                  <div class="card-body">
                    <!-- Silence Threshold Configuration Form -->
                    <form id="studySettingsForm">
                      <div class="row">
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label for="botId" class="form-label">
                              <i class="bi bi-cpu me-1"></i>Bot ID
                            </label>
                            <input
                              type="text"
                              class="form-control"
                              id="botId"
                              placeholder="Enter Bot ID"
                              required
                            />
                            <div class="form-text">
                              The bot ID to configure silence threshold for
                            </div>
                          </div>
                        </div>
                        <div class="col-md-6">
                          <div class="mb-3">
                            <label for="silenceThreshold" class="form-label">
                              <i class="bi bi-volume-mute me-1"></i>Silence Threshold
                            </label>
                            <input
                              type="number"
                              class="form-control"
                              id="silenceThreshold"
                              placeholder="Enter silence threshold (milliseconds)"
                              min="0"
                              max="10000"
                              required
                            />
                            <div class="form-text">
                              Silence threshold in milliseconds (0-10000)
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="mb-4">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                          <button type="button" class="btn btn-outline-secondary me-md-2" id="resetForm">
                            <i class="bi bi-arrow-clockwise me-1"></i>Reset
                          </button>
                          <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>Update Silence Threshold
                          </button>
                        </div>
                      </div>
                    </form>

                    <!-- Result Message -->
                    <div class="mt-3">
                      <div
                        id="settingResult"
                        class="alert"
                        style="display: none"
                      ></div>
                    </div>

                    <!-- Current Configuration Display -->
                    <div class="mt-4">
                      <div class="card bg-light">
                        <div class="card-header">
                          <h6 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>Current Configuration
                          </h6>
                        </div>
                        <div class="card-body">
                          <div class="row" id="currentConfig">
                            <div class="col-md-6">
                              <div class="config-item">
                                <strong>Bot ID:</strong>
                                <span id="lastBotId" class="text-muted">Not configured</span>
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="config-item">
                                <strong>Silence Threshold:</strong>
                                <span id="lastSilenceThreshold" class="text-muted">Not configured</span>
                              </div>
                            </div>
                          </div>
                          <div class="row mt-2">
                            <div class="col-12">
                              <div class="config-item">
                                <strong>Last Update:</strong>
                                <span id="lastUpdateTime" class="text-muted">Never updated</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script th:src="@{/js/admin.js}"></script>
    <script th:src="@{/js/study_settings.js}"></script>
  </body>
</html>
