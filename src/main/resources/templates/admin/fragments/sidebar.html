<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<body>
<div th:fragment="sidebar (active)" class="sidebar">
    <div class="sidebar-header">
        <h4><i class="bi bi-speedometer2"></i> PIKA ROBOT</h4>
    </div>
    <ul class="sidebar-menu">
        <li>
            <a href="/web/admin/dashboard" class="menu-item" th:classappend="${active} == 'dashboard' ? ' active'">
                <i class="bi bi-house-door"></i>
                <span>Dashboard</span>
            </a>
        </li>
        <li>
            <a href="/web/admin/conversations" class="menu-item" th:classappend="${active} == 'conversations' ? ' active'">
                <i class="bi bi-chat-dots"></i>
                <span>Conversations</span>
            </a>
        </li>
        <li>
            <a href="/web/admin/metrics" class="menu-item" th:classappend="${active} == 'metrics' ? ' active'">
                <i class="bi bi-graph-up"></i>
                <span>Monitor response time</span>
            </a>
        </li>
        <li>
            <a href="/web/admin/face_demo" class="menu-item" th:classappend="${active} == 'face_demo' ? ' active'">
                <i class="bi bi-upload"></i>
                <span>Upload file</span>
            </a>
        </li>
        <li>
            <a href="/web/admin/log_robot" class="menu-item" th:classappend="${active} == 'log_robot' ? ' active'">
                <i class="bi bi-clipboard-data"></i>
                <span>Robot Logs</span>
            </a>
        </li>
        <li>
            <a href="/web/admin/settings" class="menu-item" th:classappend="${active} == 'settings' ? ' active'">
                <i class="bi bi-gear"></i>
                <span>Robot Settings</span>
            </a>
        </li>
        <li>
            <a href="/web/admin/study_settings" class="menu-item" th:classappend="${active} == 'study_settings' ? ' active'">
                <i class="bi bi-book"></i>
                <span>Study Settings</span>
            </a>
        </li>
    </ul>
</div>
</body>
</html>


