<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Robot Metrics</title>
    <link
            href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
            rel="stylesheet"
    />
    <link
            rel="stylesheet"
            href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css"
    />
    <link rel="stylesheet" th:href="@{/css/admin.css}"/>
</head>
<body>
<div class="admin-wrapper">
    <!-- Sidebar -->
    <div th:replace="admin/fragments/sidebar :: sidebar('metrics')"></div>

    <!-- Main Content -->
    <div class="main-content">
        <nav class="top-navbar">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center">
                    <button id="sidebar-toggle" class="btn">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="admin-profile">
                        <div class="dropdown">
                            <button
                                    class="btn dropdown-toggle"
                                    type="button"
                                    id="profileDropdown"
                                    data-bs-toggle="dropdown"
                            >
                                <i class="bi bi-person-circle"></i>
                                <span>Admin</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="#"
                                    ><i class="bi bi-person"></i> Profile</a
                                    >
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#"
                                    ><i class="bi bi-box-arrow-right"></i> Logout</a
                                    >
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
            <div class="container-fluid">
                <h2>Robot Metrics Dashboard</h2>

                <!-- Metrics Navigation Blocks -->
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card metric-card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-clock-history"></i> Backend Response Time
                                </h5>
                                <p class="card-text">
                                    Track and analyze response times from server backend.
                                </p>
                                <div class="mt-3">
                                    <a
                                            href="/web/admin/metrics/sentry"
                                            class="btn btn-primary"
                                    >View Details</a
                                    >
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card metric-card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-chat-dots"></i> Robot logs
                                </h5>
                                <p class="card-text">
                                    Monitor statistics from robot conversation.
                                </p>
                                <div class="mt-3">
                                    <a
                                            href="/web/admin/metrics/robot"
                                            class="btn btn-primary"
                                    >View Details</a
                                    >
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card metric-card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-gear"></i> System Performance
                                </h5>
                                <p class="card-text">
                                    Monitor system resources, error rates, and overall
                                    performance metrics.
                                </p>
                                <div class="mt-3">
                                    <a
                                            href="/web/admin/metrics/system"
                                            class="btn btn-primary"
                                    >View Details</a
                                    >
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script th:src="@{/js/admin.js}"></script>
</body>
</html>
