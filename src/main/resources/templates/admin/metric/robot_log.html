<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Robot Logs Analysis</title>
    <link
            href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
            rel="stylesheet"
    />
    <link
            rel="stylesheet"
            href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css"
    />
    <link rel="stylesheet" th:href="@{/css/admin.css}"/>
    <style>
        .loading {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 5px;
            z-index: 1000;
        }
    </style>
</head>
<body>
<div class="admin-wrapper">
    <!-- Sidebar -->
    <div th:replace="admin/fragments/sidebar :: sidebar('metrics')"></div>

    <!-- Main Content -->
    <div class="main-content">
        <nav class="top-navbar">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center">
                    <button id="sidebar-toggle" class="btn">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="admin-profile">
                        <div class="dropdown">
                            <button
                                    class="btn dropdown-toggle"
                                    type="button"
                                    id="profileDropdown"
                                    data-bs-toggle="dropdown"
                            >
                                <i class="bi bi-person-circle"></i>
                                <span>Admin</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="#"
                                    ><i class="bi bi-person"></i> Profile</a
                                    >
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#"
                                    ><i class="bi bi-box-arrow-right"></i> Logout</a
                                    >
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
            <div class="container-fluid">
                <h2>Conversation Logs Analysis</h2>

                <div class="table-responsive mt-4">
                    <table class="table table-striped" id="resultsTable">
                        <thead>
                        <tr>
                            <th>Conversation ID</th>
                            <th>Robot ID</th>
                            <th>Loop #</th>
                            <th>Mic Duration (ms)</th>
                            <th>End to Stalling (ms)</th>
                            <th>End to Chat (ms)</th>
                        </tr>
                        </thead>
                        <tbody id="tableBody"></tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between mt-3">
                    <button class="btn btn-primary" id="prevPage" disabled>
                        Previous
                    </button>
                    <span id="pageInfo">Page 1</span>
                    <button class="btn btn-primary" id="nextPage">Next</button>
                </div>

                <button class="btn btn-success mt-3" id="exportBtn">
                    Export to Excel
                </button>
            </div>
        </div>
    </div>
</div>

<div class="loading" id="loadingSpinner">Loading...</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/xlsx/dist/xlsx.full.min.js"></script>
<script th:src="@{/js/admin.js}"></script>
<script>
    const API_BASE_URL = "/robot/api/v1/admin";
    let currentPage = 0;
    let totalPages = 1;

    function showLoading() {
        document.getElementById("loadingSpinner").style.display = "block";
    }

    function hideLoading() {
        document.getElementById("loadingSpinner").style.display = "none";
    }

    function processLogData(logs) {
        let loops = [];
        let currentLoop = {};

        logs.forEach((logEntry) => {
            const timestamp = parseInt(logEntry.match(/\[(\d+)\]/)[1]);

            if (logEntry.includes("CHAT_RESPONSE finish")) {
                currentLoop.startMic = timestamp;
            } else if (logEntry.includes("is_stop: true")) {
                currentLoop.endMic = timestamp;
            } else if (
                logEntry.includes("CHAT_STALLING") &&
                !logEntry.includes("finish")
            ) {
                currentLoop.stallingResponse = timestamp;
            } else if (
                logEntry.includes("CHAT_RESPONSE") &&
                !logEntry.includes("finish")
            ) {
                currentLoop.chatResponse = timestamp;

                if (Object.keys(currentLoop).length === 4) {
                    loops.push({...currentLoop});
                    currentLoop = {};
                }
            }
        });

        return loops.map((loop) => ({
            micDuration: loop.endMic - loop.startMic,
            endToStalling: loop.stallingResponse - loop.endMic,
            endToChat: loop.chatResponse - loop.endMic,
        }));
    }

    function loadData(page) {
        showLoading();
        fetch(`${API_BASE_URL}/robot_log?page=${page}`)
            .then((response) => response.json())
            .then((response) => {
                if (response.status !== 200) {
                    throw new Error(response.message);
                }
                const data = response.data;
                const tableBody = document.getElementById("tableBody");
                tableBody.innerHTML = "";

                data.content.forEach((conversation) => {
                    try {
                        const results = processLogData(conversation.log);

                        results.forEach((result, index) => {
                            const tr = document.createElement("tr");
                            tr.innerHTML = `
                    <td>${conversation.conversationId}</td>
                    <td>${conversation.robotId}</td>
                    <td>${index + 1}</td>
                    <td>${result.micDuration}</td>
                    <td>${result.endToStalling}</td>
                    <td>${result.endToChat}</td>
                  `;
                            tableBody.appendChild(tr);
                        });
                    } catch (error) {
                        console.error(
                            `Error processing log for conversation ${conversation.conversationId}:`,
                            error
                        );
                    }
                });

                currentPage = data.number;
                totalPages = data.totalPages;

                document.getElementById("prevPage").disabled = currentPage === 0;
                document.getElementById("nextPage").disabled =
                    currentPage === totalPages - 1;
                document.getElementById("pageInfo").textContent = `Page ${
                    currentPage + 1
                } of ${totalPages}`;
            })
            .catch((error) => {
                console.error("Error loading data:", error);
                alert("Error loading data. Please check console for details.");
            })
            .finally(() => {
                hideLoading();
            });
    }

    function exportToExcel() {
        const table = document.getElementById("resultsTable");
        const wb = XLSX.utils.table_to_book(table);
        XLSX.writeFile(wb, `conversation_logs_page_${currentPage + 1}.xlsx`);
    }

    // Event Listeners
    document.getElementById("prevPage").addEventListener("click", () => {
        if (currentPage > 0) loadData(currentPage - 1);
    });

    document.getElementById("nextPage").addEventListener("click", () => {
        if (currentPage < totalPages - 1) loadData(currentPage + 1);
    });

    document
        .getElementById("exportBtn")
        .addEventListener("click", exportToExcel);

    // Load initial data
    loadData(0);
</script>
</body>
</html>
