<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Robot Metrics</title>
    <link
            href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
            rel="stylesheet"
    />
    <link
            rel="stylesheet"
            href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css"
    />
    <link rel="stylesheet" th:href="@{/css/admin.css}"/>
</head>
<body>
<div class="admin-wrapper">
    <!-- Sidebar -->
    <div th:replace="admin/fragments/sidebar :: sidebar('metrics')"></div>

    <!-- Main Content -->
    <div class="main-content">
        <nav class="top-navbar">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center">
                    <button id="sidebar-toggle" class="btn">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="admin-profile">
                        <div class="dropdown">
                            <button
                                    class="btn dropdown-toggle"
                                    type="button"
                                    id="profileDropdown"
                                    data-bs-toggle="dropdown"
                            >
                                <i class="bi bi-person-circle"></i>
                                <span>Admin</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li>
                                    <a class="dropdown-item" href="#"
                                    ><i class="bi bi-person"></i> Profile</a
                                    >
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#"
                                    ><i class="bi bi-box-arrow-right"></i> Logout</a
                                    >
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <div class="content-wrapper">
            <div class="container-fluid">
                <h2>Robot Metrics</h2>
                <div class="d-flex justify-content-end mb-3">
                    <button
                            class="btn btn-sm btn-outline-secondary toggle-llm-column"
                            title="Toggle LLM Conversation ID"
                    >
                        <i class="bi bi-eye-slash"></i> LLM Conversation ID
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th>Conversation ID</th>
                            <th>Response Time</th>
                            <th>Stalling</th>
                            <th>Upload audio</th>
                            <th>Bot LLM Time</th>
                            <th>TTS Time</th>
                            <th>Animations Time</th>
                            <th>Timestamp</th>
                            <th class="llm-conversation-column">LLM Conversation ID</th>
                        </tr>
                        </thead>
                        <tbody id="metricsTableBody"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script th:src="@{/js/admin.js}"></script>
<script th:src="@{/js/sentry.js}"></script>
</body>
</html>
