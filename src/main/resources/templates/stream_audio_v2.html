<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Speech to Text Conversation</title>
    <style>
        #status {
            font-weight: bold;
            color: green;
        }

        #mediaContainer {
            margin: 10px 0;
            max-width: 100%;
        }

        #mediaContainer img,
        #mediaContainer video {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
<h1>Speech to Text Conversation</h1>
<div style="margin-bottom: 20px;">
    <label for="botIdInput">Bot ID:</label>
    <input type="text" id="botIdInput" value="446" style="margin-left: 10px; padding: 5px; width: 100px;">
</div>
<button id="startConversationButton">💬 Start Conversation</button>
<button id="skipButton" disabled>⏭ SKIP</button>
<p id="status">Status: Idle</p>
<p id="transcript">Transcript will appear here...</p>
<p id="response">Response will appear here...</p>
<div id="mediaContainer"></div>
<audio id="audioPlayback" controls style="display: none"></audio>

<script>
    const startConversationButton = document.getElementById(
        "startConversationButton"
    );
    const skipButton = document.getElementById("skipButton");
    const status = document.getElementById("status");
    const transcript = document.getElementById("transcript");
    const response = document.getElementById("response");

    let socket;
    let audioContext;
    let source;
    let processor;
    let mediaStream;
    const SAMPLE_RATE = 16000; // 16,000 Hz sample rate
    const BYTES_PER_SAMPLE = 2; // 16-bit integer = 2 bytes per sample
    const SAMPLES_PER_BUFFER = 4096;
    const BYTES_PER_BUFFER = SAMPLES_PER_BUFFER * BYTES_PER_SAMPLE;
    let isRecording = false;
    let currentAudioPromise = Promise.resolve(); // Track current audio playback
    let skipButtonPressed = false; // Track if skip button has been pressed during current recording

    startConversationButton.addEventListener("click", () => {
        if (startConversationButton.textContent === "💬 Start Conversation") {
            startConversation();
            startConversationButton.textContent = "⏹ Stop Conversation";
        } else {
            stopConversation();
            startConversationButton.textContent = "💬 Start Conversation";
        }
    });

    skipButton.addEventListener("click", () => {
        if (
            socket &&
            socket.readyState === WebSocket.OPEN &&
            !skipButtonPressed
        ) {
            socket.send(JSON.stringify({type: "SKIP"}));
            console.log("Send skip signal")
            skipButtonPressed = true;
            skipButton.disabled = true;
        }
    });

    function getWebSocketUrl() {
        const protocol =
            window.location.protocol === "https:" ? "wss://" : "ws://";
        const host = window.location.host; // This will give you the host (domain + port if present)
        const botId = document.getElementById("botIdInput").value;
        return `${protocol}${host}/ws/free_talk?audio_format=pcm&robot_id=testWs&bot_id=${botId}`; // Adjust the path if necessary
    }

    function startConversation() {
        if (!socket || socket.readyState !== WebSocket.OPEN) {
            let socketUrl = getWebSocketUrl();
            console.log("WebSocket URL:", socketUrl);
            socket = new WebSocket(socketUrl);

            console.log("WebSocket connection opened");

            socket.binaryType = "arraybuffer"; // Expect binary data

            socket.onopen = function () {
                console.log("WebSocket connected");
                //startRecording(); // Start recording when WebSocket is open
            };

            socket.onmessage = async function (event) {
                const responseMessage = JSON.parse(event.data);
                console.log(responseMessage);
                if (
                    responseMessage.type === "ASR" &&
                    responseMessage.data.transcript
                ) {
                    transcript.innerText = responseMessage.data.transcript;
                    if (responseMessage.data.is_stop) {
                        console.log("Received is_stop = true. Pausing recording...");
                        pauseRecording();
                        status.textContent = "Status: Waiting for response";
                        status.style.color = "red";
                    }
                } else if (
                    responseMessage.type === "CHAT_RESPONSE" ||
                    responseMessage.type === "CHAT_STALLING"
                ) {
                    // console.log(
                    //   `${responseMessage.type} received.`,
                    //   JSON.stringify(responseMessage)
                    // );
                    // Handle both CHAT_RESPONSE and CHAT_STALLING the same way for playback
                    await renderMessagesSequentially(responseMessage.data.messages);

                    // Only restart recording after CHAT_RESPONSE messages are fully played
                    if (
                        responseMessage.type === "CHAT_RESPONSE" &&
                        !responseMessage.data.has_next_message
                    ) {
                        startRecording();
                    }
                }
            };

            socket.onclose = function (event) {
                console.log(
                    "WebSocket closed with code:",
                    event.code,
                    "and reason:",
                    event.reason
                );
            };

            socket.onerror = function (error) {
                console.error("WebSocket error:", error);
            };
        }
    }

    // Function to render messages and play audio sequentially
    async function renderMessagesSequentially(messages) {
        const mediaContainer = document.getElementById("mediaContainer");

        for (const message of messages) {
            if (message.text) {
                response.innerText = message.text;
            }

            // Handle media content if present
            if (message.media) {
                mediaContainer.innerHTML = ""; // Clear previous media
                if (message.media.type === "IMAGE") {
                    const img = document.createElement("img");
                    img.src = message.media.url;
                    img.alt = "Response Image";
                    mediaContainer.appendChild(img);
                } else if (message.media.type === "VIDEO") {
                    const video = document.createElement("video");
                    video.src = message.media.url;
                    video.controls = true;
                    video.style.maxWidth = "100%";
                    mediaContainer.appendChild(video);
                }
            } else {
                mediaContainer.innerHTML = ""; // Clear media if none present
            }

            if (message.audio) {
                await playAudio(message.audio);
            }
        }
    }

    // Function to play audio and return a Promise that resolves when playback finishes
    function playAudio(audioUrl) {
        // Wait for any current audio to finish before starting new audio
        currentAudioPromise = currentAudioPromise.then(() => {
            return new Promise((resolve) => {
                const audioPlayback = document.getElementById("audioPlayback");
                audioPlayback.src = audioUrl;
                audioPlayback.style.display = "none";

                // Ensure any existing onended handlers are removed
                audioPlayback.onended = null;

                audioPlayback.play();
                audioPlayback.onended = function () {
                    resolve();
                };

                // Also handle cases where audio fails to play
                audioPlayback.onerror = function () {
                    console.error("Audio playback error");
                    resolve();
                };
            });
        });

        return currentAudioPromise;
    }

    function startRecording() {
        if (isRecording) return;

        navigator.mediaDevices
            .getUserMedia({audio: true})
            .then((stream) => {
                mediaStream = stream;
                audioContext = new AudioContext({sampleRate: SAMPLE_RATE});
                source = audioContext.createMediaStreamSource(stream);
                processor = audioContext.createScriptProcessor(
                    SAMPLES_PER_BUFFER,
                    1,
                    1
                );

                processor.onaudioprocess = function (e) {
                    const inputData = e.inputBuffer.getChannelData(0);
                    // Convert Float32Array to Int16Array
                    const int16Data = new Int16Array(inputData.length);
                    for (let i = 0; i < inputData.length; i++) {
                        // Convert float [-1.0, 1.0] to int16 [-32768, 32767]
                        const s = Math.max(-1, Math.min(1, inputData[i]));
                        int16Data[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
                    }
                    if (socket && socket.readyState === WebSocket.OPEN) {
                        socket.send(int16Data.buffer);
                        console.log(
                            "Sent audio data of size:",
                            int16Data.buffer.byteLength
                        );
                    }
                };

                source.connect(processor);
                processor.connect(audioContext.destination);

                isRecording = true;
                skipButtonPressed = false; // Reset skip button state
                skipButton.disabled = false; // Enable skip button
                status.textContent = "Status: Recording..."; // Update status
                status.style.color = "green"; // Set the text color to green
                console.log("Recording started");
            })
            .catch((error) => {
                console.error("Error accessing media devices.", error);
            });
    }

    function pauseRecording() {
        // Stop recording but keep the conversation alive
        if (processor) {
            processor.disconnect();
        }
        if (source) {
            source.disconnect();
        }
        if (audioContext) {
            audioContext.suspend(); // Pause the audio context
        }

        isRecording = false;
        skipButton.disabled = true; // Disable skip button when recording pauses
        status.textContent = "Status: Waiting for response"; // Update status
        status.style.color = "red"; // Set the text color to red
        console.log("Recording paused");
    }

    function stopConversation() {
        // Stop the audio context, processor, and media stream tracks
        if (processor) {
            processor.disconnect();
        }
        if (source) {
            source.disconnect();
        }
        if (audioContext) {
            audioContext.close();
        }
        if (mediaStream) {
            mediaStream.getTracks().forEach((track) => track.stop());
        }

        // If there is an active WebSocket connection, close it
        if (socket && socket.readyState === WebSocket.OPEN) {
            socket.close();
            console.log("WebSocket connection closed.");
        }

        isRecording = false;
        status.textContent = "Status: Idle"; // Update status
    }
</script>
</body>
</html>
