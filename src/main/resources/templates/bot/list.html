<!DOCTYPE html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> <PERSON> AI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/css/bot-list.css" rel="stylesheet">
    <style>
        .bot-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .bot-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .bot-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }
        
        .bot-body {
            padding: 1.5rem;
        }
        
        .bot-name {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }
        
        .bot-description {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .bot-status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-loading {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-ready {
            background-color: #d1edff;
            color: #0c5460;
        }
        
        .search-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .search-input {
            border: none;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .search-input:focus {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            outline: none;
        }
        
        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .no-results {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            border: 1px solid #f5c6cb;
        }
        
        /* Button container styling */
        .action-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .action-buttons .btn {
            white-space: nowrap;
            min-width: fit-content;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">Danh sách Bot AI</h1>
                <a href="/" class="btn btn-outline-secondary">Quay lại</a>
            </div>
        </div>

        <!-- Filter by name (approximate match) -->
        <div class="row mb-3">
            <div class="col-12">
                <form method="get" action="/bot-view/list" class="row g-2">
                    <div class="col-md-6 col-lg-4">
                        <input type="text"
                               class="form-control"
                               name="search"
                               th:value="${search}"
                               placeholder="Tìm theo tên bot (gần đúng)">
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-primary">Lọc</button>
                    </div>
                    <div class="col-auto" th:if="${search}">
                        <a href="/bot-view/list" class="btn btn-outline-secondary">Xóa bộ lọc</a>
                    </div>
                </form>
            </div>
        </div>

        <div th:if="${error != null}" class="row">
            <div class="col-12">
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span th:text="${error}"></span>
                </div>
            </div>
        </div>

        <div th:if="${error == null}" class="row">
            <div class="col-12">
                <div th:if="${#lists.isEmpty(bots)}" class="no-results">
                    <h4 class="mt-3">Không có dữ liệu</h4>
                </div>

                <div th:if="${!#lists.isEmpty(bots)}" class="card">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 100px;">ID</th>
                                        <th>Tên bot</th>
                                        <th>Mô tả</th>
                                        <th style="width: 240px;">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="bot : ${bots}">
                                        <td th:text="${bot.id}">1</td>
                                        <td th:text="${bot.name}">Tên bot</td>
                                        <td th:text="${bot.description}">Mô tả bot</td>
                                        <td>
                                            <div class="action-buttons">
                                                <button type="button"
                                                        class="btn btn-sm btn-success play-btn"
                                                        th:attr="data-bot-id=${bot.id}">
                                                    <i class="fas fa-play me-1"></i> Chạy full intent
                                                </button>
                                                <button type="button"
                                                        class="btn btn-sm btn-warning random-play-btn"
                                                        th:attr="data-bot-id=${bot.id}">
                                                    <i class="fas fa-random me-1"></i> Chạy 1 lần random
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Regular play button handler
            var buttons = document.querySelectorAll('.play-btn');
            buttons.forEach(function (btn) {
                btn.addEventListener('click', function () {
                    var botId = this.getAttribute('data-bot-id');
                    var originalHtml = this.innerHTML;
                    this.disabled = true;
                    this.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Đang chạy';
                    fetch('/robot/api/v1/communication/execute?bot_id=' + encodeURIComponent(botId), {
                        method: 'GET'
                    }).then(function (res) {
                        return res.json().catch(function () { return res.text(); });
                    }).then(function (data) {
                        if (typeof data === 'object' && data && data.status === 0) {
                            alert('Đã bắt đầu chạy bot_id=' + botId);
                        } else {
                            alert('Phản hồi: ' + (typeof data === 'string' ? data : JSON.stringify(data)));
                        }
                    }).catch(function (err) {
                        alert('Lỗi gọi API: ' + err);
                    }).finally(() => {
                        btn.disabled = false;
                        btn.innerHTML = originalHtml;
                    });
                });
            });

            // Random play button handler
            var randomButtons = document.querySelectorAll('.random-play-btn');
            randomButtons.forEach(function (btn) {
                btn.addEventListener('click', function () {
                    var botId = this.getAttribute('data-bot-id');
                    var originalHtml = this.innerHTML;
                    this.disabled = true;
                    this.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Đang chạy random';
                    fetch('/robot/api/v1/conversations/execute-single-random-test-async?botId=' + encodeURIComponent(botId), {
                        method: 'POST'
                    }).then(function (res) {
                        return res.json().catch(function () { return res.text(); });
                    }).then(function (data) {
                        if (typeof data === 'object' && data && data.status === 0) {
                            alert('Đã bắt đầu chạy random test cho bot_id=' + botId + '. Kết quả sẽ được gửi qua webhook.');
                        } else {
                            alert('Phản hồi: ' + (typeof data === 'string' ? data : JSON.stringify(data)));
                        }
                    }).catch(function (err) {
                        alert('Lỗi gọi API: ' + err);
                    }).finally(() => {
                        btn.disabled = false;
                        btn.innerHTML = originalHtml;
                    });
                });
            });
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/bot-list.js"></script>
</body>
</html>
