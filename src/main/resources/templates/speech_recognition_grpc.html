<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Real-time Speech Recognition</title>
    <style>
      body {
        font-family: sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        margin: 0;
        background-color: #f4f4f4;
      }
      #controls button {
        padding: 10px 20px;
        font-size: 16px;
        margin: 5px;
        cursor: pointer;
      }
      #transcription {
        margin-top: 20px;
        padding: 15px;
        border: 1px solid #ccc;
        background-color: #fff;
        width: 80%;
        max-width: 600px;
        min-height: 100px;
        white-space: pre-wrap;
      }
      #status {
        margin-top: 10px;
        font-style: italic;
      }
    </style>
  </head>
  <body>
    <h1>Real-time Speech Recognition</h1>

    <div id="controls">
      <button id="recordButton">Start Recording</button>
      <button id="stopButton" disabled>Stop Recording</button>
    </div>

    <div id="transcription">
      <p><em>Transcription will appear here...</em></p>
    </div>
    <div id="status">Not Recording</div>

    <script>
      const audioProcessorUrl = "/js/audio-processor.js";
    </script>
    <script src="/js/speech_recognition_grpc.js"></script>
  </body>
</html>
