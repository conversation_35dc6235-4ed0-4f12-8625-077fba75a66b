-- Migration: Create ShedLock table (PostgreSQL)
-- Kept for future use by a migration/raw SQL runner

CREATE TABLE IF NOT EXISTS shedlock (
    name        VARCHAR(64)  NOT NULL,
    lock_until  TIMESTAMP(3) WITH TIME ZONE NOT NULL,
    locked_at   TIMESTAMP(3) WITH TIME ZONE NOT NULL,
    locked_by   VARCHAR(255) NOT NULL,
    CONSTRAINT shedlock_pkey PRIMARY KEY (name)
);

-- Optional index that can help find expired locks faster
-- CREATE INDEX IF NOT EXISTS idx_shedlock_lock_until ON shedlock(lock_until);


