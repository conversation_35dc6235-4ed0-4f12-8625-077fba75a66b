FROM khiemnd5/spring-eureka-base:v2 AS builder

WORKDIR /app

# Copy pom.xml trước để cache dependencies
COPY pom.xml .

# Download dependencies với cache mount
RUN --mount=type=cache,target=/root/.m2 \
    mvn dependency:go-offline -B

# Copy source code sau
COPY src ./src

# Build application với cache mount
ARG PROFILE
RUN --mount=type=cache,target=/root/.m2 \
    mvn clean install -Dspring-boot.run.profiles=${PROFILE} -DskipTests

# For Java 11, try this
FROM khiemnd5/ffmpeg-java:latest

RUN mkdir /opt/app

# Refer to Maven build -> finalName
COPY --from=builder /app/target/*.jar /opt/app/backend-robot.jar

# java -jar /opt/app/apptofu.jar
ENTRYPOINT ["java", "-jar", "/opt/app/backend-robot.jar"]